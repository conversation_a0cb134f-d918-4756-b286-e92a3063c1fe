/**
 * OneNata Admin 数据模型索引
 * 
 * 这个文件导出所有的数据模型、类型定义和服务类
 * 用于 OneNata 商家管理系统
 */

// 基础模型
export * from './base_model';

// 通用类型和枚举
export * from './types';

// Portal User 相关模型（商家系统用户）
export * from './portal-user';

// Customer 相关模型（来自 OneNata App 的用户和宠物）
export * from './customer';

// Store 相关模型（店铺、产品、库存、服务等）
export * from './store';

// Order 相关模型（订单、跟踪记录、订单商品）
export * from './order';

// Payment 相关模型（支付记录）
export * from './payment';

// Appointment 相关模型（预约系统）
export * from './appointment';

// 默认导出
export default {
  BaseModel: 'BaseModel',
  Types: 'Types',
  PortalUser: 'PortalUser',
  Customer: 'Customer', 
  Store: 'Store',
  Order: 'Order',
  Payment: 'Payment'
}; 