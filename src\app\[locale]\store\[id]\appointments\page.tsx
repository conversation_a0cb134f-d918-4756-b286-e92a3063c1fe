'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/app/[locale]/components/ui/Button';
import { useAuth } from '@/app/lib/firebase/context/AuthContext';
import { Appointment } from '@/app/lib/models/appointment';
import { AppointmentStatus, AppointmentSource } from '@/app/lib/models/types';
import appointmentService from '@/app/lib/services/appointment_service';
import storeService from '@/app/lib/services/store_services';
import { StoreHeader } from '@/app/[locale]/components/ui/StoreHeader';
import { useTranslations } from 'next-intl';
import { 
  FiCalendar, 
  FiClock, 
  FiHeart, 
  FiPlus,
  FiUsers,
  FiCheck,
  FiFilter,
  FiEye,
  FiEdit,
  FiTrash2
} from 'react-icons/fi';
import { getUserStoreRole, UserStoreRole } from '../../../../lib/utils/permissions';
import MonthlyAppointmentCalendar from '@/app/[locale]/components/ui/MonthlyAppointmentCalendar';

const AppointmentsPage = () => {
  const router = useRouter();
  const params = useParams();
  const { userData } = useAuth();
  const t = useTranslations('appointments');
  const storeId = params.id as string;

  // State
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<AppointmentStatus | ''>('');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [isAppointmentEnabled, setIsAppointmentEnabled] = useState(false);
  const [userStoreRole, setUserStoreRole] = useState<UserStoreRole | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [appointmentToDelete, setAppointmentToDelete] = useState<string | null>(null);

  // Statistics
  const [stats, setStats] = useState({
    isEnabled: false,
    totalAppointments: 0,
    upcomingAppointments: 0,
    completedAppointments: 0,
    totalCustomers: 0,
    activeServices: 0,
    staffWithSchedules: 0
  });

  // Load appointments
  const loadAppointments = async () => {
    try {
      setLoading(true);
      
      // 获取用户角色
      if (userData?.uid) {
        const role = await getUserStoreRole(userData.uid, storeId);
        setUserStoreRole(role);
      }
      
      // Check if appointment system is enabled
      const enabledResult = await storeService.isAppointmentEnabled(storeId);
      if (enabledResult.success) {
        setIsAppointmentEnabled(enabledResult.data || false);
      }

      if (!enabledResult.data) {
        setLoading(false);
        return;
      }

      // Load appointments
      const result = await appointmentService.getStoreAppointments(storeId, {
        status: selectedStatus || undefined,
        date: selectedDate || undefined,
        limit: 100
      });

      if (result.success && result.data) {
        let filteredAppointments = result.data;
        
        // 如果是staff，只显示自己的预约
        if (userStoreRole?.isStaff && userData?.uid) {
          filteredAppointments = result.data.filter(appointment => 
            appointment.staffId === userData.uid
          );
        }
        
        setAppointments(filteredAppointments);
      }

      // Load statistics
      const statsResult = await storeService.getAppointmentStats(storeId);
      if (statsResult.success && statsResult.data) {
        setStats(statsResult.data);
      }

    } catch (error) {
      console.error('Error loading appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  // Enable appointment system
  const handleEnableAppointments = async () => {
    if (!userData?.uid) return;

    try {
      const result = await storeService.toggleAppointmentStatus(storeId, true, userData.uid);
      if (result.success) {
        setIsAppointmentEnabled(true);
        loadAppointments();
        alert(t('enableSuccess'));
      } else {
        alert(result.error || t('enableError'));
      }
    } catch (error) {
      console.error('Error enabling appointments:', error);
      alert(t('enableError'));
    }
  };

  // Update appointment status
  const handleUpdateStatus = async (appointmentId: string, newStatus: AppointmentStatus) => {
    if (!userData?.uid) return;

    try {
      const result = await appointmentService.updateAppointmentStatus(
        appointmentId,
        newStatus,
        userData.uid
      );

      if (result.success) {
        loadAppointments();
        alert(t('statusUpdateSuccess'));
      } else {
        alert(result.error || t('statusUpdateError'));
      }
    } catch (error) {
      console.error('Error updating appointment status:', error);
      alert(t('statusUpdateError'));
    }
  };

  // Delete appointment
  const handleDeleteAppointment = async () => {
    if (!userData?.uid || !appointmentToDelete) return;

    try {
      const result = await appointmentService.deleteAppointment(
        appointmentToDelete,
        userData.uid
      );

      if (result.success) {
        loadAppointments();
        alert(t('deleteSuccess'));
        setDeleteModalOpen(false);
        setAppointmentToDelete(null);
      } else {
        alert(result.error || t('deleteError'));
      }
    } catch (error) {
      console.error('Error deleting appointment:', error);
      alert(t('deleteError'));
    }
  };

  // Open delete confirmation modal
  const openDeleteModal = (appointmentId: string) => {
    setAppointmentToDelete(appointmentId);
    setDeleteModalOpen(true);
  };

  // Close delete confirmation modal
  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
    setAppointmentToDelete(null);
  };

  // Get status badge color
  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.CONFIRMED:
        return 'bg-green-100 text-green-800';
      case AppointmentStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800';
      case AppointmentStatus.COMPLETED:
        return 'bg-gray-100 text-gray-800';
      case AppointmentStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      case AppointmentStatus.NO_SHOW:
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  // Get status text
  const getStatusText = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.DRAFT:
        return t('status.draft');
      case AppointmentStatus.CONFIRMED:
        return t('status.confirmed');
      case AppointmentStatus.IN_PROGRESS:
        return t('status.inProgress');
      case AppointmentStatus.COMPLETED:
        return t('status.completed');
      case AppointmentStatus.CANCELLED:
        return t('status.cancelled');
      case AppointmentStatus.NO_SHOW:
        return t('status.noShow');
      default:
        return t('status.unknown');
    }
  };

  // Format date and time
  const formatDateTime = (date: string, time: string) => {
    const dateObj = new Date(`${date} ${time}`);
    return dateObj.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Effects
  useEffect(() => {
    if (storeId) {
      loadAppointments();
    }
  }, [storeId, selectedStatus, selectedDate]);

  // 检查是否可以创建预约
  const canCreateAppointment = userStoreRole?.isOwner || userStoreRole?.isAdmin || false;

  // 检查是否可以管理预约系统
  // const canManageAppointmentSystem = userStoreRole?.isOwner || userStoreRole?.isAdmin || false;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  // If appointment system is not enabled
  if (!isAppointmentEnabled) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
            <div className="mb-6">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <FiCalendar className="w-12 h-12 text-blue-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">{t('notEnabled.title')}</h2>
              <p className="text-gray-600 mb-6">{t('notEnabled.description')}</p>
            </div>

            <div className="bg-gray-50 rounded-xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('notEnabled.features.title')}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <div className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-600 mr-3" />
                  <span>{t('notEnabled.features.onlineBooking')}</span>
                </div>
                <div className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-600 mr-3" />
                  <span>{t('notEnabled.features.staffSchedule')}</span>
                </div>
                <div className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-600 mr-3" />
                  <span>{t('notEnabled.features.customerManagement')}</span>
                </div>
                <div className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-600 mr-3" />
                  <span>{t('notEnabled.features.serviceTime')}</span>
                </div>
                <div className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-600 mr-3" />
                  <span>{t('notEnabled.features.autoReminders')}</span>
                </div>
                <div className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-600 mr-3" />
                  <span>{t('notEnabled.features.onenataIntegration')}</span>
                </div>
              </div>
            </div>

            <Button
              onClick={handleEnableAppointments}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3 text-lg rounded-xl"
            >
              {t('notEnabled.enableButton')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <FiCalendar className="w-8 h-8 mr-3 text-blue-600" />
                {t('title')}
              </h1>
              <p className="text-gray-600 mt-2">{t('subtitle')}</p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => router.push(`/store/${storeId}/customers`)}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl"
              >
                <FiUsers className="w-4 h-4 mr-2" />
                {t('actions.manageCustomers')}
              </Button>
              <Button
                onClick={() => router.push(`/store/${storeId}/appointments/create`)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-xl"
                disabled={!canCreateAppointment}
              >
                <FiPlus className="w-4 h-4 mr-2" />
                {t('actions.createAppointment')}
              </Button>
            </div>
          </div>
          {!canCreateAppointment && userStoreRole?.isStaff && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-blue-700 text-sm">
                {t('staffPermissionNote')}
              </p>
            </div>
          )}

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                  <FiCalendar className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('stats.totalAppointments')}</h3>
                  <p className="text-3xl font-bold text-blue-600 mt-1">{stats.totalAppointments}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                  <FiClock className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('stats.upcoming')}</h3>
                  <p className="text-3xl font-bold text-orange-600 mt-1">{stats.upcomingAppointments}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                  <FiCheck className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('stats.completed')}</h3>
                  <p className="text-3xl font-bold text-green-600 mt-1">{stats.completedAppointments}</p>
                </div>
              </div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                  <FiHeart className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{t('stats.activeServices')}</h3>
                  <p className="text-3xl font-bold text-purple-600 mt-1">{stats.activeServices}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-100 mb-6">
            <div className="flex items-center mb-4">
              <FiFilter className="w-5 h-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">{t('filters.title')}</h3>
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="md:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('filters.status')}</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value as AppointmentStatus)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">{t('filters.allStatuses')}</option>
                  <option value={AppointmentStatus.DRAFT}>{t('status.draft')}</option>
                  <option value={AppointmentStatus.CONFIRMED}>{t('status.confirmed')}</option>
                  <option value={AppointmentStatus.IN_PROGRESS}>{t('status.inProgress')}</option>
                  <option value={AppointmentStatus.COMPLETED}>{t('status.completed')}</option>
                  <option value={AppointmentStatus.CANCELLED}>{t('status.cancelled')}</option>
                  <option value={AppointmentStatus.NO_SHOW}>{t('status.noShow')}</option>
                </select>
              </div>
              <div className="md:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-2">{t('filters.date')}</label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Monthly Calendar View */}
        <MonthlyAppointmentCalendar
          appointments={appointments}
          storeId={storeId}
          canCreateAppointment={canCreateAppointment}
          onUpdateStatus={handleUpdateStatus}
          onDeleteAppointment={(appointmentId: string) => openDeleteModal(appointmentId)}
          selectedDate={selectedDate}
          selectedStatus={selectedStatus}
        />

        {/* Delete Confirmation Modal */}
        {deleteModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {t('deleteConfirm.title')}
              </h3>
              <p className="text-gray-600 mb-6">
                {t('deleteConfirm.message')}
              </p>
              <div className="flex gap-3 justify-end">
                <Button
                  onClick={closeDeleteModal}
                  className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg"
                >
                  {t('deleteConfirm.cancel')}
                </Button>
                <Button
                  onClick={handleDeleteAppointment}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg"
                >
                  {t('deleteConfirm.confirm')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppointmentsPage; 