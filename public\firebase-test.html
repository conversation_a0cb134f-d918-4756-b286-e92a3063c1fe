<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase 模拟器测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #FDECCE, #F2D3A4);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #A126FF;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #A126FF;
            border-bottom: 2px solid #F2D3A4;
            padding-bottom: 10px;
        }
        .service-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #F2D3A4;
            border-radius: 10px;
        }
        .button {
            background: linear-gradient(135deg, #A126FF, #C084FC);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: scale(1.05);
        }
        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .log {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #F2D3A4;
            border-radius: 5px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase 模拟器测试中心</h1>
        
        <div class="service-section">
            <h2>📊 模拟器状态</h2>
            <div id="emulator-status">
                <p>正在检查模拟器状态...</p>
            </div>
            <button class="button" onclick="checkAllEmulators()">刷新状态</button>
            <button class="button" onclick="window.open('http://localhost:4000', '_blank')">打开模拟器 UI</button>
        </div>
    </div>

    <div class="grid">
        <!-- Auth 测试 -->
        <div class="container">
            <h2>🔐 Authentication 测试</h2>
            <div class="service-section">
                <h3>用户注册</h3>
                <input type="email" id="signup-email" placeholder="邮箱">
                <input type="password" id="signup-password" placeholder="密码">
                <input type="text" id="signup-name" placeholder="显示名称">
                <button class="button" onclick="testSignup()">注册用户</button>
            </div>
            
            <div class="service-section">
                <h3>用户登录</h3>
                <input type="email" id="login-email" placeholder="邮箱">
                <input type="password" id="login-password" placeholder="密码">
                <button class="button" onclick="testLogin()">登录</button>
                <button class="button" onclick="testLogout()">登出</button>
            </div>
        </div>

        <!-- Firestore 测试 -->
        <div class="container">
            <h2>🗄️ Firestore 测试</h2>
            <div class="service-section">
                <h3>文档操作</h3>
                <input type="text" id="doc-collection" placeholder="集合名称" value="test-collection">
                <input type="text" id="doc-id" placeholder="文档 ID" value="test-doc">
                <textarea id="doc-data" placeholder="文档数据 (JSON)" rows="3">{"name": "测试文档", "timestamp": "2024-01-01"}</textarea>
                <div>
                    <button class="button" onclick="testFirestoreWrite()">写入文档</button>
                    <button class="button" onclick="testFirestoreRead()">读取文档</button>
                    <button class="button" onclick="testFirestoreDelete()">删除文档</button>
                </div>
            </div>
        </div>

        <!-- Storage 测试 -->
        <div class="container">
            <h2>📁 Storage 测试</h2>
            <div class="service-section">
                <h3>文件操作</h3>
                <input type="file" id="file-input" accept="*/*">
                <input type="text" id="file-path" placeholder="存储路径" value="test-files/sample.txt">
                <div>
                    <button class="button" onclick="testStorageUpload()">上传文件</button>
                    <button class="button" onclick="testStorageDownload()">获取下载链接</button>
                    <button class="button" onclick="testStorageDelete()">删除文件</button>
                </div>
            </div>
        </div>

        <!-- Realtime Database 测试 -->
        <div class="container">
            <h2>⚡ Realtime Database 测试</h2>
            <div class="service-section">
                <h3>数据操作</h3>
                <input type="text" id="db-path" placeholder="数据路径" value="test-data/sample">
                <textarea id="db-data" placeholder="数据内容 (JSON)" rows="3">{"message": "Hello Database", "timestamp": "2024-01-01"}</textarea>
                <div>
                    <button class="button" onclick="testDatabaseWrite()">写入数据</button>
                    <button class="button" onclick="testDatabaseRead()">读取数据</button>
                    <button class="button" onclick="testDatabaseDelete()">删除数据</button>
                </div>
            </div>
        </div>

        <!-- Functions 测试 -->
        <div class="container">
            <h2>⚙️ Functions 测试</h2>
            <div class="service-section">
                <h3>函数调用</h3>
                <input type="text" id="function-name" placeholder="函数名称" value="helloWorld">
                <textarea id="function-data" placeholder="函数参数 (JSON)" rows="3">{"name": "World"}</textarea>
                <button class="button" onclick="testFunction()">调用函数</button>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📝 测试日志</h2>
        <div class="log" id="test-log">
            等待测试操作...
        </div>
        <button class="button" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let logElement = document.getElementById('test-log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const emoji = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            logElement.innerHTML += `[${timestamp}] ${emoji} ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logElement.innerHTML = '日志已清空<br>';
        }
        
        // 检查模拟器状态
        async function checkAllEmulators() {
            const statusElement = document.getElementById('emulator-status');
            const emulators = [
                { name: 'Auth', port: 9099 },
                { name: 'Firestore', port: 8080 },
                { name: 'Storage', port: 9199 },
                { name: 'Database', port: 9000 },
                { name: 'Functions', port: 5001 },
            ];
            
            let statusHTML = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">';
            
            for (const emulator of emulators) {
                try {
                    const response = await fetch(`http://localhost:${emulator.port}`, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    statusHTML += `<div><strong>${emulator.name}:</strong> <span class="status connected">已连接</span></div>`;
                } catch {
                    statusHTML += `<div><strong>${emulator.name}:</strong> <span class="status disconnected">未连接</span></div>`;
                }
            }
            
            statusHTML += '</div>';
            statusElement.innerHTML = statusHTML;
        }
        
        // 模拟测试函数（实际项目中需要导入真实的 Firebase 服务）
        function testSignup() {
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const name = document.getElementById('signup-name').value;
            
            if (!email || !password) {
                addLog('请填写邮箱和密码', 'error');
                return;
            }
            
            addLog(`模拟注册用户: ${email}`, 'info');
            addLog('注意: 请在实际的注册页面测试真实功能', 'info');
        }
        
        function testLogin() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;
            
            if (!email || !password) {
                addLog('请填写邮箱和密码', 'error');
                return;
            }
            
            addLog(`模拟登录用户: ${email}`, 'info');
        }
        
        function testLogout() {
            addLog('模拟用户登出', 'info');
        }
        
        function testFirestoreWrite() {
            const collection = document.getElementById('doc-collection').value;
            const docId = document.getElementById('doc-id').value;
            const data = document.getElementById('doc-data').value;
            
            try {
                JSON.parse(data);
                addLog(`模拟写入 Firestore: ${collection}/${docId}`, 'info');
                addLog(`数据: ${data}`, 'info');
            } catch {
                addLog('无效的 JSON 数据', 'error');
            }
        }
        
        function testFirestoreRead() {
            const collection = document.getElementById('doc-collection').value;
            const docId = document.getElementById('doc-id').value;
            addLog(`模拟读取 Firestore: ${collection}/${docId}`, 'info');
        }
        
        function testFirestoreDelete() {
            const collection = document.getElementById('doc-collection').value;
            const docId = document.getElementById('doc-id').value;
            addLog(`模拟删除 Firestore: ${collection}/${docId}`, 'info');
        }
        
        function testStorageUpload() {
            const fileInput = document.getElementById('file-input');
            const path = document.getElementById('file-path').value;
            
            if (!fileInput.files[0]) {
                addLog('请选择要上传的文件', 'error');
                return;
            }
            
            addLog(`模拟上传文件: ${fileInput.files[0].name} 到 ${path}`, 'info');
        }
        
        function testStorageDownload() {
            const path = document.getElementById('file-path').value;
            addLog(`模拟获取下载链接: ${path}`, 'info');
        }
        
        function testStorageDelete() {
            const path = document.getElementById('file-path').value;
            addLog(`模拟删除文件: ${path}`, 'info');
        }
        
        function testDatabaseWrite() {
            const path = document.getElementById('db-path').value;
            const data = document.getElementById('db-data').value;
            
            try {
                JSON.parse(data);
                addLog(`模拟写入 Database: ${path}`, 'info');
                addLog(`数据: ${data}`, 'info');
            } catch {
                addLog('无效的 JSON 数据', 'error');
            }
        }
        
        function testDatabaseRead() {
            const path = document.getElementById('db-path').value;
            addLog(`模拟读取 Database: ${path}`, 'info');
        }
        
        function testDatabaseDelete() {
            const path = document.getElementById('db-path').value;
            addLog(`模拟删除 Database: ${path}`, 'info');
        }
        
        function testFunction() {
            const functionName = document.getElementById('function-name').value;
            const data = document.getElementById('function-data').value;
            
            try {
                JSON.parse(data);
                addLog(`模拟调用函数: ${functionName}`, 'info');
                addLog(`参数: ${data}`, 'info');
            } catch {
                addLog('无效的 JSON 参数', 'error');
            }
        }
        
        // 页面加载时检查模拟器状态
        window.onload = function() {
            addLog('Firebase 模拟器测试页面已加载', 'success');
            checkAllEmulators();
        };
    </script>
</body>
</html> 