/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";
import "./VariantHoverTrueWrapper.css";

interface Props {
  variant: "one";
  className: any;
  divClassName: any;
}

export const VariantHoverTrueWrapper = ({
  variant,
  className,
  divClassName,
}: Props): JSX.Element => {
  return (
    <div className={`variant-hover-true-wrapper ${className}`}>
      <div className="text-wrapper-12">phone_iphone</div>

      <div className={`text-wrapper-13 ${divClassName}`}>Get on App Store</div>
    </div>
  );
};
