{"name": "onenata_admin", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=--inspect next dev", "build": "next build", "start": "next start", "lint": "next lint", "test:firebase": "node scripts/test-firebase-auth.mjs", "test:auth": "node scripts/test-direct-auth.mjs", "emulator": "./firebase-emulators.sh", "debug": "next dev --turbopack --inspect"}, "dependencies": {"@firebasegen/default-connector": "file:dataconnect-generated/js/default-connector", "@types/uuid": "^10.0.0", "clsx": "^2.1.1", "firebase": "^11.10.0", "firebase-admin": "^13.4.0", "next": "^15.3.5", "next-intl": "4.0", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5.8.3"}}