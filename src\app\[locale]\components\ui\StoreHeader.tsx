'use client';

import { useState, useRef, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../lib/firebase/context/AuthContext';
import { getUserStoreRole, getVisibleNavigationItems, UserStoreRole } from '../../../lib/utils/permissions';
import { 
  FiUser, 
  FiLogOut, 
  FiSettings, 
  FiChevronDown,
  FiBell,
  FiMenu,
  FiHome,
  FiUsers,
  FiCalendar,
  FiBarChart,
  FiMapPin,
  FiArrowLeft
} from 'react-icons/fi';

interface StoreHeaderProps {
  storeName?: string;
  storeId?: string;
  currentPage?: 'overview' | 'staff' | 'services' | 'analytics' | 'bookings' | 'customers' | 'appointments';
  showBackButton?: boolean;
}

export const StoreHeader = ({ storeName, storeId, currentPage = 'overview', showBackButton = true }: StoreHeaderProps) => {
  const t = useTranslations('common');
  const tStore = useTranslations('store');
  const router = useRouter();
  const { user, userData, signOut } = useAuth();
  
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [userStoreRole, setUserStoreRole] = useState<UserStoreRole | null>(null);
  const [loadingRole, setLoadingRole] = useState(true);
  const userMenuRef = useRef<HTMLDivElement>(null);

  // 获取用户在当前店铺的角色
  useEffect(() => {
    const loadUserRole = async () => {
      if (!user?.uid || !storeId) {
        setLoadingRole(false);
        return;
      }

      console.log('userData', userData);

      try {
        const role = await getUserStoreRole(userData!.uid || '', storeId);
        setUserStoreRole(role);
      } catch (error) {
        console.error('Error loading user role:', error);
      } finally {
        setLoadingRole(false);
      }
    };

    loadUserRole();
  }, [user?.uid, storeId]);

  // 处理点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/auth/login');
    } catch (error) {
      console.error('error sign out:', error);
    }
  };

  const handleBackClick = () => {
    router.push('/dashboard');
  };

  // 获取可见的导航项
  const navigationItems = getVisibleNavigationItems(userStoreRole).map(item => {
    const iconMap: { [key: string]: React.ComponentType<React.SVGProps<SVGSVGElement>> } = {
      FiHome,
      FiUsers,
      FiCalendar,
      FiBarChart
    };

    return {
      ...item,
      icon: iconMap[item.icon],
      href: item.href.replace('[id]', storeId || '')
    };
  });

  // 调试信息
  console.log('StoreHeader - userStoreRole:', userStoreRole);
  console.log('StoreHeader - navigationItems:', navigationItems);

  return (
    <header className="bg-white border-b border-gray-200 shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* 左侧 - 返回按钮、Logo和导航 */}
          <div className="flex items-center space-x-4">
            {/* 返回按钮 */}
            {showBackButton && (
              <button
                onClick={handleBackClick}
                className="flex items-center justify-center w-10 h-10 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors"
                title={t('actions.back')}
              >
                <FiArrowLeft className="w-5 h-5" />
              </button>
            )}

            {/* Logo */}
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <FiMapPin className="w-5 h-5 text-white" />
              </div>
              <div className="flex flex-col">
                <h1 className="text-lg font-semibold text-gray-900">
                  {storeName || 'OneNata Admin'}
                </h1>
                <span className="text-xs text-gray-500">{tStore('management')}</span>
              </div>
            </div>

            {/* 桌面端导航 */}
            {storeId && !loadingRole && (
              <nav className="hidden md:flex items-center space-x-1 ml-8">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentPage === item.key;
                  
                  return (
                    <button
                      key={item.key}
                      onClick={() => router.push(item.href)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        isActive
                          ? 'bg-purple-100 text-purple-700 border border-purple-200'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      <span>{tStore(item.label)}</span>
                    </button>
                  );
                })}
              </nav>
            )}
          </div>

          {/* 右侧 - 通知和用户菜单 */}
          <div className="flex items-center space-x-4">
            {/* 用户角色显示 */}
            {/* {userStoreRole && (
              <div className="hidden sm:flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-lg">
                <span className="text-xs text-gray-600">角色:</span>
                <span className={`text-xs font-medium px-2 py-0.5 rounded ${
                  userStoreRole.isOwner 
                    ? 'bg-purple-100 text-purple-800' 
                    : userStoreRole.isAdmin 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {userStoreRole.isOwner ? '店主' : userStoreRole.isAdmin ? '管理员' : '员工'}
                </span>
              </div>
            )} */}

            {/* 通知按钮 */}
            <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
              <FiBell className="w-5 h-5" />
            </button>

            {/* 移动端菜单按钮 */}
            {storeId && !loadingRole && (
              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className="md:hidden p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <FiMenu className="w-5 h-5" />
              </button>
            )}

            {/* 用户菜单 */}
            <div className="relative" ref={userMenuRef}>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-3 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                  <FiUser className="w-4 h-4 text-white" />
                </div>
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-medium text-gray-900">
                    {userData?.firstName} {userData?.lastName}
                  </div>
                  <div className="text-xs text-gray-500">{user?.email}</div>
                </div>
                <FiChevronDown className="w-4 h-4" />
              </button>

              {/* 用户下拉菜单 */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                  <div className="px-4 py-3 border-b border-gray-100">
                    <div className="text-sm font-medium text-gray-900">
                      {userData?.firstName} {userData?.lastName}
                    </div>
                    <div className="text-xs text-gray-500">{user?.email}</div>
                    {userStoreRole && (
                      <div className="text-xs text-purple-600 mt-1">
                        {userStoreRole.isOwner ? 'Owner' : 
                         userStoreRole.isAdmin ? 'Admin' : 'Staff'}
                      </div>
                    )}
                  </div>
                  
                  <button
                    onClick={() => router.push('/auth/profile-edit')}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    <FiSettings className="w-4 h-4" />
                    <span>{t('profile.settings')}</span>
                  </button>
                  
                  <button
                    onClick={handleSignOut}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                  >
                    <FiLogOut className="w-4 h-4" />
                    <span>{t('auth.signOut')}</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 移动端导航菜单 */}
        {storeId && !loadingRole && showMobileMenu && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="flex flex-col space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.key;
                
                return (
                  <button
                    key={item.key}
                    onClick={() => {
                      router.push(item.href);
                      setShowMobileMenu(false);
                    }}
                    className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      isActive
                        ? 'bg-purple-100 text-purple-700 border border-purple-200'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tStore(item.label)}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}; 