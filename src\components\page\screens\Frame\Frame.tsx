import React from "react";
import { ContainerWrapper } from "./sections/ContainerWrapper";
import { Div } from "./sections/Div";
import { DivWrapper } from "./sections/DivWrapper";
import { Footer } from "./sections/Footer";
import { Header } from "./sections/Header";
import { Section } from "./sections/Section";
import { SectionComponentNode } from "./sections/SectionComponentNode";
import { SectionWrapper } from "./sections/SectionWrapper";
import "./Frame.css";

interface FrameProps {
  locale: string;
}

export const Frame = ({ locale }: FrameProps): JSX.Element => {
  return (
    <div className="frame" data-model-id="3606:26855">
      <div className="div-2">
        <div className="overlap-6">
          <Header locale={locale} />
          <img
            className="toy"
            alt="Toy"
            src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
          />
        </div>

        <Section />
        <div className="overlap-7">
          <SectionWrapper />
          <div className="black-2">
            <div className="overlap-8">
              <div className="element-2">
                <div className="img-wrapper">
                  <img
                    className="mask-group-3"
                    alt="Mask group"
                    src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
                  />
                </div>
              </div>

              <div className="element-3">
                <div className="mockup-3">
                  <img
                    className="mask-group-4"
                    alt="Mask group"
                    src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="overlap-9">
          <DivWrapper />
          <div className="hand-and-iphone">
            <div className="artboard">
              <img
                className="background-4"
                alt="Background"
                src="https://c.animaapp.com/9exExmER/img/background.png"
              />

              <div className="overlap-group-3">
                <img
                  className="hand"
                  alt="Hand"
                  src="https://c.animaapp.com/9exExmER/img/hand.png"
                />

                <img
                  className="iphone-pro-2"
                  alt="Iphone pro"
                  src="https://c.animaapp.com/9exExmER/img/iphone-16-pro.png"
                />

                <div className="mockup-4" />
              </div>
            </div>
          </div>
        </div>

        <Div />
        <SectionComponentNode />
        <ContainerWrapper />
        <Footer />
      </div>
    </div>
  );
};
