import { 
  User, 
  createUserWithEmailAndPassword, 
  sendEmailVerification, 
  sendPasswordResetEmail, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  UserCredential,
  updateProfile,
  onAuthStateChanged,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  ConfirmationResult,
  updatePassword,
  reauthenticateWithCredential,
  EmailAuthProvider
} from 'firebase/auth';
import { FirebaseError } from 'firebase/app';
import { auth, db } from '../config';
import { doc, setDoc, query, collection, where, getDocs, updateDoc } from 'firebase/firestore';
import { v4 as uuidv4 } from 'uuid';
import { createHash, randomBytes } from 'crypto';
import { PortalUserAccount, PortalUserData } from '../../models/portal-user';
import { UserType } from '../../models/types';
import { AuthError, AuthErrorType } from '../../types/common';

// 存储电话验证结果
let phoneVerificationResult: ConfirmationResult | null = null;

// 生成盐值
const generateHashSalt = (): string => {
  return randomBytes(32).toString('hex');
};

// 计算哈希值
const hashPasswordWithSalt = (password: string, salt: string): string => {
  return createHash('sha256').update(password + salt).digest('hex');
};

// 使用邮箱密码注册
export const signUp = async (
  email: string, 
  password: string, 
  firstName: string,
  lastName: string,
  displayName?: string,
  invitationCode?: string,
  phoneNumber?: string,
  userType: UserType = UserType.PETSTORE_STAFF,
  locale?: string,
  notificationsEnabled?: boolean,
  createdBy?: string
): Promise<string> => {
  try {
    // 验证输入
    if (!email || !password || !firstName || !lastName) {
      throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, 'Invalid credentials');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, 'Invalid email format');
    }

    // 验证密码强度
    if (password.length < 6) {
      throw new AuthError(AuthErrorType.WEAK_PASSWORD, 'Password must be at least 6 characters long');
    }

    // 检查邮箱是否已存在
    const emailExists = await checkEmailExists(email);
    if (emailExists) {
      throw new AuthError(AuthErrorType.USER_ALREADY_EXISTS, 'Email already exists');
    }

    // 检查手机号是否已存在（如果提供）
    if (phoneNumber) {
      const phoneExists = await checkPhoneExists(phoneNumber);
      if (phoneExists) {
        throw new AuthError(AuthErrorType.USER_ALREADY_EXISTS, 'Phone number already exists');
      }
    }

    // 创建Firebase Auth用户
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;


    // 生成随机盐值和哈希密码
    const salt = generateHashSalt();
    const hashedCredential = hashPasswordWithSalt(password, salt);

    // 更新用户档案
    await updateProfile(firebaseUser, {
      displayName: displayName || `${firstName} ${lastName}`,
    });

    // 发送邮箱验证
    await sendEmailVerification(firebaseUser);

    // // 只有当提供了有效的手机号时才发送验证码
    // if (phoneNumber && phoneNumber.trim() !== '' && phoneNumber.trim() !== '+1 ') {
    //   await sendPhoneVerificationCode(phoneNumber);
    // }

        // 创建用户文档
    const uid = await createPortalUserDocuments(
      firebaseUser, 
      firstName, 
      lastName, 
      displayName || `${firstName} ${lastName}`, 
      invitationCode, 
      salt, 
      hashedCredential, 
      phoneNumber, 
      userType,
      locale,
      notificationsEnabled,
      createdBy || undefined,
      false
    );
    

    return uid;
  } catch (error: unknown) {
    console.error('Error signing up: ', error);
    if (error instanceof AuthError) {
      throw error;
    }
    throw new AuthError(
      AuthErrorType.UNKNOWN_ERROR, 
      getFirebaseErrorMessage(error as { code?: string; message?: string })
    );
  }
};

// 创建用户文档
export const createPortalUserDocuments = async (
  user: User, 
  firstName: string,
  lastName: string,
  displayName: string,
  invitationCode?: string, 
  salt?: string, 
  hashedCredential?: string, 
  phoneNumber?: string,
  userType: UserType = UserType.PETSTORE_STAFF,
  locale?: string,
  notificationsEnabled?: boolean,
  createdBy?: string,
  needChangePassword?: boolean
): Promise<string> => {
  try {
    const sid = uuidv4();

    // 创建用户账户文档 - portal-user-account 集合
    const userAccountRef = doc(db, 'portal-user-account', sid);
    const userAccountData: Partial<PortalUserAccount> = {
      sid,
      fid: user.uid,
      phoneNumber: phoneNumber || undefined,
      email: user.email || '',
      salt: salt || undefined,
      hashedCredential: hashedCredential || undefined,
      isEmailVerified: user.emailVerified,
      isValid: true,
      isSynced: true,
      updated_by: createdBy || sid,
      needChangePassword: needChangePassword || false,
    };

    // 移除 undefined 值
    Object.keys(userAccountData).forEach(key => {
      if (userAccountData[key as keyof typeof userAccountData] === undefined) {
        delete userAccountData[key as keyof typeof userAccountData];
      }
    });

    await setDoc(userAccountRef, userAccountData);

    // 创建用户数据文档 - portal-user-data 集合
    const userDataSid = uuidv4();
    const userDataRef = doc(db, 'portal-user-data', userDataSid);
    const userData: Partial<PortalUserData> = {
      sid: userDataSid,
      fid: user.uid,
      uid: sid, // 关联到用户账户的 sid
      displayName,
      firstName,
      lastName,
      phoneNumber: phoneNumber || undefined,
      userType,
      invitationCode: invitationCode || undefined,
      bio: undefined,
      photoURL: user.photoURL || undefined,
      preferences: {
        notificationsEnabled: notificationsEnabled ?? true,
        language: locale || 'zh-CN'
      },
      isValid: true,
      isSynced: true,
      updated_by: createdBy || sid,
      update_date: new Date()
    };

    // 移除 undefined 值
    Object.keys(userData).forEach(key => {
      if (userData[key as keyof typeof userData] === undefined) {
        delete userData[key as keyof typeof userData];
      }
    });

    await setDoc(userDataRef, userData);

    return userData.uid || '';

  } catch (error: unknown) {
    console.error('Error creating user documents: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '创建用户文档失败');
  }
};

// 使用邮箱密码登录
export const signIn = async (email: string, password: string): Promise<UserCredential> => {
  try {
    // 验证输入
    if (!email || !password) {
      throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, 'Invalid credentials');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, 'Invalid email format');
    }

    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    
    // 更新用户最后登录时间
    await updateUserLastLogin(userCredential.user.uid);
    
    return userCredential;
  } catch (error: unknown) {
    console.error('Error signing in: ', error);
    if (error instanceof AuthError) {
      throw error;
    }
    throw new AuthError(
      AuthErrorType.INVALID_CREDENTIALS,
      getFirebaseErrorMessage(error as { code?: string; message?: string })
    );
  }
};

// 登出
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error: unknown) {
    console.error('Error signing out: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '登出失败');
  }
};

// 更新用户最后登录时间
export const updateUserLastLogin = async (uid: string): Promise<void> => {
  try {
    const collectionRef = collection(db, 'portal-user-account');
    const q = query(collectionRef, where('fid', '==', uid));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.error('User not found');
      return;
    }

    await setDoc(querySnapshot.docs[0].ref, {
      updatedAt: new Date().toISOString()
    }, { merge: true });
  } catch (error: unknown) {
    console.error('Error updating last login: ', error);
    // 不抛出错误，这是非关键操作
  }
};

// 发送密码重置邮件
export const resetPassword = async (email: string): Promise<void> => {
  try {
    // 验证输入
    if (!email) {
      throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, '缺少邮箱地址');
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, '邮箱格式不正确');
    }

    await sendPasswordResetEmail(auth, email);
  } catch (error: unknown) {
    console.error('Error sending password reset email: ', error);
    if (error instanceof AuthError) {
      throw error;
    }
    throw new AuthError(
      AuthErrorType.UNKNOWN_ERROR,
      getFirebaseErrorMessage(error as { code?: string; message?: string })
    );
  }
};

// 发送邮箱验证邮件
export const sendEmailVerificationToUser = async (user: User): Promise<void> => {
  try {
    await sendEmailVerification(user);
  } catch (error: unknown) {
    console.error('Error sending verification email: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '发送验证邮件失败');
  }
};

// 更新用户账户的邮箱验证状态
export const updateUserAccountEmailVerificationStatus = async (uid: string, isVerified: boolean): Promise<void> => {
  try {
    const collectionRef = collection(db, 'portal-user-account');
    const q = query(collectionRef, where('fid', '==', uid));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const docRef = querySnapshot.docs[0].ref;
      await updateDoc(docRef, {
        isEmailVerified: isVerified,
        updatedAt: new Date()
      });
      console.log('User account email verification status updated:', uid, isVerified);
    } else {
      console.warn('User account not found for uid:', uid);
    }
  } catch (error: unknown) {
    console.error('Error updating user account email verification status: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '更新邮箱验证状态失败');
  }
};

// 更新用户个人资料完成状态
export const updateUserProfileCompletionStatus = async (uid: string, isCompleted: boolean): Promise<void> => {
  try {
    const collectionRef = collection(db, 'portal-user-data');
    const q = query(collectionRef, where('fid', '==', uid));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const docRef = querySnapshot.docs[0].ref;
      await updateDoc(docRef, {
        isProfileCompleted: isCompleted,
        updatedAt: new Date()
      });
      console.log('User profile completion status updated:', uid, isCompleted);
    } else {
      console.warn('User data not found for uid:', uid);
    }
  } catch (error: unknown) {
    console.error('Error updating user profile completion status: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '更新个人资料完成状态失败');
  }
};

// 获取用户账户数据
export const getUserAccountData = async (uid: string): Promise<PortalUserAccount | null> => {
  try {
    const collectionRef = collection(db, 'portal-user-account');
    const q = query(collectionRef, where('fid', '==', uid));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    return querySnapshot.docs[0].data() as PortalUserAccount;
  } catch (error: unknown) {
    console.error('Error getting user account data: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '获取用户账户数据失败');
  }
};

// 获取用户数据
export const getUserData = async (uid: string): Promise<PortalUserData | null> => {
  try {
    const collectionRef = collection(db, 'portal-user-data');
    const q = query(collectionRef, where('fid', '==', uid));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    return querySnapshot.docs[0].data() as PortalUserData;
  } catch (error: unknown) {
    console.error('Error getting user data: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '获取用户数据失败');
  }
};

// 根据用户账户 sid 获取用户数据
export const getUserDataByAccountSid = async (accountSid: string): Promise<PortalUserData | null> => {
  try {
    const collectionRef = collection(db, 'portal-user-data');
    const q = query(collectionRef, where('uid', '==', accountSid));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    return querySnapshot.docs[0].data() as PortalUserData;
  } catch (error: unknown) {
    console.error('Error getting user data by account sid: ', error);
    throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '获取用户数据失败');
  }
};

// 获取用户的 Portal 账户 SID
export const getPortalAccountSid = async (uid: string): Promise<string | null> => {
  try {
    const userData = await getUserAccountData(uid);
    return userData?.sid || null;
  } catch (error: unknown) {
    console.error('Error getting portal account sid: ', error);
    return null;
  }
};

// 检查邮箱是否已存在
export const checkEmailExists = async (email: string): Promise<boolean> => {
  try {
    const collectionRef = collection(db, 'portal-user-account');
    const q = query(collectionRef, where('email', '==', email));
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error: unknown) {
    console.error('Error checking email existence: ', error);
    return false;
  }
};

// 检查手机号是否已存在
export const checkPhoneExists = async (phoneNumber: string): Promise<boolean> => {
  try {
    const collectionRef = collection(db, 'portal-user-account');
    const q = query(collectionRef, where('phoneNumber', '==', phoneNumber));
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error: unknown) {
    console.error('Error checking phone existence: ', error);
    return false;
  }
};

// 监听用户认证状态
export const onAuthChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, callback);
};

// 发送手机号验证码
export const sendPhoneVerificationCode = async (phoneNumber: string): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('Sending verification code to phone:', phoneNumber);
    
    // 验证手机号码格式
    if (!phoneNumber || phoneNumber.trim() === '' || phoneNumber.trim() === '+1 ') {
      return {
        success: false,
        message: '请输入有效的手机号码'
      };
    }
    
    // 确保手机号码以+1开头（北美格式）
    let formattedPhone = phoneNumber.replace(/\D/g, '');
    if (formattedPhone.length === 10) {
      formattedPhone = '+1' + formattedPhone;
    } else if (formattedPhone.length === 11 && formattedPhone.startsWith('1')) {
      formattedPhone = '+' + formattedPhone;
    } else {
      return {
        success: false,
        message: '请输入有效的手机号码格式'
      };
    }
    
    console.log('Formatted phone number:', formattedPhone);
    
    // 在开发环境中，我们使用Firebase Auth模拟器的手机验证
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode: Using Firebase Auth emulator for phone verification');
      // 发送验证码
      // 设置reCAPTCHA验证器
      const recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        }
      });
      const confirmationResult = await signInWithPhoneNumber(auth, formattedPhone, recaptchaVerifier);
      phoneVerificationResult = confirmationResult;
      // 在模拟器中，我们可以直接返回成功，因为模拟器会自动处理验证码
      // Firebase Auth模拟器支持特定的测试手机号码
      return {
        success: true,
        message: '验证码已发送到您的手机'
      };
    }
    
    // 生产环境中使用真实的Firebase Auth手机验证
    try {
      // 设置reCAPTCHA验证器
      const recaptchaVerifier = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
        callback: () => {
          console.log('reCAPTCHA solved');
        }
      });
      
      // 发送验证码
      const confirmationResult = await signInWithPhoneNumber(auth, formattedPhone, recaptchaVerifier);
      phoneVerificationResult = confirmationResult;
      
      return {
        success: true,
        message: '验证码已发送到您的手机'
      };
    } catch (error) {
      console.error('Firebase phone verification error:', error);
      return {
        success: false,
        message: '发送验证码失败，请稍后重试'
      };
    }
  } catch (error) {
    console.error('Error sending phone verification code:', error);
    return {
      success: false,
      message: '发送验证码失败，请稍后重试'
    };
  }
};

// 验证手机号验证码
export const verifyPhoneCode = async (phoneNumber: string, code: string): Promise<{ success: boolean; message: string }> => {
  try {
    console.log('Verifying phone code:', phoneNumber, code);
    
    // 在开发环境中，使用Firebase Auth模拟器验证
    if (process.env.NODE_ENV === 'development') {
      console.log('Development mode: Using Firebase Auth emulator for phone verification');
      
             // Firebase Auth模拟器支持特定的测试手机号和验证码
       // 对于测试手机号，任何6位数字都可以作为验证码
       if (code.length === 6 && /^\d{6}$/.test(code)) {
         try {
           // 在模拟器环境中，我们直接返回成功
           // 实际的用户创建会在注册流程中处理
           return {
             success: true,
             message: '验证成功'
           };
         } catch (error) {
           console.error('Firebase phone verification error:', error);
           return {
             success: false,
             message: '验证码错误'
           };
         }
       } else {
        return {
          success: false,
          message: '请输入6位数字验证码'
        };
      }
    }
    
    // 生产环境中使用真实的Firebase Auth验证
    try {
      if (!phoneVerificationResult) {
        return {
          success: false,
          message: '请先发送验证码'
        };
      }
      
      // 确认验证码
      const result = await phoneVerificationResult.confirm(code);
      console.log('Phone verification successful:', result);
      
      return {
        success: true,
        message: '验证成功'
      };
    } catch (error) {
      console.error('Firebase phone verification error:', error);
      return {
        success: false,
        message: '验证码错误'
      };
    }
  } catch (error) {
    console.error('Error verifying phone code:', error);
    return {
      success: false,
      message: '验证失败，请稍后重试'
    };
  }
};

// 管理员创建员工账户
export const createStaffAccount = async (
  email: string, 
  password: string, 
  createdBy?: string,
  phoneNumber?: string,
  needChangePassword?: boolean
): Promise<string> => {
  try {
    // 动态导入避免循环依赖
    const { createStaffAccountFunction } = await import('./functions');
    
    // const salt = generateHashSalt();
    // const hashedCredential = hashPasswordWithSalt(password, salt);
    
    // 调用 Cloud Function 创建员工账户
    const result = await createStaffAccountFunction({
      email,
      password,
      createdBy,
      phoneNumber,
      needChangePassword
    });

    if (!result.success) {
      throw new Error(result.error || 'Failed to create staff account');
    }

    if (!result.data) {
      throw new Error('Failed to create staff account');
    }

 

    return result.data.userAccountSid;
    
  } catch (error) {
    console.error('Error creating staff account:', error);
    throw error;
  }
};

// 更新密码（同时更新 Firebase Auth 和 Firestore）
export const updatePasswordBoth = async (
  email: string,
  currentPassword: string,
  newPassword: string,
  userAccountSid: string
): Promise<void> => {
  try {
    // 1. 验证当前密码（重新认证）
    const user = auth.currentUser;
    if (!user) {
      throw new AuthError(AuthErrorType.UNAUTHORIZED, '用户未登录');
    }

    // 2. 重新认证用户
    const credential = EmailAuthProvider.credential(email, currentPassword);
    await reauthenticateWithCredential(user, credential);

    // 3. 更新 Firebase Auth 中的密码
    await updatePassword(user, newPassword);

    // 4. 生成新的盐值和哈希密码
    const newSalt = generateHashSalt();
    const newHashedCredential = hashPasswordWithSalt(newPassword, newSalt);

    // 5. 更新 Firestore 中的用户账户数据
    const userAccountRef = doc(db, 'portal-user-account', userAccountSid);
    await updateDoc(userAccountRef, {
      salt: newSalt,
      hashedCredential: newHashedCredential,
      needChangePassword: false, // 修改密码后标记为不需要再次修改
      update_date: new Date(),
    });

    console.log('密码更新成功');
  } catch (error: unknown) {
    console.error('更新密码失败:', error);
    
    if (error instanceof FirebaseError) {
      switch (error.code) {
        case 'auth/wrong-password':
          throw new AuthError(AuthErrorType.INVALID_CREDENTIALS, '当前密码错误');
        case 'auth/weak-password':
          throw new AuthError(AuthErrorType.WEAK_PASSWORD, '新密码强度不够');
        case 'auth/requires-recent-login':
          throw new AuthError(AuthErrorType.REQUIRES_RECENT_LOGIN, '需要重新登录');
        default:
          throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '密码更新失败');
      }
    } else if (error instanceof AuthError) {
      throw error;
    } else {
      throw new AuthError(AuthErrorType.UNKNOWN_ERROR, '密码更新失败');
    }
  }
};

// 获取 Firebase 错误消息
export const getFirebaseErrorMessage = (error: { code?: string; message?: string }): string => {
  switch (error.code) {
    case 'auth/email-already-in-use':
      return '该邮箱已被注册';
    case 'auth/invalid-email':
      return '邮箱格式不正确';
    case 'auth/weak-password':
      return '密码强度不够';
    case 'auth/user-not-found':
      return '用户不存在';
    case 'auth/wrong-password':
      return '密码错误';
    case 'auth/too-many-requests':
      return '请求过于频繁，请稍后再试';
    case 'auth/network-request-failed':
      return '网络连接失败';
    case 'auth/user-disabled':
      return '用户账户已被禁用';
    case 'auth/operation-not-allowed':
      return '操作不被允许';
    default:
      return error.message || '发生未知错误';
  }
}; 


// 创建customer
export const createCustomerAccount = async (
  email: string,
  password: string,
  createdBy?: string,
  phoneNumber?: string,
  needChangePassword?: boolean
): Promise<string> => {
  try {
    // 动态导入避免循环依赖 
    const { createCustomerFunction } = await import('./functions');
    
    // 调用 Cloud Function 创建客户账户
    const result = await createCustomerFunction({
      email,
      password,
      createdBy,
      phoneNumber,
      needChangePassword
    });   

    if (!result.success) {
      throw new Error(result.error || 'Failed to create customer account');
    }

    if (!result.data) {
      throw new Error('Failed to create customer account');
    }

    return result.data.userAccountSid;
  } catch (error) {
    console.error('Error creating customer account:', error);
    throw error;
  }
};