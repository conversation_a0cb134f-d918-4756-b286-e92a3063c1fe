import { BaseModel, BaseModelImpl, JsonUtil } from './base_model';
import { 
  OrderType,
  OrderStatus,
  PaymentStatus,
  DeliveryStatus,
  Currency,
  Timestamp,
  Address,
  OrderItem
} from './types';

/**
 * Order - 订单
 * 存储在 Firestore collection "orders"
 */
export interface Order extends BaseModel {
  orderType: OrderType;
  orderTotal: number; // 订单总额
  orderPaymentPrice: number; // 实际支付金额
  orderPromotionCode?: string; // 优惠码
  orderHST: number; // HST 税
  orderPST: number; // PST 税
  orderCurrency: Currency;
  orderStatus: OrderStatus;
  paymentId: string; // 支付 ID
  paymentStatus: PaymentStatus;
  customerId: string; // 客户 ID (OneNata App 用户)
  delivery: boolean; // 是否配送
  deliveryStatus?: DeliveryStatus;
  lastUpdateId?: string; // 最新跟踪记录 ID
  address?: Address; // 配送地址
  photoUrls: string[]; // 订单相关照片
  storeId?: string; // 店铺 ID
  estimatedDeliveryTime?: Timestamp; // 预计配送时间
  actualDeliveryTime?: Timestamp; // 实际配送时间
  notes?: string; // 订单备注
  createdAt?: Timestamp; // 创建时间 (继承自 BaseModel)
}

export class OrderImpl extends BaseModelImpl implements Order {
  orderType: OrderType;
  orderTotal: number;
  orderPaymentPrice: number;
  orderPromotionCode?: string;
  orderHST: number;
  orderPST: number;
  orderCurrency: Currency;
  orderStatus: OrderStatus;
  paymentId: string;
  paymentStatus: PaymentStatus;
  customerId: string;
  delivery: boolean;
  deliveryStatus?: DeliveryStatus;
  lastUpdateId?: string;
  address?: Address;
  photoUrls: string[];
  storeId?: string;
  estimatedDeliveryTime?: Timestamp;
  actualDeliveryTime?: Timestamp;
  notes?: string;

  constructor(data: Partial<Order>) {
    super(data);
    this.orderType = data.orderType || OrderType.PRODUCTS;
    this.orderTotal = data.orderTotal || 0;
    this.orderPaymentPrice = data.orderPaymentPrice || 0;
    this.orderPromotionCode = data.orderPromotionCode;
    this.orderHST = data.orderHST || 0;
    this.orderPST = data.orderPST || 0;
    this.orderCurrency = data.orderCurrency || Currency.CAD;
    this.orderStatus = data.orderStatus || OrderStatus.PENDING;
    this.paymentId = data.paymentId || '';
    this.paymentStatus = data.paymentStatus || PaymentStatus.UNPAID;
    this.customerId = data.customerId || '';
    this.delivery = data.delivery ?? false;
    this.deliveryStatus = data.deliveryStatus;
    this.lastUpdateId = data.lastUpdateId;
    this.address = data.address;
    this.photoUrls = data.photoUrls || [];
    this.storeId = data.storeId;
    this.estimatedDeliveryTime = data.estimatedDeliveryTime;
    this.actualDeliveryTime = data.actualDeliveryTime;
    this.notes = data.notes;
  }

  static fromJson(json: Record<string, unknown>): OrderImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new OrderImpl({
      ...baseModel,
      orderType: (json.orderType as OrderType) || OrderType.PRODUCTS,
      orderTotal: JsonUtil.numberFromJson(json.orderTotal) || 0,
      orderPaymentPrice: JsonUtil.numberFromJson(json.orderPaymentPrice) || 0,
      orderPromotionCode: JsonUtil.stringFromJson(json.orderPromotionCode),
      orderHST: JsonUtil.numberFromJson(json.orderHST) || 0,
      orderPST: JsonUtil.numberFromJson(json.orderPST) || 0,
      orderCurrency: (json.orderCurrency as Currency) || Currency.CAD,
      orderStatus: (json.orderStatus as OrderStatus) || OrderStatus.PENDING,
      paymentId: JsonUtil.stringFromJson(json.paymentId) || '',
      paymentStatus: (json.paymentStatus as PaymentStatus) || PaymentStatus.UNPAID,
      customerId: JsonUtil.stringFromJson(json.customerId) || '',
      delivery: JsonUtil.boolFromJson(json.delivery) ?? false,
      deliveryStatus: json.deliveryStatus as DeliveryStatus,
      lastUpdateId: JsonUtil.stringFromJson(json.lastUpdateId),
      address: json.address as Address,
      photoUrls: (json.photoUrls as string[]) || [],
      storeId: JsonUtil.stringFromJson(json.storeId),
      estimatedDeliveryTime: json.estimatedDeliveryTime ? new Date(json.estimatedDeliveryTime as string) : undefined,
      actualDeliveryTime: json.actualDeliveryTime ? new Date(json.actualDeliveryTime as string) : undefined,
      notes: JsonUtil.stringFromJson(json.notes),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      orderType: this.orderType,
      orderTotal: this.orderTotal,
      orderPaymentPrice: this.orderPaymentPrice,
      orderPromotionCode: JsonUtil.stringToJson(this.orderPromotionCode),
      orderHST: this.orderHST,
      orderPST: this.orderPST,
      orderCurrency: this.orderCurrency,
      orderStatus: this.orderStatus,
      paymentId: this.paymentId,
      paymentStatus: this.paymentStatus,
      customerId: this.customerId,
      delivery: this.delivery,
      deliveryStatus: this.deliveryStatus,
      lastUpdateId: JsonUtil.stringToJson(this.lastUpdateId),
      address: this.address,
      photoUrls: this.photoUrls,
      storeId: JsonUtil.stringToJson(this.storeId),
      estimatedDeliveryTime: this.estimatedDeliveryTime ? new Date(this.estimatedDeliveryTime).toISOString() : undefined,
      actualDeliveryTime: this.actualDeliveryTime ? new Date(this.actualDeliveryTime).toISOString() : undefined,
      notes: JsonUtil.stringToJson(this.notes),
    };
  }

  getTotalTax(): number {
    return this.orderHST + this.orderPST;
  }

  getSubtotal(): number {
    return this.orderTotal - this.getTotalTax();
  }

  getDiscount(): number {
    return this.orderTotal - this.orderPaymentPrice;
  }

  getFormattedTotal(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.orderCurrency
    }).format(this.orderTotal);
  }

  getFormattedPaymentPrice(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.orderCurrency
    }).format(this.orderPaymentPrice);
  }

  isCompleted(): boolean {
    return this.orderStatus === OrderStatus.COMPLETED;
  }

  isPaid(): boolean {
    return this.paymentStatus === PaymentStatus.PAID;
  }

  isDelivered(): boolean {
    return this.deliveryStatus === DeliveryStatus.DELIVERED;
  }

  requiresDelivery(): boolean {
    return this.delivery === true;
  }

  getDeliveryAddress(): string {
    if (!this.address) return '';
    const { addressLine1, addressLine2, city, province, postCode } = this.address;
    const parts = [addressLine1, addressLine2, city, province, postCode].filter(Boolean);
    return parts.join(', ');
  }

  getEstimatedDeliveryDate(): string {
    if (!this.estimatedDeliveryTime) return '未知';
    return new Date(this.estimatedDeliveryTime).toLocaleDateString('zh-CN');
  }

  getActualDeliveryDate(): string {
    if (!this.actualDeliveryTime) return '未配送';
    return new Date(this.actualDeliveryTime).toLocaleDateString('zh-CN');
  }
}

/**
 * Tracking Record - 跟踪记录
 */
export interface TrackingRecord extends BaseModel {
  orderId: string; // 订单 ID
  updateStatus: OrderStatus;
  updateDeliveryStatus?: DeliveryStatus;
  updateTime: Timestamp;
  photos: string[];
  notes?: string;
  updatedBy?: string; // 更新者 ID
  location?: string; // 更新位置
}

export class TrackingRecordImpl extends BaseModelImpl implements TrackingRecord {
  orderId: string;
  updateStatus: OrderStatus;
  updateDeliveryStatus?: DeliveryStatus;
  updateTime: Timestamp;
  photos: string[];
  notes?: string;
  updatedBy?: string;
  location?: string;

  constructor(data: Partial<TrackingRecord>) {
    super(data);
    this.orderId = data.orderId || '';
    this.updateStatus = data.updateStatus || OrderStatus.PENDING;
    this.updateDeliveryStatus = data.updateDeliveryStatus;
    this.updateTime = data.updateTime || new Date();
    this.photos = data.photos || [];
    this.notes = data.notes;
    this.updatedBy = data.updatedBy;
    this.location = data.location;
  }

  static fromJson(json: Record<string, unknown>): TrackingRecordImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new TrackingRecordImpl({
      ...baseModel,
      orderId: JsonUtil.stringFromJson(json.orderId) || '',
      updateStatus: (json.updateStatus as OrderStatus) || OrderStatus.PENDING,
      updateDeliveryStatus: json.updateDeliveryStatus as DeliveryStatus,
      updateTime: json.updateTime ? new Date(json.updateTime as string) : new Date(),
      photos: (json.photos as string[]) || [],
      notes: JsonUtil.stringFromJson(json.notes),
      updatedBy: JsonUtil.stringFromJson(json.updatedBy),
      location: JsonUtil.stringFromJson(json.location),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      orderId: this.orderId,
      updateStatus: this.updateStatus,
      updateDeliveryStatus: this.updateDeliveryStatus,
      updateTime: new Date(this.updateTime).toISOString(),
      photos: this.photos,
      notes: JsonUtil.stringToJson(this.notes),
      updatedBy: JsonUtil.stringToJson(this.updatedBy),
      location: JsonUtil.stringToJson(this.location),
    };
  }

  getFormattedUpdateTime(): string {
    return new Date(this.updateTime).toLocaleString('zh-CN');
  }

  getStatusText(): string {
    switch (this.updateStatus) {
      case OrderStatus.PENDING: return '待处理';
      case OrderStatus.COMPLETED: return '已完成';
      case OrderStatus.REJECTED: return '已拒绝';
      case OrderStatus.REFUNDED: return '已退款';
      default: return '未知状态';
    }
  }

  getDeliveryStatusText(): string {
    if (!this.updateDeliveryStatus) return '';
    switch (this.updateDeliveryStatus) {
      case DeliveryStatus.PREPARE_ORDER: return '准备订单';
      case DeliveryStatus.READY_FOR_CUSTOMER: return '等待客户取货';
      case DeliveryStatus.PICKED_UP: return '已取货';
      case DeliveryStatus.OUT_OF_DELIVERY: return '配送中';
      case DeliveryStatus.DELIVERED: return '已配送';
      default: return '未知状态';
    }
  }
}

/**
 * Order Products - 订单商品
 */
export interface OrderProducts extends BaseModel {
  orderId: string; // 订单 ID
  items: OrderItem[];
  totalQuantity: number; // 总数量
  totalAmount: number; // 总金额
}

export class OrderProductsImpl extends BaseModelImpl implements OrderProducts {
  orderId: string;
  items: OrderItem[];
  totalQuantity: number;
  totalAmount: number;

  constructor(data: Partial<OrderProducts>) {
    super(data);
    this.orderId = data.orderId || '';
    this.items = data.items || [];
    this.totalQuantity = data.totalQuantity || 0;
    this.totalAmount = data.totalAmount || 0;
  }

  static fromJson(json: Record<string, unknown>): OrderProductsImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new OrderProductsImpl({
      ...baseModel,
      orderId: JsonUtil.stringFromJson(json.orderId) || '',
      items: (json.items as OrderItem[]) || [],
      totalQuantity: JsonUtil.numberFromJson(json.totalQuantity) || 0,
      totalAmount: JsonUtil.numberFromJson(json.totalAmount) || 0,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      orderId: this.orderId,
      items: this.items,
      totalQuantity: this.totalQuantity,
      totalAmount: this.totalAmount,
    };
  }

  addItem(item: OrderItem): void {
    this.items.push(item);
    this.recalculateTotals();
  }

  removeItem(productId: string): void {
    this.items = this.items.filter(item => item.productId !== productId);
    this.recalculateTotals();
  }

  updateItemQuantity(productId: string, quantity: number): void {
    const item = this.items.find(item => item.productId === productId);
    if (item) {
      item.quantity = quantity;
      item.subtotal = item.price * quantity;
      this.recalculateTotals();
    }
  }

  private recalculateTotals(): void {
    this.totalQuantity = this.items.reduce((sum, item) => sum + item.quantity, 0);
    this.totalAmount = this.items.reduce((sum, item) => sum + item.subtotal, 0);
  }

  getFormattedTotalAmount(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: Currency.CAD
    }).format(this.totalAmount);
  }

  getItemByProductId(productId: string): OrderItem | undefined {
    return this.items.find(item => item.productId === productId);
  }

  getUniqueProductCount(): number {
    return this.items.length;
  }
}

/**
 * 订单服务类
 */
export class OrderService {
  /**
   * 从 API 响应创建订单列表
   */
  static createOrdersFromApiResponse(response: unknown[]): OrderImpl[] {
    return response
      .filter((item): item is Record<string, unknown> => 
        typeof item === 'object' && item !== null
      )
      .map(item => OrderImpl.fromJson(item));
  }

  /**
   * 从 API 响应创建跟踪记录列表
   */
  static createTrackingRecordsFromApiResponse(response: unknown[]): TrackingRecordImpl[] {
    return response
      .filter((item): item is Record<string, unknown> => 
        typeof item === 'object' && item !== null
      )
      .map(item => TrackingRecordImpl.fromJson(item));
  }

  /**
   * 从 API 响应创建订单商品列表
   */
  static createOrderProductsFromApiResponse(response: unknown[]): OrderProductsImpl[] {
    return response
      .filter((item): item is Record<string, unknown> => 
        typeof item === 'object' && item !== null
      )
      .map(item => OrderProductsImpl.fromJson(item));
  }

  /**
   * 搜索订单
   */
  static searchOrders(orders: Order[], query: string): Order[] {
    const lowerQuery = query.toLowerCase();
    return orders.filter(order => 
      order.sid?.toLowerCase().includes(lowerQuery) ||
      order.customerId.toLowerCase().includes(lowerQuery) ||
      order.paymentId.toLowerCase().includes(lowerQuery) ||
      order.orderPromotionCode?.toLowerCase().includes(lowerQuery)
    );
  }

  /**
   * 按状态过滤订单
   */
  static getOrdersByStatus(orders: Order[], status: OrderStatus): Order[] {
    return orders.filter(order => order.orderStatus === status);
  }

  /**
   * 按支付状态过滤订单
   */
  static getOrdersByPaymentStatus(orders: Order[], paymentStatus: PaymentStatus): Order[] {
    return orders.filter(order => order.paymentStatus === paymentStatus);
  }

  /**
   * 按配送状态过滤订单
   */
  static getOrdersByDeliveryStatus(orders: Order[], deliveryStatus: DeliveryStatus): Order[] {
    return orders.filter(order => order.deliveryStatus === deliveryStatus);
  }

  /**
   * 获取需要配送的订单
   */
  static getDeliveryOrders(orders: Order[]): Order[] {
    return orders.filter(order => order.delivery === true);
  }

  /**
   * 获取客户的订单
   */
  static getOrdersByCustomer(orders: Order[], customerId: string): Order[] {
    return orders.filter(order => order.customerId === customerId);
  }

  /**
   * 获取店铺的订单
   */
  static getOrdersByStore(orders: Order[], storeId: string): Order[] {
    return orders.filter(order => order.storeId === storeId);
  }

  /**
   * 按日期范围过滤订单
   */
  static getOrdersByDateRange(orders: Order[], startDate: Date, endDate: Date): Order[] {
    return orders.filter(order => {
      if (!order.createdAt) return false;
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDate && orderDate <= endDate;
    });
  }

  /**
   * 计算订单总金额
   */
  static calculateOrdersTotal(orders: Order[]): number {
    return orders.reduce((sum, order) => sum + order.orderTotal, 0);
  }

  /**
   * 计算订单实际支付总额
   */
  static calculateOrdersPaymentTotal(orders: Order[]): number {
    return orders.reduce((sum, order) => sum + order.orderPaymentPrice, 0);
  }

  /**
   * 获取订单统计信息
   */
  static getOrderStatistics(orders: Order[]): {
    total: number;
    pending: number;
    completed: number;
    rejected: number;
    refunded: number;
    totalAmount: number;
    totalPaymentAmount: number;
  } {
    return {
      total: orders.length,
      pending: orders.filter(o => o.orderStatus === OrderStatus.PENDING).length,
      completed: orders.filter(o => o.orderStatus === OrderStatus.COMPLETED).length,
      rejected: orders.filter(o => o.orderStatus === OrderStatus.REJECTED).length,
      refunded: orders.filter(o => o.orderStatus === OrderStatus.REFUNDED).length,
      totalAmount: this.calculateOrdersTotal(orders),
      totalPaymentAmount: this.calculateOrdersPaymentTotal(orders),
    };
  }

  /**
   * 验证订单数据
   */
  static validateOrder(order: Order): string[] {
    const errors: string[] = [];
    
    if (!order.customerId) {
      errors.push('客户 ID 是必需的');
    }
    
    if (order.orderTotal <= 0) {
      errors.push('订单总额必须大于 0');
    }
    
    if (order.orderPaymentPrice < 0) {
      errors.push('支付金额不能为负数');
    }
    
    if (order.orderPaymentPrice > order.orderTotal) {
      errors.push('支付金额不能超过订单总额');
    }
    
    if (order.delivery && !order.address) {
      errors.push('配送订单必须提供配送地址');
    }
    
    return errors;
  }

  /**
   * 生成订单摘要
   */
  static generateOrderSummary(order: OrderImpl): string {
    const statusText = order.isCompleted() ? '已完成' : '进行中';
    const deliveryText = order.requiresDelivery() ? '需要配送' : '店内取货';
    return `订单 ${order.sid} - ${statusText} - ${deliveryText} - ${order.getFormattedTotal()}`;
  }
} 