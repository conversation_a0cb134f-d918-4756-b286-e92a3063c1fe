import React from "react";
import "./SectionComponentNode.css";

export const SectionComponentNode = (): JSX.Element => {
  return (
    <div className="section-component-node">
      <div className="heading-loved-by">Loved by Pet Parents</div>

      <div className="background-shadow">
        <p className="onenata-has-been-a">
          &#34;<PERSON><PERSON><PERSON> has been a game-changer for
          <br />
          managing fluffy&#39;s health. The AI insights
          <br />
          are surprisingly accurate, and I love the vet report feature!&#34;
        </p>

        <div className="sarah-k" />

        <div className="text-wrapper-7"><PERSON>.</div>

        <div className="text-wrapper-8">Verified User</div>
      </div>

      <div className="background-shadow-2">
        <p className="finding-pet-friendly">
          &#34;Finding pet-friendly spots used to be a<br />
          hassle. Now, with <PERSON><PERSON><PERSON>&#39;s map, it&#39;s super
          <br />
          easy. Plus, connecting with other dog
          <br />
          owners in my area is great.&#34;
        </p>

        <div className="mark-l" />

        <div className="text-wrapper-9">Mark L.</div>

        <div className="text-wrapper-8">Verified User</div>
      </div>

      <div className="background-shadow-3">
        <p className="i-was-skeptical">
          &#34;I was skeptical about another pet app, but
          <br />
          OneNata truly combines everything I need.
          <br />
          From tracking walks to sharing cute pics,
          <br />
          it&#39;s all there.&#34;
        </p>

        <div className="emily-p" />

        <div className="text-wrapper-10">Emily P.</div>

        <div className="text-wrapper-8">Verified User</div>
      </div>
    </div>
  );
};

