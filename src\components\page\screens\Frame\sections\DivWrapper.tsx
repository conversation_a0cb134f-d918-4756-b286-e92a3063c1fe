import React from "react";
import "./DivWrapper.css";

export const DivWrapper = (): JSX.Element => {
  return (
    <div className="div-wrapper">
      <div className="keep-track-of-your-wrapper">
        <p className="keep-track-of-your">
          Keep track of your pet&#39;s
          <br />
          health
        </p>
      </div>

      <p className="log-health-details-2">
        Log health details by hand or sync from devices, view well-being
        <br />
        indexes based on AI, and share reports with your vet.
      </p>

      <div className="list-2">
        <div className="item-2">
          <div className="text-wrapper-3">check_circle</div>

          <p className="text-wrapper-4">
            Manually log vaccinations, medications, vet visits
          </p>
        </div>

        <div className="item-2">
          <div className="text-wrapper-3">check_circle</div>

          <p className="text-wrapper-4">
            Sync data from smart collars, feeders, or water fountains
          </p>
        </div>

        <div className="item-2">
          <div className="text-wrapper-3">check_circle</div>

          <p className="text-wrapper-4">
            AI-powered insights into your pet&#39;s activity and rest
          </p>
        </div>

        <div className="item-2">
          <div className="text-wrapper-3">check_circle</div>

          <p className="text-wrapper-4">
            Export wellness reports to share with your vet
          </p>
        </div>
      </div>
    </div>
  );
};

