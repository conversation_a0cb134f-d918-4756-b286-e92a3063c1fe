'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';
import { CustomDatePicker } from '../../components/ui/CustomDatePicker';
import { PhotoUpload } from '../../components/ui/PhotoUpload';
import { PhoneNumberInput, validatePhoneNumber } from '../../components/ui/PhoneNumberInput';
import { useAuth, AuthenticatedRoute } from '../../../lib/firebase/context/AuthContext';
import { useTheme } from '../../../lib/firebase/context/ThemeContext';
import { UserPreferences } from '../../../lib/models/types';
import { FirestoreService } from "../../../lib/firebase/services";
import { 
  FiArrowLeft, 
  FiUser, 
  FiMail, 
  FiCalendar,
  FiFileText,
  FiSettings,
  FiSave,
  FiX
} from 'react-icons/fi';

export default function ProfileEditPage() {
  return (
    <AuthenticatedRoute>
      <ProfileEditContent />
    </AuthenticatedRoute>
  );
}

function ProfileEditContent() {
  const params = useParams();
  const router = useRouter();
  const { userData, user, refreshUser } = useAuth();
  const { setTheme } = useTheme();
  const locale = params.locale as string;
  
  // 翻译
  const t = useTranslations('profileEdit');

  const [formData, setFormData] = useState({
    firstName: userData?.firstName || '',
    lastName: userData?.lastName || '',
    displayName: userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim(),
    email: user?.email || '',
    phoneNumber: userData?.phoneNumber || '',
    dateOfBirth: userData?.dateOfBirth ? new Date(userData.dateOfBirth).toISOString().split('T')[0] : '',
    bio: userData?.bio || '',
    photoURL: userData?.photoURL || '',
    preferences: {
      notificationsEnabled: userData?.preferences?.notificationsEnabled ?? true,
      language: userData?.preferences?.language || locale,
      theme: userData?.preferences?.theme || 'system',
      emailNotifications: userData?.preferences?.emailNotifications ?? true,
      smsNotifications: userData?.preferences?.smsNotifications ?? true,
    } as UserPreferences,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // 监听表单变化
  useEffect(() => {
    const originalData = {
      firstName: userData?.firstName || '',
      lastName: userData?.lastName || '',
      displayName: userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim(),
      email: user?.email || '',
      phoneNumber: userData?.phoneNumber || '',
      dateOfBirth: userData?.dateOfBirth ? new Date(userData.dateOfBirth).toISOString().split('T')[0] : '',
      bio: userData?.bio || '',
      photoURL: userData?.photoURL || '',
      preferences: {
        notificationsEnabled: userData?.preferences?.notificationsEnabled ?? true,
        language: userData?.preferences?.language || locale,
        theme: userData?.preferences?.theme || 'system',
        emailNotifications: userData?.preferences?.emailNotifications ?? true,
        smsNotifications: userData?.preferences?.smsNotifications ?? true,
      },
    };

    const hasChanged = JSON.stringify(formData) !== JSON.stringify(originalData);
    setHasChanges(hasChanged);
  }, [formData, userData, user, locale]);



  // 处理电话号码输入
  const handlePhoneChange = (value: string) => {
    // PhoneNumberInput 组件已经处理了格式化，直接使用
    setFormData({ ...formData, phoneNumber: value });
    
    // 清除错误
    if (errors.phoneNumber) {
      setErrors({ ...errors, phoneNumber: '' });
    }
  };

  // 处理照片上传成功
  const handlePhotoUploaded = (photoURL: string) => {
    setFormData({ ...formData, photoURL });
    setErrors({ ...errors, photo: '' });
  };

  // 处理照片上传错误
  const handlePhotoError = (error: string) => {
    setErrors({ ...errors, photo: error });
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = t('validation.firstNameRequired');
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('validation.lastNameRequired');
    }
    if (!formData.email.trim()) {
      newErrors.email = t('validation.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('validation.emailInvalid');
    }
    if (formData.phoneNumber && formData.phoneNumber.trim() !== '+1 ' && !validatePhoneNumber(formData.phoneNumber)) {
      newErrors.phoneNumber = t('validation.phoneInvalid');
    }

    // 验证出生日期不能超过今天
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (birthDate > today) {
        newErrors.dateOfBirth = t('validation.futureDateError');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      if (!user?.uid) {
        setErrors({ general: 'User not authenticated' });
        setIsLoading(false);
        return;
      }

      // 准备更新数据
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        displayName: formData.displayName || `${formData.firstName} ${formData.lastName}`.trim(),
        email: formData.email,
        phoneNumber: formData.phoneNumber,
        dateOfBirth: formData.dateOfBirth,
        bio: formData.bio,
        photoURL: formData.photoURL,
        preferences: formData.preferences,
      };

      // 获取用户数据文档
      const userDataQuery = await FirestoreService.getMany(
        'portal-user-data',
        {
          where: [{ field: 'fid', operator: '==', value: user.uid }],
          limit: 1
        }
      );

      if (!userDataQuery.success || !userDataQuery.data || userDataQuery.data.length === 0) {
        setErrors({ general: 'User data not found' });
        setIsLoading(false);
        return;
      }

      const userDataDoc = userDataQuery.data[0];
      const docId = userDataDoc.id;

      if (!docId) {
        setErrors({ general: 'User document ID not found' });
        setIsLoading(false);
        return;
      }

      // 更新用户资料
      const result = await FirestoreService.update(
        'portal-user-data',
        docId,
        updateData
      );

      if (result.success) {
        // 刷新用户数据
        await refreshUser();
        // 返回dashboard
        router.back();
      } else {
        setErrors({ general: result.error || t('error') });
      }
    } catch (error) {
      console.error('Profile update error:', error);
      setErrors({ general: t('error') });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (hasChanges) {
      if (window.confirm(t('confirmLeave'))) {
        router.back();
      }
    } else {
      router.back();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* 头部 */}
        <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl p-6 border border-white/30 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={handleCancel}
                className="flex items-center text-slate-700 hover:text-slate-900 mr-4 p-2 rounded-xl hover:bg-slate-100/80 transition-all duration-200"
              >
                <FiArrowLeft className="w-5 h-5 mr-1" />
                <span className="font-medium">{t('backButton')}</span>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900">{t('title')}</h1>
                <p className="text-slate-600 font-medium">{t('subtitle')}</p>
              </div>
            </div>
            
            {hasChanges && (
              <div className="flex items-center text-amber-700 bg-amber-100/80 px-4 py-2 rounded-xl border border-amber-200">
                <span className="text-sm font-semibold">{t('unsavedChanges')}</span>
              </div>
            )}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* 基本信息卡片 */}
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl p-8 border border-white/30">
            <div className="flex items-center mb-6">
              <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl mr-3">
                <FiUser className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-xl font-bold text-slate-900">{t('basicInfo')}</h2>
            </div>

            {/* 错误消息 */}
            {errors.general && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl">
                <p className="text-red-700 text-sm font-medium">{errors.general}</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 头像上传 */}
              <div className="md:col-span-2">
                <label className="block text-sm font-semibold text-slate-800 mb-3">
                  {t('avatar')}
                </label>
                <PhotoUpload
                  userId={user?.uid || ''}
                  currentPhotoURL={formData.photoURL}
                  onPhotoUploaded={handlePhotoUploaded}
                  onError={handlePhotoError}
                  className="w-32 h-32"
                  size="large"
                  userName={formData.displayName || `${formData.firstName} ${formData.lastName}`.trim()}
                />
                {errors.photo && (
                  <p className="mt-2 text-sm text-red-600 font-medium">{errors.photo}</p>
                )}
              </div>

              {/* 名字 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('firstName')} *
                </label>
                <div className="relative">
                  <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5" />
                  <Input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                    className="pl-10 bg-white/80 border-slate-300 text-slate-900 placeholder-slate-500"
                    placeholder={t('firstNamePlaceholder')}
                    error={errors.firstName}
                  />
                </div>
              </div>

              {/* 姓氏 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('lastName')} *
                </label>
                <div className="relative">
                  <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5" />
                  <Input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                    className="pl-10 bg-white/80 border-slate-300 text-slate-900 placeholder-slate-500"
                    placeholder={t('lastNamePlaceholder')}
                    error={errors.lastName}
                  />
                </div>
              </div>

              {/* 显示名称 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('displayName')}
                </label>
                <div className="relative">
                  <FiUser className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5" />
                  <Input
                    type="text"
                    value={formData.displayName}
                    onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                    className="pl-10 bg-white/80 border-slate-300 text-slate-900 placeholder-slate-500"
                    placeholder={t('displayNamePlaceholder')}
                  />
                </div>
              </div>

              {/* 邮箱 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('email')} *
                </label>
                <div className="relative">
                  <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5" />
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="pl-10 bg-white/80 border-slate-300 text-slate-900 placeholder-slate-500"
                    placeholder={t('emailPlaceholder')}
                    error={errors.email}
                  />
                </div>
              </div>

              {/* 电话号码 */}
              <PhoneNumberInput
                label={t('phoneNumber')}
                value={formData.phoneNumber}
                onChange={(value) => handlePhoneChange(value)}
                placeholder={t('phoneNumberPlaceholder')}
                error={errors.phoneNumber}
                className="bg-white/80 border-slate-300 text-slate-900 placeholder-slate-500"
              />

              {/* 出生日期 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('dateOfBirth')}
                </label>
                <div className="relative">
                  <FiCalendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5 z-10" />
                  <CustomDatePicker
                    value={formData.dateOfBirth}
                    onChange={(value) => {
                      setFormData({
                        ...formData,
                        dateOfBirth: value
                      });
                    }}
                    className="pl-10 bg-white/80 border-slate-300 text-slate-900"
                    error={errors.dateOfBirth}
                  />
                </div>
              </div>
            </div>

            {/* 个人简介 */}
            <div className="mt-6">
              <label className="block text-sm font-semibold text-slate-800 mb-2">
                {t('bio')}
              </label>
              <div className="relative">
                <FiFileText className="absolute left-3 top-3 text-slate-500 w-5 h-5" />
                <textarea
                  value={formData.bio}
                  onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                  className="w-full pl-10 pr-4 py-3 bg-white/80 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 resize-none text-slate-900 placeholder-slate-500"
                  placeholder={t('bioPlaceholder')}
                  rows={4}
                />
              </div>
            </div>
          </div>

          {/* 偏好设置卡片 */}
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl p-8 border border-white/30">
            <div className="flex items-center mb-6">
              <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-xl mr-3">
                <FiSettings className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-xl font-bold text-slate-900">{t('preferences')}</h2>
            </div>

            <div className="space-y-6">
              {/* 语言设置 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('language')}
                </label>
                <select
                  value={formData.preferences.language}
                  onChange={(e) => setFormData({
                    ...formData,
                    preferences: { ...formData.preferences, language: e.target.value }
                  })}
                  className="w-full px-4 py-3 bg-white/80 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
                >
                  <option value="zh-CN">中文</option>
                  <option value="en">English</option>
                </select>
              </div>

              {/* 主题设置 */}
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('theme')}
                </label>
                <select
                  value={formData.preferences.theme}
                  onChange={(e) => {
                    const newTheme = e.target.value as 'light' | 'dark' | 'system';
                    setFormData({
                      ...formData,
                      preferences: { ...formData.preferences, theme: newTheme }
                    });
                    // 立即应用主题变化
                    setTheme(newTheme);
                  }}
                  className="w-full px-4 py-3 bg-white/80 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
                >
                  <option value="system">{t('themeSystem')}</option>
                  <option value="light">{t('themeLight')}</option>
                  <option value="dark">{t('themeDark')}</option>
                </select>
              </div>

              {/* 通知设置 */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-slate-900">{t('notificationSettings')}</h3>
                
                <div className="flex items-center justify-between p-4 bg-slate-50/80 rounded-xl border border-slate-200">
                  <div>
                    <label className="text-sm font-semibold text-slate-800">{t('enableNotifications')}</label>
                    <p className="text-xs text-slate-600 font-medium">{t('enableNotificationsDesc')}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.preferences.notificationsEnabled}
                    onChange={(e) => setFormData({
                      ...formData,
                      preferences: { ...formData.preferences, notificationsEnabled: e.target.checked }
                    })}
                    className="w-5 h-5 text-violet-600 border-2 border-slate-400 rounded focus:ring-violet-500 bg-white"
                  />
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-50/80 rounded-xl border border-slate-200">
                  <div>
                    <label className="text-sm font-semibold text-slate-800">{t('emailNotifications')}</label>
                    <p className="text-xs text-slate-600 font-medium">{t('emailNotificationsDesc')}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.preferences.emailNotifications}
                    onChange={(e) => setFormData({
                      ...formData,
                      preferences: { ...formData.preferences, emailNotifications: e.target.checked }
                    })}
                    className="w-5 h-5 text-violet-600 border-2 border-slate-400 rounded focus:ring-violet-500 bg-white"
                  />
                </div>

                <div className="flex items-center justify-between p-4 bg-slate-50/80 rounded-xl border border-slate-200">
                  <div>
                    <label className="text-sm font-semibold text-slate-800">{t('smsNotifications')}</label>
                    <p className="text-xs text-slate-600 font-medium">{t('smsNotificationsDesc')}</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={formData.preferences.smsNotifications}
                    onChange={(e) => setFormData({
                      ...formData,
                      preferences: { ...formData.preferences, smsNotifications: e.target.checked }
                    })}
                    className="w-5 h-5 text-violet-600 border-2 border-slate-400 rounded focus:ring-violet-500 bg-white"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-xl p-6 border border-white/30">
            <div className="flex flex-col sm:flex-row gap-4 justify-end">
              <Button
                type="button"
                onClick={handleCancel}
                variant="secondary"
                className="flex items-center justify-center space-x-2 bg-slate-200 hover:bg-slate-300 text-slate-800 border-slate-300"
              >
                <FiX className="w-4 h-4" />
                <span className="font-semibold">{t('cancelButton')}</span>
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !hasChanges}
                className="flex items-center justify-center space-x-2 min-w-[120px] bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <>
                    <FiSave className="w-4 h-4" />
                    <span>{t('saveButton')}</span>
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
} 