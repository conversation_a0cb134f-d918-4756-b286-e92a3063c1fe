# OneNata Admin Portal - 开发者文档

## 📋 目录

1. [项目概述](#项目概述)
2. [技术架构](#技术架构)
3. [项目结构](#项目结构)
4. [环境设置](#环境设置)
5. [业务逻辑](#业务逻辑)
6. [开发指南](#开发指南)
7. [部署指南](#部署指南)
8. [故障排除](#故障排除)
9. [API 文档](#api-文档)
10. [数据库设计](#数据库设计)

---

## 🎯 项目概述

### 项目简介

OneNata Admin Portal 是一个基于 Next.js 和 Firebase 的宠物店管理系统，提供完整的 CRM 功能，包括用户管理、店铺管理、预约系统、客户管理等核心业务功能。

### 核心功能

- **用户认证系统** - Firebase Authentication 集成
- **店铺管理** - 完整的店铺 CRUD 操作
- **预约系统** - 智能预约管理和时间调度
- **客户管理** - 客户档案和关系管理
- **员工管理** - 员工排班和服务分配
- **库存管理** - 商品和库存跟踪
- **数据分析** - 业务统计和报表
- **国际化支持** - 多语言界面

### 技术栈

- **前端**: Next.js 15 (App Router), React 19, TypeScript
- **样式**: Tailwind CSS 4
- **认证**: Firebase Authentication
- **数据库**: Firebase Firestore
- **存储**: Firebase Storage
- **函数**: Firebase Cloud Functions
- **国际化**: next-intl
- **开发工具**: ESLint, Firebase Emulator

---

## 🏗️ 技术架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App  │    │  Firebase Auth  │    │   Firestore     │
│   (Frontend)   │◄──►│   (Auth)        │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Cloud Functions │    │  Storage        │    │  Google Maps    │
│   (Backend)    │    │  (Files)        │    │   (Location)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 分层架构

1. **表示层 (Presentation Layer)**
   - Next.js App Router
   - React Components
   - Tailwind CSS UI

2. **业务逻辑层 (Business Logic Layer)**
   - Services (`src/app/lib/services/`)
   - Models (`src/app/lib/models/`)
   - Controllers (`src/app/lib/controllers/`)

3. **数据访问层 (Data Access Layer)**
   - Firebase SDK
   - FirestoreService
   - StorageService

4. **基础设施层 (Infrastructure Layer)**
   - Firebase Authentication
   - Firebase Firestore
   - Firebase Storage
   - Cloud Functions

### 数据流

```
用户操作 → React Component → Service Layer → Firebase SDK → Firestore
                ↓
        状态管理 → UI 更新 → 用户反馈
```

---

## 📁 项目结构

```
onenata_admin/
├── src/
│   ├── app/
│   │   ├── [locale]/                    # 国际化路由
│   │   │   ├── auth/                    # 认证页面
│   │   │   │   ├── login/              # 登录
│   │   │   │   ├── signup/             # 注册
│   │   │   │   ├── verify-email/       # 邮箱验证
│   │   │   │   ├── profile-setup/      # 资料设置
│   │   │   │   └── profile-edit/       # 资料编辑
│   │   │   ├── dashboard/              # 仪表板
│   │   │   │   ├── business/           # 商业用户仪表板
│   │   │   │   └── internal/           # 内部用户仪表板
│   │   │   ├── store/                  # 店铺管理
│   │   │   │   ├── create/             # 创建店铺
│   │   │   │   ├── [id]/               # 店铺详情
│   │   │   │   │   ├── appointments/   # 预约管理
│   │   │   │   │   ├── customers/      # 客户管理
│   │   │   │   │   ├── services/       # 服务管理
│   │   │   │   │   └── staff/          # 员工管理
│   │   │   │   └── edit/               # 编辑店铺
│   │   │   ├── admin/                  # 管理员功能
│   │   │   │   └── store-approval/     # 店铺审批
│   │   │   └── components/             # 共享组件
│   │   │       ├── ui/                 # UI 组件
│   │   │       ├── notifications/      # 通知组件
│   │   │       └── LanguageSelector.tsx
│   │   ├── api/                        # API 路由
│   │   │   └── google-maps/            # Google Maps API
│   │   ├── globals.css                 # 全局样式
│   │   ├── layout.tsx                  # 根布局
│   │   └── page.tsx                    # 首页
│   ├── lib/
│   │   ├── firebase/                   # Firebase 配置
│   │   │   ├── config.ts              # Firebase 配置
│   │   │   ├── admin.ts               # Admin SDK
│   │   │   ├── emulators.ts           # 模拟器配置
│   │   │   ├── context/               # React Context
│   │   │   │   ├── AuthContext.tsx    # 认证上下文
│   │   │   │   └── ThemeContext.tsx   # 主题上下文
│   │   │   └── services/              # Firebase 服务
│   │   │       ├── auth.ts            # 认证服务
│   │   │       ├── firestore.ts       # Firestore 服务
│   │   │       ├── storage.ts         # Storage 服务
│   │   │       └── index.ts           # 服务导出
│   │   ├── models/                     # 数据模型
│   │   │   ├── appointment.ts         # 预约模型
│   │   │   ├── customer.ts            # 客户模型
│   │   │   ├── store.ts               # 店铺模型
│   │   │   ├── staff.ts               # 员工模型
│   │   │   └── types.ts               # 类型定义
│   │   ├── services/                   # 业务服务
│   │   │   ├── appointment_service.ts # 预约服务
│   │   │   ├── customer_service.ts    # 客户服务
│   │   │   ├── store_services.ts      # 店铺服务
│   │   │   ├── staff_services.ts      # 员工服务
│   │   │   └── google_map_services.ts # 地图服务
│   │   ├── types/                      # 类型定义
│   │   │   └── common.ts              # 通用类型
│   │   └── utils/                      # 工具函数
│   │       ├── permissions.ts         # 权限控制
│   │       └── validations.ts         # 数据验证
│   └── i18n/                          # 国际化
│       ├── navigation.ts              # 导航翻译
│       ├── request.ts                 # 请求翻译
│       └── routing.ts                 # 路由翻译
├── messages/                          # 翻译文件
│   ├── en.json                       # 英文翻译
│   └── zh-CN.json                    # 中文翻译
├── public/                           # 静态资源
│   ├── images/                       # 图片资源
│   └── onenata/                      # OneNata 品牌资源
├── functions/                        # Firebase Functions
│   ├── src/                         # 函数源码
│   ├── package.json                 # 函数依赖
│   └── tsconfig.json               # TypeScript 配置
├── portal_cloud/                    # Portal Cloud Functions
│   ├── src/                        # 函数源码
│   ├── package.json                # 函数依赖
│   └── tsconfig.json              # TypeScript 配置
├── dataconnect/                     # Firebase Data Connect
├── scripts/                         # 脚本文件
├── .docs/                          # 项目文档
├── .cursor/                        # 开发文档
├── firebase.json                   # Firebase 配置
├── firestore.rules                 # Firestore 安全规则
├── firestore.indexes.json          # Firestore 索引
├── storage.rules                   # Storage 安全规则
├── next.config.ts                  # Next.js 配置
├── tsconfig.json                   # TypeScript 配置
├── package.json                    # 项目依赖
└── README.md                       # 项目说明
```

---

## ⚙️ 环境设置

### 1. 系统要求

- **Node.js**: 18.x 或更高版本
- **npm**: 9.x 或更高版本
- **Firebase CLI**: 最新版本
- **Git**: 版本控制

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd onenata_admin

# 安装依赖
npm install

# 安装 Firebase CLI (如果未安装)
npm install -g firebase-tools
```

### 3. 环境变量配置

创建 `.env.local` 文件：

```bash
# Firebase 配置
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# 开发环境模拟器
NEXT_PUBLIC_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
NEXT_PUBLIC_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199
NEXT_PUBLIC_FIREBASE_FUNCTIONS_EMULATOR_HOST=localhost:5001

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# 环境标识
NODE_ENV=development
```

### 4. Firebase 项目设置

```bash
# 登录 Firebase
firebase login

# 初始化项目
firebase init

# 选择功能：
# - Firestore
# - Functions
# - Storage
# - Emulators
```

### 5. 启动开发环境

```bash
# 启动 Firebase 模拟器
firebase emulators:start --import=./emulators-data --export-on-exit=./emulators-data

# 新终端窗口启动开发服务器
npm run dev
```

### 6. 验证安装

```bash
# 测试 Firebase 配置
npm run test:firebase

# 测试认证流程
npm run test:auth

# 代码检查
npm run lint
```

---

## 🏢 业务逻辑

### 1. 用户管理系统

#### 用户类型

```typescript
enum UserType {
  ONENATA_ADMIN = '101',           // OneNata 管理员
  PETSTORE_OWNER = '201',          // 宠物店店主
  PETSTORE_ADMIN = '202',          // 宠物店管理员
  PETSTORE_STAFF = '203',          // 宠物店员工
  PETSTORE_CUSTOMER_FROM_PORTAL = '204', // Portal 创建的客户
  PETSTORE_CUSTOMER_FROM_APP = '205'     // App 创建的客户
}
```

#### 认证流程

1. **注册流程**
   ```
   用户注册 → 邮箱验证 → 资料设置 → 权限分配 → 完成注册
   ```

2. **登录流程**
   ```
   用户登录 → Firebase 验证 → 获取用户数据 → 权限检查 → 跳转仪表板
   ```

3. **权限控制**
   ```typescript
   // 基于用户类型的权限控制
   const canCreateStore = userData?.userType === UserType.PETSTORE_OWNER;
   const canManageStaff = userData?.userType === UserType.PETSTORE_ADMIN;
   ```

### 2. 店铺管理系统

#### 店铺创建流程

```typescript
interface CreateStoreData {
  storeName: string;
  businessType: BusinessType;
  description?: string;
  phone: string;
  email: string;
  website?: string;
  currentAddress: Address;
  services: ServiceTypes;
  businessHours?: BusinessHours;
  googlePlaceId?: string;
  avatarUrl?: string;
  storePhotos?: string[];
}
```

#### 店铺审批流程

1. **提交申请** - 店主创建店铺
2. **管理员审核** - 内部管理员审核
3. **状态更新** - 批准/拒绝
4. **通知用户** - 邮件/推送通知

### 3. 预约管理系统

#### 预约状态流程

```typescript
enum AppointmentStatus {
  DRAFT = 'draft',           // 草稿
  CONFIRMED = 'confirmed',   // 已确认
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed',   // 已完成
  CANCELLED = 'cancelled',   // 已取消
  NO_SHOW = 'no_show',      // 未到场
  LAPSED = 'lapsed'         // 已过期
}
```

#### 预约创建流程

1. **选择客户和宠物**
2. **选择服务类别**
3. **选择具体服务**
4. **选择员工**
5. **选择日期**
6. **选择时间**
7. **确认预约**

#### 时间管理系统

- **15分钟时间块** - 所有时间以15分钟为单位
- **员工排班** - 基于员工工作时间
- **冲突检测** - 自动检查时间冲突
- **可用性计算** - 实时计算可用时间段

### 4. 客户管理系统

#### 客户来源

```typescript
enum CustomerSource {
  ONENATA_APP = 'onenata_app',     // OneNata App
  PORTAL_CREATED = 'portal_created', // Portal 创建
  WALK_IN = 'walk_in'              // 到店客户
}
```

#### 客户创建流程

```typescript
// 8步客户创建流程
async function createCustomer(data: CreateCustomerAccountRequest) {
  // 1. 验证输入数据
  // 2. 检查邮箱是否存在
  // 3. 检查电话号码是否存在
  // 4. 验证密码
  // 5. 创建 Firebase 账户
  // 6. 创建用户数据
  // 7. 添加到店铺客户列表
  // 8. 完成
}
```

### 5. 员工管理系统

#### 员工角色

```typescript
enum StoreRole {
  OWNER = 'owner',       // 店主
  ADMIN = 'admin',       // 管理员
  STAFF = 'staff'        // 员工
}
```

#### 员工服务分配

```typescript
interface EmployeeService {
  serviceId: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  serviceAmount: number;      // 服务价格
  minDuration: number;        // 最短时间
  maxDuration: number;        // 最长时间
  defaultDuration: number;    // 默认时间
}
```

### 6. 服务管理系统

#### 服务类别

```typescript
enum ServiceCategory {
  GROOMING = 'grooming',       // 美容护理
  VETERINARY = 'veterinary',   // 兽医服务
  TRAINING = 'training',       // 训练服务
  BOARDING = 'boarding',       // 寄养服务
  DAY_CARE = 'day_care',       // 日间护理
  MOBILE_SERVICE = 'mobile_service' // 上门服务
}
```

#### 店铺服务管理

```typescript
interface StoreService {
  serviceId: string;
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  commission: number;              // 佣金比例
  isOnlineBookingEnabled: boolean; // 是否支持在线预约
  requiresApproval: boolean;       // 是否需要审批
  staffIds: string[];             // 提供服务的员工列表
}
```

---

## 🛠️ 开发指南

### 1. 代码规范

#### TypeScript 规范

```typescript
// 接口命名使用 PascalCase
interface UserData {
  uid: string;
  email: string;
  displayName: string;
}

// 函数命名使用 camelCase
async function createUser(userData: UserData): Promise<string> {
  // 实现
}

// 常量使用 UPPER_SNAKE_CASE
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_TIMEOUT = 5000;
```

#### React 组件规范

```typescript
// 组件使用 PascalCase
interface UserProfileProps {
  userId: string;
  onUpdate?: (user: UserData) => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId, onUpdate }) => {
  // 组件实现
};
```

#### 文件命名规范

- **组件文件**: `PascalCase.tsx`
- **服务文件**: `snake_case.ts`
- **类型文件**: `types.ts`
- **工具文件**: `utils.ts`

### 2. 状态管理

#### 使用 React Context

```typescript
// 认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<PortalUserData | null>(null);
  
  // 提供认证状态和方法
  const value = {
    user,
    userData,
    signIn,
    signOut,
    refreshUser
  };
  
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
```

#### 使用 React Hooks

```typescript
// 自定义 Hook
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
```

### 3. 错误处理

#### 统一错误处理

```typescript
// 错误类型定义
interface AppError {
  code: string;
  message: string;
  details?: any;
}

// 错误处理函数
const handleError = (error: any): AppError => {
  if (error.code === 'auth/user-not-found') {
    return {
      code: 'USER_NOT_FOUND',
      message: '用户不存在'
    };
  }
  
  return {
    code: 'UNKNOWN_ERROR',
    message: '未知错误'
  };
};
```

#### 异步错误处理

```typescript
// 使用 try-catch 处理异步操作
const createAppointment = async (data: AppointmentData) => {
  try {
    const result = await appointmentService.create(data);
    return { success: true, data: result };
  } catch (error) {
    const appError = handleError(error);
    return { success: false, error: appError };
  }
};
```

### 4. 数据验证

#### 表单验证

```typescript
// 验证规则
const validationRules = {
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入有效的邮箱地址'
  },
  phoneNumber: {
    required: false,
    pattern: /^\+1\s\d{3}\s\d{3}\s\d{4}$/,
    message: '请输入有效的电话号码'
  }
};

// 验证函数
const validateForm = (data: any): ValidationResult => {
  const errors: Record<string, string> = {};
  
  Object.keys(validationRules).forEach(field => {
    const rule = validationRules[field];
    const value = data[field];
    
    if (rule.required && !value) {
      errors[field] = rule.message;
    } else if (value && rule.pattern && !rule.pattern.test(value)) {
      errors[field] = rule.message;
    }
  });
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
```

### 5. 国际化

#### 翻译文件结构

```json
{
  "common": {
    "save": "保存",
    "cancel": "取消",
    "loading": "加载中..."
  },
  "auth": {
    "login": "登录",
    "signup": "注册",
    "email": "邮箱"
  }
}
```

#### 使用翻译

```typescript
import { useTranslations } from 'next-intl';

const MyComponent = () => {
  const t = useTranslations('common');
  
  return (
    <button>{t('save')}</button>
  );
};
```

### 6. 测试

#### 单元测试

```typescript
// 服务测试
describe('AppointmentService', () => {
  it('should create appointment successfully', async () => {
    const appointmentData = {
      storeId: 'store123',
      customerId: 'customer123',
      serviceId: 'service123',
      date: '2024-01-15',
      time: '10:00'
    };
    
    const result = await appointmentService.create(appointmentData);
    
    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();
  });
});
```

#### 集成测试

```typescript
// API 路由测试
describe('/api/appointments', () => {
  it('should create appointment via API', async () => {
    const response = await fetch('/api/appointments', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(appointmentData)
    });
    
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
  });
});
```

---

## 🚀 部署指南

### 1. 生产环境配置

#### 环境变量

```bash
# 生产环境变量
NEXT_PUBLIC_FIREBASE_API_KEY=your-production-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-production-domain.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-production-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-production-bucket.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-production-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-production-app-id

# 移除模拟器配置
# NEXT_PUBLIC_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
# NEXT_PUBLIC_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
# NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# 生产环境标识
NODE_ENV=production
```

#### Firebase 配置

```bash
# 部署 Firestore 规则
firebase deploy --only firestore:rules

# 部署 Storage 规则
firebase deploy --only storage

# 部署 Functions
firebase deploy --only functions

# 部署所有资源
firebase deploy
```

### 2. Vercel 部署

#### 配置 Vercel

1. **连接 GitHub 仓库**
2. **配置环境变量**
3. **设置构建命令**

```json
// vercel.json
{
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "functions": {
    "src/app/api/**/*.ts": {
      "runtime": "nodejs22.x"
    }
  },
  "env": {
    "NODE_ENV": "production"
  }
}
```

#### 部署步骤

```bash
# 1. 构建项目
npm run build

# 2. 测试构建结果
npm run start

# 3. 推送到 GitHub
git add .
git commit -m "Prepare for production deployment"
git push origin main

# 4. Vercel 自动部署
```

### 3. 数据库迁移

#### Firestore 索引

```json
// firestore.indexes.json
{
  "indexes": [
    {
      "collectionGroup": "appointment",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "storeId", "order": "ASCENDING" },
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "create_date", "order": "DESCENDING" }
      ]
    }
  ]
}
```

#### 安全规则

```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用户数据访问规则
    match /portal-user-data/{document} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.fid || 
         request.auth.uid == resource.data.created_by);
    }
    
    // 店铺数据访问规则
    match /store-info/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.staffs;
    }
  }
}
```

---

## 🔧 故障排除

### 1. 常见问题

#### Firebase 连接问题

```bash
# 检查 Firebase 配置
npm run test:firebase

# 检查模拟器状态
firebase emulators:start --only auth,firestore,storage

# 重置模拟器数据
firebase emulators:start --import=./emulators-data --export-on-exit=./emulators-data
```

#### 构建错误

```bash
# 清理缓存
rm -rf .next
rm -rf node_modules/.cache

# 重新安装依赖
rm -rf node_modules
npm install

# 重新构建
npm run build
```

#### 类型错误

```bash
# 检查 TypeScript 配置
npx tsc --noEmit

# 修复类型错误
npm run lint -- --fix
```

### 2. 调试技巧

#### 开发模式调试

```bash
# 启动调试模式
npm run debug

# 使用 Chrome DevTools 调试
# 打开 chrome://inspect
```

#### 日志调试

```typescript
// 添加调试日志
console.log('Debug info:', { user, userData });

// 使用 Firebase 日志
import * as logger from "firebase-functions/logger";
logger.info('Function called with data:', data);
```

#### 网络调试

```bash
# 检查 API 请求
# 使用浏览器开发者工具 Network 标签

# 检查 Firebase 请求
# 使用 Firebase Console 查看实时日志
```

### 3. 性能优化

#### 数据库查询优化

```typescript
// 使用复合索引
const appointments = await FirestoreService.getMany('appointment', {
  where: [
    { field: 'storeId', operator: '==', value: storeId },
    { field: 'status', operator: '==', value: 'confirmed' }
  ],
  orderBy: [{ field: 'create_date', direction: 'desc' }],
  limit: 20
});
```

#### 前端性能优化

```typescript
// 使用 React.memo 优化组件
const UserCard = React.memo(({ user }: UserCardProps) => {
  return <div>{user.name}</div>;
});

// 使用 useMemo 缓存计算结果
const filteredUsers = useMemo(() => {
  return users.filter(user => user.status === 'active');
}, [users]);
```

---

## 📚 API 文档

### 1. 认证 API

#### 登录

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "user": {
      "uid": "user123",
      "email": "<EMAIL>"
    },
    "token": "jwt-token"
  }
}
```

#### 注册

```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe"
}
```

### 2. 店铺 API

#### 创建店铺

```http
POST /api/stores
Content-Type: application/json

{
  "storeName": "Pet Store",
  "businessType": "pet_store",
  "phone": "****** 123 4567",
  "email": "<EMAIL>",
  "currentAddress": {
    "addressLine1": "123 Main St",
    "city": "Vancouver",
    "province": "BC",
    "country": "Canada",
    "postCode": "V6B 1A1"
  }
}
```

#### 获取店铺列表

```http
GET /api/stores?status=active&limit=10
```

### 3. 预约 API

#### 创建预约

```http
POST /api/appointments
Content-Type: application/json

{
  "storeId": "store123",
  "customerId": "customer123",
  "serviceId": "service123",
  "staffId": "staff123",
  "date": "2024-01-15",
  "time": "10:00",
  "duration": 60,
  "notes": "Special instructions"
}
```

#### 获取预约列表

```http
GET /api/appointments?storeId=store123&status=confirmed&date=2024-01-15
```

### 4. 客户 API

#### 创建客户

```http
POST /api/customers
Content-Type: application/json

{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "phoneNumber": "****** 987 6543",
  "note": "New customer"
}
```

#### 获取客户列表

```http
GET /api/customers?storeId=store123&source=portal_created
```

---

## 🗄️ 数据库设计

### 1. Firestore 集合结构

#### 用户数据 (portal-user-data)

```typescript
interface PortalUserData extends BaseModel {
  uid: string;                    // Firebase Auth UID
  email: string;                  // 邮箱
  displayName: string;            // 显示名称
  firstName?: string;             // 名字
  lastName?: string;              // 姓氏
  phoneNumber?: string;           // 电话号码
  userType: UserType;            // 用户类型
  photoURL?: string;             // 头像URL
  preferences?: UserPreferences;  // 用户偏好
  isProfileCompleted: boolean;    // 资料是否完成
}
```

#### 店铺信息 (store-info)

```typescript
interface StoreInfo extends BaseModel {
  storeId: string;               // 店铺ID
  storeName: string;             // 店铺名称
  businessType: BusinessType;    // 业务类型
  description?: string;          // 描述
  phone: string;                 // 电话
  email: string;                 // 邮箱
  website?: string;              // 网站
  currentAddress: Address;       // 当前地址
  services: ServiceTypes;        // 服务类型
  businessHours?: BusinessHours; // 营业时间
  googlePlaceId?: string;        // Google Place ID
  avatarUrl?: string;            // 头像URL
  storePhotos?: string[];        // 店铺照片
  storeVerifiedStatus: StoreVerifiedStatus; // 验证状态
  storeStatus: StoreStatus;      // 店铺状态
  appointmentOpen: boolean;      // 预约开关
  staffs: string[];             // 员工列表
  customerList: string[];        // 客户列表
}
```

#### 预约数据 (appointment)

```typescript
interface Appointment extends BaseModel {
  appointmentId: string;         // 预约ID
  storeId: string;              // 店铺ID
  customerId: string;           // 客户ID
  staffId: string;              // 员工ID
  serviceId: string;            // 服务ID
  status: AppointmentStatus;    // 预约状态
  source: AppointmentSource;    // 预约来源
  timeInfo: AppointmentTimeInfo; // 时间信息
  customerInfo: CustomerInfo;   // 客户信息
  serviceInfo: ServiceInfo;     // 服务信息
  staffInfo: StaffInfo;         // 员工信息
  notes?: string;               // 备注
}
```

#### 员工服务 (employee-service)

```typescript
interface EmployeeService extends BaseModel {
  serviceId: string;            // 服务ID
  staffId: string;              // 员工ID
  serviceCategory: ServiceCategory; // 服务类别
  serviceBreed: ServiceBreed;   // 服务对象
  serviceAmount: number;        // 服务价格
  minDuration: number;          // 最短时间
  maxDuration: number;          // 最长时间
  defaultDuration: number;      // 默认时间
  description?: string;         // 描述
}
```

#### 店铺服务 (bookable-service)

```typescript
interface BookableService extends BaseModel {
  serviceId: string;            // 服务ID
  storeId: string;              // 店铺ID
  serviceName: string;          // 服务名称
  serviceCategory: ServiceCategory; // 服务类别
  serviceBreed: ServiceBreed;   // 服务对象
  commission: number;           // 佣金比例
  isOnlineBookingEnabled: boolean; // 是否支持在线预约
  requiresApproval: boolean;    // 是否需要审批
  staffIds: string[];          // 提供服务的员工列表
  totalBookings: number;        // 总预约数
  completedBookings: number;    // 已完成预约数
}
```

### 2. 数据关系

```mermaid
erDiagram
    PortalUserData ||--o{ StoreInfo : "owns"
    StoreInfo ||--o{ Appointment : "has"
    StoreInfo ||--o{ BookableService : "provides"
    StoreInfo ||--o{ EmployeeService : "employs"
    Appointment }|--|| BookableService : "uses"
    Appointment }|--|| PortalUserData : "involves"
    EmployeeService }|--|| PortalUserData : "provided_by"
```

### 3. 索引设计

#### 复合索引

```json
{
  "indexes": [
    {
      "collectionGroup": "appointment",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "storeId", "order": "ASCENDING" },
        { "fieldPath": "status", "order": "ASCENDING" },
        { "fieldPath": "create_date", "order": "DESCENDING" }
      ]
    },
    {
      "collectionGroup": "store-info",
      "queryScope": "COLLECTION",
      "fields": [
        { "fieldPath": "storeVerifiedStatus", "order": "ASCENDING" },
        { "fieldPath": "create_date", "order": "DESCENDING" }
      ]
    }
  ]
}
```

### 4. 安全规则

#### 基本规则

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 用户数据访问
    match /portal-user-data/{document} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.fid || 
         request.auth.uid == resource.data.created_by);
    }
    
    // 店铺数据访问
    match /store-info/{document} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.staffs;
    }
    
    // 预约数据访问
    match /appointment/{document} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.staffId ||
         request.auth.uid in get(/databases/$(database)/documents/store-info/$(resource.data.storeId)).data.staffs);
    }
  }
}
```

---

## 📝 更新日志

### v1.0.0 (2024-01-15)

#### 新增功能
- ✅ 完整的用户认证系统
- ✅ 店铺管理功能
- ✅ 预约管理系统
- ✅ 客户管理系统
- ✅ 员工管理系统
- ✅ 国际化支持
- ✅ 响应式设计

#### 技术改进
- ✅ TypeScript 类型安全
- ✅ Firebase 集成
- ✅ 性能优化
- ✅ 错误处理
- ✅ 代码规范

#### 文档
- ✅ 完整的开发者文档
- ✅ API 文档
- ✅ 部署指南
- ✅ 故障排除指南

---

## 🤝 贡献指南

### 开发流程

1. **Fork 项目**
2. **创建功能分支** `git checkout -b feature/new-feature`
3. **提交更改** `git commit -m 'Add new feature'`
4. **推送分支** `git push origin feature/new-feature`
5. **创建 Pull Request**

### 代码规范

- 遵循 TypeScript 规范
- 使用 ESLint 检查代码
- 编写单元测试
- 更新相关文档

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建过程或辅助工具的变动
```

---

## 📞 支持

### 联系方式

- **项目维护者**: OneNata Team
- **邮箱**: <EMAIL>
- **GitHub Issues**: [项目 Issues 页面]

### 常见问题

1. **如何重置密码？**
   - 使用 Firebase Authentication 的密码重置功能

2. **如何添加新语言？**
   - 在 `messages/` 目录添加新的翻译文件
   - 更新 `i18n/` 配置

3. **如何部署到生产环境？**
   - 参考 [部署指南](#部署指南) 章节

4. **如何调试 Firebase 问题？**
   - 使用 Firebase Console 查看日志
   - 检查安全规则配置
   - 验证环境变量设置

---

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

*最后更新: 2024年1月15日*
*文档版本: v1.0.0* 