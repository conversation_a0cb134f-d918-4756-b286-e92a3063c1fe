.div {
  background-color: #fdecce;
  border-radius: 200px 0px 0px 0px;
  height: 688px;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 3280px;
  width: 1920px;
}

.div .stay-connected-with-wrapper {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  left: 256px;
  position: absolute;
  top: 120px;
  width: 640px;
}

.div .stay-connected-with {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 55px;
  font-weight: 700;
  letter-spacing: 0;
  line-height: 55px;
  margin-right: -10.00px;
  margin-top: -1.00px;
  position: relative;
  width: fit-content;
}

.div .create-a-profile-for-2 {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 56px;
  left: 256px;
  letter-spacing: 0.40px;
  line-height: 28px;
  position: absolute;
  top: 259px;
  width: 650px;
}

.div .list-3 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 16px;
  left: 256px;
  position: absolute;
  top: 408px;
  width: 640px;
}

.div .item-3 {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 32px;
}

.div .text-wrapper-5 {
  display: block;
  font-size: 24px;
  line-height: 1;
  margin: 0;
  padding: 0;
  color: #a025ff;
  font-family: "Material Icons", Helvetica;
  font-weight: 400;
  letter-spacing: 0;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.div .text-wrapper-6 {
  display: block;
  font-size: 18px;
  line-height: 28px;
  margin: 0;
  padding: 0;
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-weight: 400;
  letter-spacing: 0;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.div .overlap-3 {
  height: 673px;
  left: 984px;
  position: absolute;
  top: 99px;
  width: 1055px;
}

.div .ellipse-2 {
  background: linear-gradient(
    141deg,
    rgba(161, 38, 255, 0.55) 0%,
    rgba(198, 198, 198, 0.55) 100%
  );
  border-radius: 336px;
  height: 672px;
  left: 383px;
  position: absolute;
  top: 1px;
  width: 672px;
}

.div .element-pacific-blue {
  height: 546px;
  left: 0;
  position: absolute;
  top: 0;
  width: 1000px;
}

.div .overlap-4 {
  height: 546px;
  position: relative;
}

.div .element {
  background-image: url(https://c.animaapp.com/9exExmER/img/shadow.png);
  background-size: 100% 100%;
  height: 501px;
  left: 0;
  position: absolute;
  top: 0;
  width: 679px;
}

.div .mockup-wrapper {
  background-image: url(https://c.animaapp.com/9exExmER/img/iphone-12-pro.png);
  background-size: 100% 100%;
  height: 442px;
  left: 69px;
  position: relative;
  top: 28px;
  width: 579px;
}

.div .mockup-2 {
  background-image: url(https://c.animaapp.com/9exExmER/img/mask-group-1.png);
  background-size: 100% 100%;
  height: 402px;
  left: 15px;
  position: relative;
  top: 7px;
  width: 551px;
}

.div .reflection {
  height: 416px;
  left: 75px;
  mix-blend-mode: screen;
  position: absolute;
  top: 29px;
  width: 571px;
}

.div .overlap-group-wrapper {
  height: 460px;
  left: 221px;
  position: absolute;
  top: 86px;
  width: 779px;
}

.div .overlap-5 {
  height: 460px;
  position: relative;
}

.div .shadow {
  height: 450px;
  left: 0;
  mix-blend-mode: multiply;
  position: absolute;
  top: 10px;
  width: 779px;
}

.div .rectangle-2 {
  height: 450px;
  left: 0;
  position: absolute;
  top: 0;
  width: 715px;
}

.div .shadow-2 {
  height: 301px;
  left: 15px;
  mix-blend-mode: multiply;
  position: absolute;
  top: 10px;
  width: 397px;
}

.div .iphone-pro {
  height: 393px;
  left: 89px;
  position: absolute;
  top: 0;
  width: 626px;
}

.div .mask-group-wrapper {
  height: 354px;
  left: 106px;
  position: absolute;
  top: 8px;
  width: 629px;
}

.div .mask-group-2 {
  height: 354px;
  left: 0;
  position: absolute;
  top: 0;
  width: 610px;
}
