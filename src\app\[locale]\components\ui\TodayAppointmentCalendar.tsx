'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { AppointmentStatus } from '@/app/lib/models/types';
import { 
  FiCalendar,
  <PERSON><PERSON>lock,
  <PERSON>User,
  <PERSON>H<PERSON>t,
  <PERSON>C<PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON>In<PERSON>rogress,
  FiX
} from 'react-icons/fi';

interface TodayAppointment {
  appointmentId: string;
  storeId: string;
  status: AppointmentStatus;
  timeInfo: {
    startTime: string;
    endTime: string;
    duration: number;
  };
  customerInfo: {
    name: string;
  };
  serviceInfo: {
    serviceName: string;
  };
  staffInfo: {
    staffName: string;
  };
}

interface TodayAppointmentCalendarProps {
  appointments: TodayAppointment[];
  storeId: string;
}

export default function TodayAppointmentCalendar({ appointments, storeId }: TodayAppointmentCalendarProps) {
  const router = useRouter();
  const t = useTranslations('todayCalendar');

  // Generate time slots for 24 hours (8 AM to 8 PM)
  const timeSlots = [];
  for (let hour = 8; hour <= 20; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);
    timeSlots.push(`${hour.toString().padStart(2, '0')}:30`);
  }

  // Group appointments by time slot
  const appointmentsByTime: Record<string, TodayAppointment[]> = {};
  appointments.forEach(apt => {
    const startTime = apt.timeInfo.startTime;
    if (!appointmentsByTime[startTime]) {
      appointmentsByTime[startTime] = [];
    }
    appointmentsByTime[startTime].push(apt);
  });

  const getStatusIcon = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.DRAFT:
        return <FiClock className="w-3 h-3 text-gray-500" />;
      case AppointmentStatus.CONFIRMED:
        return <FiCheck className="w-3 h-3 text-blue-500" />;
      case AppointmentStatus.IN_PROGRESS:
        return <FiInProgress className="w-3 h-3 text-yellow-500" />;
      case AppointmentStatus.COMPLETED:
        return <FiCheck className="w-3 h-3 text-green-500" />;
      case AppointmentStatus.CANCELLED:
        return <FiX className="w-3 h-3 text-red-500" />;
      default:
        return <FiClock className="w-3 h-3 text-gray-500" />;
    }
  };

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.DRAFT:
        return 'bg-gray-100 border-gray-300 text-gray-700';
      case AppointmentStatus.CONFIRMED:
        return 'bg-blue-50 border-blue-300 text-blue-800';
      case AppointmentStatus.IN_PROGRESS:
        return 'bg-yellow-50 border-yellow-300 text-yellow-800';
      case AppointmentStatus.COMPLETED:
        return 'bg-green-50 border-green-300 text-green-800';
      case AppointmentStatus.CANCELLED:
        return 'bg-red-50 border-red-300 text-red-800';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-700';
    }
  };

  const handleAppointmentClick = (appointmentId: string) => {
    router.push(`/store/${storeId}/appointments/${appointmentId}`);
  };

  const formatTime = (time: string) => {
    const [hour, minute] = time.split(':');
    const hourNum = parseInt(hour);
    const period = hourNum >= 12 ? 'PM' : 'AM';
    const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
    return `${displayHour}:${minute} ${period}`;
  };

  const today = new Date();
  const todayString = today.toLocaleDateString('en-US', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <FiCalendar className="w-6 h-6 text-violet-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
            <p className="text-sm text-gray-600">{todayString}</p>
          </div>
        </div>
        <div className="text-sm text-gray-600">
          {appointments.length} {t('totalAppointments')}
        </div>
      </div>

      {appointments.length === 0 ? (
        <div className="text-center py-8">
          <FiCalendar className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500 text-lg font-medium">{t('noAppointments')}</p>
          <p className="text-gray-400 text-sm">{t('noAppointmentsDesc')}</p>
        </div>
      ) : (
        <div className="space-y-1 max-h-96 overflow-y-auto">
          {timeSlots.map((timeSlot) => {
            const appointmentsAtTime = appointmentsByTime[timeSlot] || [];
            
            return (
              <div key={timeSlot} className="flex">
                {/* Time column */}
                <div className="w-20 flex-shrink-0 py-2 pr-4">
                  <div className="text-xs text-gray-500 font-medium">
                    {formatTime(timeSlot)}
                  </div>
                </div>
                
                {/* Appointments column */}
                <div className="flex-1 min-h-[2rem] border-l border-gray-100 pl-4">
                  {appointmentsAtTime.length > 0 && (
                    <div className="space-y-1">
                      {appointmentsAtTime.map((appointment) => (
                        <div
                          key={appointment.appointmentId}
                          onClick={() => handleAppointmentClick(appointment.appointmentId)}
                          className={`p-2 rounded-lg border cursor-pointer hover:shadow-sm transition-all duration-200 ${getStatusColor(appointment.status)}`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2 min-w-0 flex-1">
                              {getStatusIcon(appointment.status)}
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center space-x-2">
                                  <FiUser className="w-3 h-3 text-gray-500 flex-shrink-0" />
                                  <span className="text-xs font-medium truncate">
                                    {appointment.customerInfo.name}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2 mt-1">
                                  <FiHeart className="w-3 h-3 text-gray-500 flex-shrink-0" />
                                  <span className="text-xs text-gray-600 truncate">
                                    {appointment.serviceInfo.serviceName}
                                  </span>
                                </div>
                                <div className="flex items-center space-x-2 mt-1">
                                  <FiClock className="w-3 h-3 text-gray-500 flex-shrink-0" />
                                  <span className="text-xs text-gray-600">
                                    {appointment.timeInfo.startTime} - {appointment.timeInfo.endTime}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
} 