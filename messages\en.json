{"HomePage": {"title": "Hello world!", "about": "Go to the about page", "selectLanguage": "Select Language", "welcome": "Welcome to OneNata Pet Store Admin", "description": "Manage your pet store with ease", "getStarted": "Get Started", "login": "<PERSON><PERSON>", "signup": "Sign Up", "loading": "Loading...", "nav": {"features": "Features", "pricing": "Pricing", "about": "About", "signIn": "Sign In"}, "hero": {"title": "Professional Pet Store Management Platform", "subtitle": "OneNata provides comprehensive digital solutions for your pet store, from appointment management to customer relationships, making your business more efficient and intelligent", "startFree": "Start Free", "watchDemo": "Watch Demo", "feature1": "14-day free trial", "feature2": "No credit card required"}, "features": {"title": "Core Features", "subtitle": "Comprehensive management solutions for your pet store", "appointments": {"title": "Smart Appointment Management", "description": "Complete appointment system with online booking, automatic reminders, and conflict detection", "bullet1": "Real-time appointment calendar", "bullet2": "Automatic conflict detection", "bullet3": "Multi-channel notifications"}, "customers": {"title": "Customer Relationship Management", "description": "Comprehensive customer profile management including pet information, service history, and preference records", "bullet1": "Complete customer profiles", "bullet2": "Pet health records", "bullet3": "Personalized service recommendations"}, "staff": {"title": "Staff Management", "description": "Efficient staff scheduling and performance management to improve team collaboration", "bullet1": "Smart scheduling system", "bullet2": "Performance analytics", "bullet3": "Role-based permissions"}, "inventory": {"title": "Inventory Management", "description": "Real-time inventory tracking and automatic reorder reminders to ensure adequate supply", "bullet1": "Real-time inventory monitoring", "bullet2": "Automatic reorder alerts", "bullet3": "Sales data analysis"}, "analytics": {"title": "Data Analytics", "description": "Deep business analytics to help you make informed business decisions", "bullet1": "Real-time business reports", "bullet2": "Customer behavior analysis", "bullet3": "Revenue trend forecasting"}, "mobile": {"title": "Mobile Integration", "description": "Seamless integration with OneNata mobile app for business management anywhere, anytime", "bullet1": "Mobile management", "bullet2": "Real-time data sync", "bullet3": "Offline functionality"}}, "cta": {"title": "Ready to Start Digital Transformation?", "subtitle": "Join thousands of pet stores using OneNata to improve your business efficiency", "startFree": "Start Free Trial", "contactUs": "Contact Us"}, "footer": {"description": "Professional pet store management platform to help your business succeed", "product": "Product", "features": "Features", "pricing": "Pricing", "demo": "Demo", "company": "Company", "about": "About", "contact": "Contact", "privacy": "Privacy", "support": "Support", "help": "Help Center", "documentation": "Documentation", "status": "Status", "copyright": "© {year} OneNata Pet Store. All rights reserved."}}, "auth-login": {"title": "Welcome Back", "subtitle": "Sign in to your OneNata account", "email": "Email Address", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "submitButton": "Sign In", "loading": "Signing in...", "noAccount": "Don't have an account?", "signupLink": "Create one now", "welcomeTitle": "Welcome to OneNata", "welcomeDescription": "Professional pet store management platform for your business success", "successMessage": "Login successful! Redirecting...", "errorGeneral": "<PERSON><PERSON> failed. Please try again.", "features": {"analytics": "Data Analytics & Reports", "pets": "Pet Information Management", "customers": "Customer Relationship Management", "finance": "Financial Flow Statistics"}}, "auth-signup": {"title": "Create Your Account", "subtitle": "Start your pet store management journey", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "email": "Email Address", "emailPlaceholder": "<EMAIL>", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "+1 (xxx)-xxx-xxxx", "password": "Password", "passwordPlaceholder": "At least 6 characters", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Enter password again", "invitationCode": "Invitation Code", "invitationCodePlaceholder": "Enter invitation code if you have one", "invitationCodeHint": "Invitation codes can help you get additional privileges", "passwordHint": "Password must contain at least 6 characters", "submitButton": "Create Account", "loading": "Creating account...", "hasAccount": "Already have an account?", "loginLink": "Sign in now", "welcomeTitle": "Welcome to Join", "welcomeSubtitle": "OneNata Pet Store Management Platform", "welcomeDescription": "Professional pet store management solutions to make your business management easier and more efficient", "successMessage": "Account created successfully! Please verify your email.", "errorGeneral": "Registration failed. Please try again.", "features": {"analytics": "Data Analytics & Reports", "pets": "Pet Information Management", "customers": "Customer Relationship Management", "finance": "Financial Flow Statistics"}, "validation": {"firstName": "Please enter your first name", "firstNameRequired": "First name is required", "lastName": "Please enter your last name", "lastNameRequired": "Last name is required", "email": "Please enter your email", "phoneNumber": "Please enter your phone number", "phoneNumberHint": "+1 (xxx)-xxx-xxxx", "phoneNumberInvalid": "Please enter a valid 10-digit phone number", "password": "Please enter your password", "confirmPassword": "Please confirm your password", "invitationCode": "Please enter your invitation code", "invitationCodeHint": "Invitation codes can help you get additional privileges", "passwordHint": "Password must contain at least 6 characters", "passwordLength": "Password must contain at least 6 characters"}, "signup": {"alreadyHaveAccount": "Already have an account?", "login": "Sign in now", "platformName": "OneNata Pet Store Management Platform", "platformDescription": "Professional pet store management solutions to make your business management easier and more efficient", "feature1": "Data Analytics & Reports", "feature2": "Pet Information Management", "feature3": "Customer Relationship Management", "feature4": "Financial Flow Statistics", "startJourney": "Start your pet store management journey", "createAccount": "Create your account", "success": "success", "welcome": "Welcome to OneNata", "creatingAccount": "Creating account...", "errorGeneral": "Registration failed. Please try again.", "successMessage": "Account created successfully! Please verify your email.", "progress": {"step1": "Verifying user type...", "step2": "Checking account information...", "step3": "Creating user account...", "step4": "Setting up user profile...", "step5": "Configuring user preferences...", "step6": "Completing account creation..."}}, "notificationPermission": {"title": "Notification Settings", "description": "Would you like to receive notifications from OneNata? You can change this option anytime in settings.", "enableOption": "Enable Notifications", "enableDescription": "Receive important updates, order alerts, and system notifications", "disableOption": "Not Now", "disableDescription": "You can enable notifications later in settings", "confirm": "Confirm", "later": "Set Later"}}, "auth-forgot-password": {"title": "Reset Password", "subtitle": "Enter your email address and we'll send you a link to reset your password", "email": "Email Address", "emailPlaceholder": "Enter your email", "submit": "Send Reset Link", "cancel": "Cancel", "loading": "Sending...", "successMessage": "Password reset email sent successfully!", "errorGeneral": "Failed to send reset email. Please try again."}, "auth-verify-phone": {"title": "Phone Verification", "subtitle": "We've sent a verification code to your phone, please enter the code to complete verification", "phoneLabel": "Phone Number", "code": "Verification Code", "codePlaceholder": "Enter 6-digit code", "verifyButton": "Verify", "loading": "Verifying...", "resendButton": "Resend code", "resending": "Sending...", "errorRequiredCode": "Please enter verification code", "errorInvalidCode": "Invalid or expired verification code", "errorGeneral": "Verification failed, please try again", "backToLogin": "Back to login", "codeSent": "Verification code sent", "codeResent": "Verification code resent", "verificationSuccess": "Verification successful! Redirecting...", "instructions": "Please enter the 6-digit code we sent to your phone"}, "auth-profile-setup": {"title": "Complete Your Profile", "subtitle": "Please fill in your information to get started", "firstName": "First Name", "firstNamePlaceholder": "Enter your first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter your last name", "displayName": "Display Name", "displayNamePlaceholder": "Enter your display name", "dateOfBirth": "Date of Birth", "bio": "Bio", "bioPlaceholder": "Tell us about yourself", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "Enter your phone number", "address": "Address Information", "addressLine1": "Address Line 1", "addressLine1Placeholder": "Enter your address", "addressLine2": "Address Line 2", "addressLine2Placeholder": "Apartment, suite, etc.", "city": "City", "cityPlaceholder": "Enter your city", "province": "Province/State", "provincePlaceholder": "Enter your province", "country": "Country", "countryPlaceholder": "Enter your country", "postCode": "Postal Code", "postCodePlaceholder": "Enter your postal code", "submitButton": "Complete Setup", "loading": "Saving...", "successMessage": "Profile setup completed successfully!", "errorGeneral": "Failed to save profile. Please try again.", "avatarPreview": "Avatar Preview", "uploadAvatar": "Click to upload avatar", "photoSizeError": "Photo file size cannot exceed 5MB", "photoTypeError": "Please select a valid image file", "updateError": "Update failed, please try again", "notificationPreferences": "Notification Preferences", "enableNotifications": "Enable Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "themePreference": "Theme Preference", "themeLight": "Light", "themeDark": "Dark", "themeSystem": "Follow System", "skipButton": "Skip for Now", "saveProfile": "Save Profile", "saving": "Saving...", "dateOfBirthFuture": "Date of birth cannot be in the future"}, "auth-verify-email": {"title": "Verify Your Email", "subtitle": "We've sent a verification link to your email address", "message": "Please check your email and click the verification link to complete your account setup.", "instructions": "After clicking the verification link in your email, this page will automatically detect the verification status and redirect you to the dashboard.", "checkingStatus": "Checking verification status...", "emailVerified": "Email verified successfully!", "redirecting": "Redirecting to dashboard...", "resend": "Resend verification email", "resending": "Resending...", "backToLogin": "Back to Login", "successMessage": "Verification email sent successfully!", "errorGeneral": "Failed to send verification email. Please try again.", "verificationFailed": "Email verification failed. Please try again.", "emailNotVerified": "Email not yet verified. Please check your email and click the verification link.", "autoRefresh": "This page will automatically refresh every 5 seconds to check your verification status.", "manualRefresh": "Refresh Status", "code": "Verification Code", "codePlaceholder": "Enter the verification code", "submit": "<PERSON><PERSON><PERSON>", "loading": "Verifying...", "verifyButton": "<PERSON><PERSON><PERSON>", "resendButton": "Resend Verification Email"}, "validation": {"emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "phoneNumberRequired": "Phone number is required", "phoneNumberInvalid": "Please enter a valid 10-digit phone number", "phoneRequired": "Phone number is required", "phoneInvalid": "Please enter a valid phone number (10 or 11 digits)", "websiteInvalid": "Please enter a valid website URL", "storeNameRequired": "Store name is required", "businessTypeRequired": "Please select a business type", "servicesRequired": "Please select at least one service", "addressRequired": "This address field is required", "displayNameRequired": "Display name is required", "required": "This field is required", "passwordMinLength": "Password must be at least 8 characters", "passwordComplexity": "Password must contain uppercase, lowercase and numbers", "confirmPasswordRequired": "Confirm password is required", "passwordMismatch": "Passwords do not match", "currentPasswordRequired": "Current password is required", "newPasswordSameAsCurrent": "New password must be different from current password"}, "dashboard": {"welcome": "Welcome, ", "user": "User", "adminPanel": "OneNata Admin Panel", "userInfo": "User Information", "email": "Email", "userType": "User Type", "userId": "User ID", "profile": "Profile", "name": "Name", "phone": "Phone", "languagePreference": "Language Preference", "accountStatus": "Account Status", "emailVerification": "Email Verification", "verified": "Verified", "unverified": "Unverified", "notificationSettings": "Notification Settings", "enabled": "Enabled", "disabled": "Disabled", "accountState": "Account State", "normal": "Normal", "quickActions": "Quick Actions", "dataAnalytics": "Data Analytics", "petManagement": "Pet Management", "customerManagement": "Customer Management", "financialManagement": "Financial Management", "notSet": "Not Set", "storeManagement": "Store Management", "storeList": "My Stores", "createStore": "Create Store", "manageStores": "Manage Stores", "noStores": "You haven't created any stores yet", "createFirstStore": "Create your first store", "storeOverview": "Store Overview", "totalStores": "Total Stores", "activeStores": "Active Stores", "pendingApproval": "Pending Approval", "monthlyRevenue": "Monthly Revenue", "viewStoreDetails": "View Details", "editStore": "Edit Store", "storeSettings": "Store Settings", "storeStatus": "Store Status", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "active": "Active", "roles": {"PETSTORE_OWNER": "Store Owner", "PETSTORE_ADMIN": "Store Admin", "PETSTORE_STAFF": "Store Staff", "ONENATA_ADMIN": "System Admin"}, "permissions": {"noStoresStaff": "No assigned stores", "contactAdminStaff": "Please contact admin to assign store permissions", "viewServices": "View Services", "manageStaff": "Manage Staff"}, "inactive": "Inactive", "storeName": "Store Name", "storeAddress": "Store Address", "businessType": "Business Type", "services": "Services", "grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "retail": "Retail", "createdDate": "Created Date", "lastUpdated": "Last Updated", "enterStoreDashboard": "Enter Store Dashboard", "storeActions": "Store Actions"}, "store": {"management": "Store Management", "nav": {"overview": "Overview", "staff": "Staff", "services": "Services", "customers": "Customers", "appointments": "Appointments"}}, "storeManagement": {"title": "Store Management", "subtitle": "Manage your pet stores", "createNewStore": "Create New Store", "myStores": "My Stores", "storeOverview": "Store Overview", "noStoresMessage": "You haven't created any stores yet", "createFirstStoreMessage": "Create your first store to start your business", "storeCard": {"viewDetails": "View Details", "editStore": "Edit", "enterDashboard": "Enter Dashboard", "settings": "Settings", "status": "Status", "address": "Address", "phone": "Phone", "email": "Email", "website": "Website", "services": "Services", "businessHours": "Business Hours", "createdAt": "Created At"}, "stats": {"totalStores": "Total Stores", "activeStores": "Active Stores", "pendingStores": "Pending Stores", "rejectedStores": "Rejected Stores"}, "filters": {"all": "All", "active": "Active", "pending": "Pending", "rejected": "Rejected", "searchPlaceholder": "Search stores..."}, "actions": {"refresh": "Refresh", "sort": "Sort", "filter": "Filter"}}, "storeCreation": {"title": "Create New Store", "subtitle": "Search your business on Google Maps first, or enter details manually", "smartSearchMode": "Enable Smart Search", "manualMode": "Manual Entry", "manualAddressEntry": "Manual Address Entry", "manualAddressHint": "Enter your business address manually. All fields marked with * are required.", "addressValidated": "Address Validated", "addressValidationFailed": "Address Validation Failed", "addressValidationSuccess": "✓ Found in Google Maps", "addressValidationCustom": "○ Custom address (not in Google Maps)", "basicInfo": "Basic Information", "storeName": "Store Name", "storeNamePlaceholder": "Enter store name", "businessType": "Business Type", "selectBusinessType": "Select business type", "description": "Store Description", "descriptionPlaceholder": "Describe your store features and services", "contactInfo": "Contact Information", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "email": "Email Address", "emailPlaceholder": "Enter email address", "website": "Website", "websitePlaceholder": "Enter website URL (optional)", "locationInfo": "Location Information", "address": "Detailed Address", "addressPlaceholder": "Enter detailed address", "searchAddress": "Search Address", "useCurrentLocation": "Use Current Location", "servicesOffered": "Services Offered", "selectServices": "Select services you provide", "businessHours": "Business Hours", "setBusinessHours": "Set business hours", "photos": "Store Photos", "storePhotos": "Store Photos", "uploadPhotos": "Upload store photos", "avatar": "Store Avatar", "uploadAvatar": "Upload store avatar", "submitButton": "Submit for Review", "submitting": "Submitting...", "draft": "Save Draft", "preview": "Preview", "smartAddressSearch": {"title": "Business Search", "selected": "✅ Selected Google Maps store, will automatically get store information and pre-fill form", "notSelected": "Search your store first. If found in Google Maps, we will automatically get business details. If you are not listed, please enter manually."}, "businessTypes": {"online_store": "Online Store", "family_based_business": "Family Based Business", "retail_business": "Retail Business", "commercial_business": "Commercial Business", "pet_store": "Pet Store", "veterinary_clinic": "Veterinary Clinic", "pet_grooming": "Pet Grooming", "pet_hotel": "Pet Hotel", "pet_training": "Pet Training", "comprehensive_service": "Comprehensive Service", "franchise": "Franchise", "mobile_service": "Mobile Service", "other": "Other"}, "validation": {"storeNameRequired": "Store name is required", "businessTypeRequired": "Please select a business type", "phoneRequired": "Phone number is required", "emailRequired": "Email address is required", "emailInvalid": "Please enter a valid email address", "websiteInvalid": "Please enter a valid website URL", "addressValidationRequired": "Please search and select an address before proceeding", "addressValidationError": "Please provide a valid address", "servicesRequired": "Please select at least one service"}, "services": {"grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "retail": "Retail", "other": "Other"}, "success": "Store created successfully! Waiting for approval...", "error": "Creation failed, please try again", "successPage": {"title": "Store Created Successfully!", "subtitle": "Your store has been created and is waiting for approval.", "status": "Status", "pendingApproval": "Pending Approval", "estimatedReviewTime": "Estimated Review Time", "reviewTimeValue": "1-3 business days", "notificationMethod": "Notification Method", "emailNotification": "Email Notification", "backToDashboard": "Back to Dashboard", "createAnotherStore": "Create Another Store", "autoRedirect": "Redirecting to dashboard in 5 seconds"}}, "storeDetails": {"title": "Store Details", "backButton": "Back", "editStore": "Edit Store", "loadingError": "Loading Failed", "loadingErrorMessage": "Unable to load store details, please try again", "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "verified": "Verified", "unverified": "Unverified"}, "tabs": {"overview": "Overview", "info": "Details", "photos": "Photos", "activity": "Activity"}, "stats": {"totalStaff": "Total Staff", "totalServices": "Total Services", "verificationStatus": "Verification Status", "createdAt": "Created At", "unknown": "Unknown", "todayRevenue": "Today's Revenue", "monthlyRevenue": "Monthly Revenue", "todayAppointments": "Today's Appointments", "pendingOrders": "Pending Orders", "completedOrders": "Completed Orders", "salesTrend": "Sales Trend", "revenueGrowth": "Revenue Growth", "customerSatisfaction": "Customer Satisfaction", "averageOrderValue": "Average Order Value"}, "dashboard": {"title": "Dashboard", "appointmentTrends": {"title": "Appointment Trends"}, "appointmentStatus": {"title": "Appointment Status"}}, "fromCompletedAppointments": "From completed appointments", "appointmentsScheduled": "Appointments scheduled", "thisMonth": "This month", "weeklyAppointments": "Weekly Appointments", "completedAppointments": "Completed Appointments", "noData": "No data", "appointmentStatus": {"draft": "Draft", "confirmed": "Confirmed", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled"}, "actions": {"manageStaff": "Manage Staff", "manageServices": "Manage Services", "editStore": "Edit Store"}, "businessInfo": "Business Information", "address": "Address", "phone": "Phone", "email": "Email", "website": "Website", "description": "Description", "notProvided": "Not Provided", "businessType": {"label": "Business Type", "online_store": "Online Store", "family_based_business": "Family Business", "retail_business": "Retail Business", "commercial_business": "Commercial Business", "pet_store": "Pet Store", "veterinary_clinic": "Veterinary Clinic", "pet_grooming": "Pet Grooming", "pet_hotel": "Pet Hotel", "pet_training": "Pet Training", "comprehensive_service": "Comprehensive Service", "franchise": "Franchise", "mobile_service": "Mobile Service", "other": "Other"}, "noPhotos": "No Photos", "noPhotosDesc": "No photos have been uploaded yet", "noActivity": "No Activity", "noActivityDesc": "No recent activity to display", "photos": {"title": "Store Photos", "addPhotos": "Add Photos", "addFirstPhoto": "Add Your First Photo", "noPhotos": "No Photos Yet", "noPhotosDesc": "Add photos to showcase your store and attract customers."}, "basicInfo": {"title": "Basic Information", "address": "Address", "phone": "Phone", "email": "Email", "website": "Website"}, "services": {"title": "Services", "grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "retail": "Retail"}, "businessHours": {"title": "Business Hours", "notSet": "Not Set", "closed": "Closed", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "staff": {"title": "Staff Information", "count": "{count} staff members"}, "activity": {"title": "Activity Log", "noActivity": "No activity records"}, "quickActions": {"title": "Quick Actions", "editInfo": "Edit Store Info", "manageStaff": "Manage Staff", "viewAppointments": "View Appointments"}, "businessTypes": {"online_store": "Online Store", "family_based_business": "Family Business", "retail_business": "Retail Business", "commercial_business": "Commercial Business", "pet_store": "Pet Store", "veterinary_clinic": "Veterinary Clinic", "pet_grooming": "Pet Grooming", "pet_hotel": "Pet Hotel", "pet_training": "Pet Training", "comprehensive_service": "Comprehensive Service", "franchise": "Franchise", "mobile_service": "Mobile Service", "other": "Other"}}, "common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "submit": "Submit", "close": "Close", "back": "Back", "noNotifications": "No notifications", "next": "Next", "open": "Open", "closed": "Closed", "previous": "Previous", "continue": "Continue", "confirm": "Confirm", "edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "clear": "Clear", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "print": "Print", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "view": "View", "preview": "Preview", "settings": "Settings", "help": "Help", "about": "About", "contact": "Contact", "privacy": "Privacy", "terms": "Terms", "logout": "Logout", "account": "Account", "dashboard": "Dashboard", "home": "Home", "yes": "Yes", "no": "No", "ok": "OK", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "required": "Required", "optional": "Optional", "all": "All", "none": "None", "other": "Other", "rejected": "Rejected", "pending": "Pending", "approved": "Approved", "verified": "Verified", "unverified": "Unverified", "reviews": "Reviews", "unknown": "Unknown", "noRating": "No Rating", "notProvided": "Not Provided", "na": "N/A", "tbd": "TBD", "coming_soon": "Coming Soon", "selectValidDate": "Please select a valid date range", "header": {"editProfile": "Edit Profile", "settings": "Settings", "notification": "Notification"}, "actions": {"back": "Back", "edit": "Edit", "save": "Save", "cancel": "Cancel", "delete": "Delete", "create": "Create", "update": "Update", "view": "View", "close": "Close"}, "profile": {"settings": "Profile Settings"}, "auth": {"signOut": "Sign Out"}, "status": {"verified": "Verified", "unverified": "Unverified", "pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "weekDays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "validation": {"phoneInvalid": "Invalid phone number format", "phoneValid": "Valid phone number format", "websiteInvalid": "Invalid website format", "websiteValid": "Valid website format", "emailInvalid": "Invalid email format", "addressInvalid": "Invalid address format"}}, "datePicker": {"year": "Year", "month": "Month", "day": "Day", "yearUnit": "", "monthUnit": "", "dayUnit": ""}, "photoUpload": {"avatarPreview": "Avatar Preview", "uploading": "Uploading...", "uploadHint": "Click or drag to upload photo", "supportedFormats": "Supports JPEG, PNG, WebP, GIF (max 5MB)", "uploadSuccess": "Photo uploaded successfully", "uploadFailed": "Upload failed", "fileSizeError": "File size cannot exceed 5MB", "fileTypeError": "Only image files are supported", "compressing": "Compressing image...", "selectPhoto": "Select Photo"}, "profileEdit": {"title": "Edit Profile", "subtitle": "Update your personal information and preferences", "backButton": "Back", "unsavedChanges": "Unsaved changes", "basicInfo": "Basic Information", "avatar": "Avatar", "firstName": "First Name", "firstNamePlaceholder": "Enter first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter last name", "displayName": "Display Name", "displayNamePlaceholder": "Enter display name", "email": "Email", "emailPlaceholder": "Enter email", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "+1 (xxx)-xxx-xxxx", "dateOfBirth": "Date of Birth", "bio": "Bio", "bioPlaceholder": "Tell us about yourself...", "preferences": "Preferences", "language": "Language", "theme": "Theme", "themeSystem": "Follow System", "themeLight": "Light Mode", "themeDark": "Dark Mode", "notificationSettings": "Notification Settings", "enableNotifications": "Enable Notifications", "enableNotificationsDesc": "Receive in-app notifications", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive important notifications via email", "smsNotifications": "SMS Notifications", "smsNotificationsDesc": "Receive important notifications via SMS", "saveButton": "Save Changes", "cancelButton": "Cancel", "saving": "Saving...", "validation": {"firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "phoneInvalid": "Invalid phone number format", "futureDateError": "Date of birth cannot be in the future"}, "success": "Profile updated successfully!", "error": "Update failed, please try again", "confirmLeave": "You have unsaved changes, are you sure you want to leave?", "debugInfo": {"userName": "User Name", "initial": "Initial", "previewUrl": "Preview URL", "empty": "(empty)", "yes": "Yes", "no": "No"}}, "storeStaff": {"title": "Staff Management", "subtitle": "Manage store staff - Total {total}, Active {active}", "addStaff": "Add Staff", "addFirstStaff": "Add First Staff Member", "noStaff": "No Staff Members", "noStaffDesc": "Start adding staff members to manage your store services", "noSearchResults": "No matching staff found", "noSearchResultsDesc": "Try adjusting your search criteria", "searchPlaceholder": "Search staff name or email...", "allRoles": "All Roles", "firstName": "First Name", "firstNamePlaceholder": "Enter first name", "lastName": "Last Name", "lastNamePlaceholder": "Enter last name", "email": "Email", "emailPlaceholder": "Enter email address", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "+1 (xxx)-xxx-xxxx", "role": "Role", "note": "Note", "notePlaceholder": "Add a note about this staff member...", "sendInvitation": "Send Invitation", "sending": "Sending...", "cancel": "Cancel", "edit": "Edit", "activate": "Activate", "deactivate": "Deactivate", "remove": "Remove", "active": "Active", "inactive": "Inactive", "joinDate": "Join Date", "confirmDelete": "Are you sure you want to remove this staff member?", "roles": {"staff": "Staff", "manager": "Manager", "serviceProvider": "Owner", "admin": "Admin", "owner": "Owner"}, "form": {"email": "Email", "emailPlaceholder": "Enter email address", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "firstName": "First Name", "firstNamePlaceholder": "Enter first name", "firstNameRequired": "First name is required", "lastName": "Last Name", "lastNamePlaceholder": "Enter last name", "lastNameRequired": "Last name is required", "phoneNumber": "Phone Number", "phoneNumberPlaceholder": "+1 (xxx)-xxx-xxxx", "phoneInvalid": "Invalid phone number format", "role": "Role", "tempPassword": "Temporary Password", "tempPasswordPlaceholder": "Leave blank to auto-generate", "tempPasswordNote": "Leave blank to auto-generate temporary password", "note": "Note", "notePlaceholder": "Add a note about this staff member...", "cancel": "Cancel", "create": "Create Staff", "save": "Save"}, "createStaffAccount": "Create Staff Account", "totalStaff": "Total Staff", "activeStaff": "Active Staff", "inactiveStaff": "Inactive Staff", "managers": "Managers", "allStatus": "All Status", "loading": "Loading...", "createSuccess": "Staff created successfully", "createError": "Failed to create staff", "updateSuccess": "Update successful", "updateError": "Update failed", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "pleaseLogin": "Please login first", "viewDetails": "View Details", "editInfo": "Edit Info", "deleteStaff": "Delete Staff", "staffDetails": "Staff Details", "basicInfo": "Basic Information", "workInfo": "Work Information", "status": "Status", "scheduleManagement": "Schedule Management", "serviceManagement": "Service Management", "editSchedule": "Edit Schedule", "applyToWeekdays": "Apply to Weekdays", "applyToWeekends": "Apply to Weekends", "schedule": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "rest": "Rest", "set": "Set", "notSet": "Not Set", "autoCancel": "Staff schedules on store closure days have been automatically cancelled. Please click save to apply changes.", "businessHours": "Business Hours", "storeClosed": "Store Closed", "quickApplyNote": "Quick apply will use store business hours", "validation": {"startTimeBeforeOpen": "Staff start time ({startTime}) cannot be earlier than store opening time ({openTime})", "endTimeAfterClose": "Staff end time ({endTime}) cannot be later than store closing time ({closeTime})", "storeClosed": "Store is closed on {weekday}, cannot schedule staff", "startTimeAfterEnd": "Start time cannot be later than or equal to end time"}}, "stats": {"joinDate": "Join Date", "scheduleStatus": "Schedule Status", "serviceCount": "Service Count", "notSet": "Not Set"}, "serviceInfo": {"category": "Category", "petType": "Pet Type", "duration": "Duration", "minutes": "minutes", "price": "Price", "count": "Completed Count"}, "validation": {"emailRequired": "Email is required", "emailInvalid": "Invalid email format", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required"}, "addService": "Add Service", "noServices": "No Services", "noServicesNote": "Please add services to provide professional care for customers", "createService": "Create Service", "editService": "Edit Service", "deleteService": "Delete Service", "confirmDeleteService": "Are you sure you want to delete this service?", "serviceCreated": "Service created successfully", "serviceUpdated": "Service updated successfully", "serviceDeleted": "Service deleted successfully", "serviceCreateError": "Failed to create service", "serviceUpdateError": "Failed to update service", "serviceDeleteError": "Failed to delete service", "serviceForm": {"serviceName": "Service Name", "serviceNamePlaceholder": "Enter service name", "serviceCategory": "Service Category", "serviceBreed": "Pet Type", "serviceDuration": "Duration (minutes)", "serviceAmount": "Price", "serviceAmountCurrency": "<PERSON><PERSON><PERSON><PERSON>", "servicePhotos": "Service Photos", "cancel": "Cancel", "save": "Save", "create": "Create Service", "update": "Update Service", "serviceNameRequired": "Service name is required", "durationRequired": "Duration is required", "durationMinimum": "Duration must be at least 1 minute", "amountRequired": "Price is required", "amountMinimum": "Price must be greater than 0"}, "serviceCategories": {"grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "wash": "Washing", "other": "Other"}, "serviceBreeds": {"dog": "Dog", "cat": "Cat", "other": "Other Pets"}, "serviceCurrency": {"CAD": "CAD", "USD": "USD"}}, "storeServices": {"title": "Service Management", "subtitle": "Manage store services - Total {total}, Active {active}", "addService": "Add Service", "addFirstService": "Add First Service", "noServices": "No Services", "noServicesDesc": "Start adding services to manage your store business", "noSearchResults": "No matching services found", "noSearchResultsDesc": "Try adjusting your search criteria", "searchPlaceholder": "Search service name or providers...", "allCategories": "All Categories", "category": "Category", "breed": "Breed", "description": "Description", "descriptionPlaceholder": "Enter service description...", "availableStaff": "Available Staff", "loadingAvailableStaff": "Loading available staff...", "noStaffForCategory": "No staff available for this category", "servicesInCategory": "services in category", "commission": "Commission", "commissionDescription": "Store commission rate from this service", "enableOnlineBooking": "Enable Online Booking", "onlineBookingEnabled": "Online Booking Enabled", "totalBookings": "Total Bookings", "completedCount": "Completed", "providers": "Providers", "photos": "photos", "requiresApproval": "Requires Approval", "cancel": "Cancel", "creating": "Creating...", "createService": "Create Service", "edit": "Edit", "delete": "Delete", "activate": "Activate", "deactivate": "Deactivate", "active": "Active", "inactive": "Inactive", "confirmDelete": "Are you sure you want to delete this service?", "noStaffWarning": "No staff available. Please add staff first.", "staffPermissionNote": "As staff, you can only view services you participate in.", "categories": {"grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "wash": "Wash", "other": "Other"}, "breeds": {"dog": "Dog", "cat": "Cat", "other": "Other"}, "validation": {"providerRequired": "At least one provider must be selected", "commissionInvalid": "Commission must be between 0-100%"}}, "staffSchedule": {"title": "Staff Schedule", "subtitle": "Manage work schedules for {count} staff members", "noStaff": "No Staff Members", "noStaffDesc": "Please add staff members first to manage schedules", "edit": "Edit", "save": "Save", "cancel": "Cancel", "addShift": "Add Shift", "noShifts": "Day Off", "unavailable": "Unavailable"}, "Address": {"addressLine1": "Address Line 1", "addressLine1Placeholder": "Enter street address", "addressLine2": "Address Line 2", "addressLine2Placeholder": "Apartment, suite, etc. (optional)", "city": "City", "cityPlaceholder": "Enter city", "province": "Province", "provincePlaceholder": "Enter province or state", "country": "Country", "countryPlaceholder": "Enter country", "postCode": "Postal Code", "postCodePlaceholder": "Enter postal code", "addressValidation": {"title": "Address Validation"}, "GoogleMaps": {"title": "Google Maps", "subtitle": "Search your business on Google Maps first, or enter details manually", "smartSearchMode": "Enable Smart Search", "manualMode": "Manual Entry", "manualAddressEntry": "Manual Address Entry", "googleMapsBusinessPreview": "Google Maps Business Preview", "businessName": "Business Name", "address": "Address", "phone": "Phone", "website": "Website", "rating": "Rating", "reviewNumber": "reviews", "autoFillForm": "These details will be automatically filled in the form in the next step", "googleMapsBusinessCompleteView": "Google Maps Business Details", "businessStatus": "Business Status", "businessType": "Business Type", "googleMapsBusinessHours": "Google Maps Business Hours", "adjustBasedOnGoogleMaps": "(Adjust based on Google Maps info)"}}, "googlePhotos": {"title": "Google Place Photos", "description": "Select photos from Google Maps to add to your store gallery. These photos will be downloaded and uploaded to your store.", "selectAll": "Select All", "deselectAll": "Deselect All", "uploadSelected": "Upload Selected", "uploading": "Uploading...", "uploaded": "Uploaded", "loadingPhotos": "Loading photos...", "uploadProgress": "Upload Progress", "uploadedCount": "{count} photos uploaded successfully"}, "auth-change-password": {"title": "Change Password", "subtitle": "Please update your password for security", "currentPassword": "Current Password", "currentPasswordPlaceholder": "Enter your current password", "newPassword": "New Password", "newPasswordPlaceholder": "Enter your new password", "confirmPassword": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your new password", "updatePassword": "Update Password", "updating": "Updating...", "skipForNow": "Skip for Now", "successMessage": "Password updated successfully! Redirecting...", "errorGeneral": "Failed to update password. Please try again.", "passwordStrength": "Password Strength", "strengthVeryWeak": "Very Weak", "strengthWeak": "Weak", "strengthFair": "Fair", "strengthGood": "Good", "strengthStrong": "Strong", "passwordRequirements": "Password must contain at least 8 characters, including uppercase, lowercase, and numbers"}, "smartAddressSearch": {"manualEntry": "Manual Entry", "suggestions": "Suggestions", "businessHours": "Business Hours", "businessType": "Business Type", "description": "Description", "descriptionPlaceholder": "Tell customers about your business...", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Phone", "phonePlaceholder": "****** 567 8900", "storeName": "Store Name", "storeNamePlaceholder": "Enter your store name...", "website": "Website", "websitePlaceholder": "https://www.example.com", "servicesOffered": "Services Offered", "storePhotos": "Store Photos", "basicInfo": "Basic Information", "contactInfo": "Contact Information", "location": "Location", "addressInfo": "Address Information", "addressSearch": "Search for your business address", "addressSearchPlaceholder": "Search for your business on Google Maps...", "addressNotFound": "Address not found", "addressNotFoundButton": "Can't find your address?", "manualAddressEntry": "Enter address manually", "useThisAddress": "Use this address", "searchingPlaces": "Searching places...", "selectFromResults": "Select your business from the results below:", "noResultsFound": "No businesses found for your search", "foundInGoogleMaps": "Found in Google Maps", "customAddress": "Custom address", "postalCodeLabel": "Postal Code", "postalCodePlaceholder": "Enter postal code (e.g., M5V 3A8)", "postalCodeHint": "Enter your postal code to find your location", "postalCodeRequired": "Postal code is required", "postalCodeNotFound": "No address found for this postal code", "postalCodeSearchError": "Failed to search by postal code", "locationFound": "Location Found", "businessNameLabel": "Business Name", "businessNamePlaceholder": "Enter your business name or keywords", "businessNameHint": "Search for your business in this area", "searchingBusinesses": "Searching for businesses...", "searchResults": "Search Results", "newSearch": "New Search", "businessSearchError": "Failed to search for businesses", "placeDetailsError": "Failed to get place details", "addressSelected": "Address Selected", "manualAddressRequired": "Please fill in all required address fields", "manualAddressError": "Failed to validate manual address", "validating": "Validating...", "enterManually": "Enter manually"}, "storeEdit": {"title": "Edit Store", "subtitle": "Update your store information", "backButton": "Back", "saveButton": "Save Changes", "saving": "Saving...", "cancelButton": "Cancel", "unsavedChanges": "Unsaved changes", "basicInfo": "Basic Information", "contactInfo": "Contact Information", "addressInfo": "Address Information", "servicesOffered": "Services Offered", "businessHours": "Business Hours", "storePhotos": "Store Photos", "storeName": "Store Name", "storeNamePlaceholder": "Enter your store name", "businessType": "Business Type", "description": "Description", "descriptionPlaceholder": "Tell customers about your business...", "phone": "Phone Number", "phonePlaceholder": "****** 567 8900", "email": "Email Address", "emailPlaceholder": "<EMAIL>", "website": "Website", "websitePlaceholder": "https://www.example.com", "addressSearch": "Search for your business address", "addressSearchPlaceholder": "Search for your business on Google Maps...", "addressNotFound": "Address not found", "addressNotFoundButton": "Can't find your address?", "manualAddressEntry": "Enter address manually", "useThisAddress": "Use this address", "searchingPlaces": "Searching places...", "selectFromResults": "Select your business from the results below:", "noResultsFound": "No businesses found for your search", "foundInGoogleMaps": "Found in Google Maps", "customAddress": "Custom address", "addressLine1": "Address Line 1", "addressLine1Placeholder": "Enter detailed address", "addressLine2": "Address Line 2", "addressLine2Placeholder": "Apartment, suite, etc. (optional)", "city": "City", "cityPlaceholder": "Enter city", "province": "Province/State", "provincePlaceholder": "Enter province", "country": "Country", "countryPlaceholder": "Enter country", "postCode": "Postal Code", "postCodePlaceholder": "Enter postal code", "validateAddress": "Validate Address", "validating": "Validating...", "addressValidated": "Address Validated", "addressValidationFailed": "Address Validation Failed", "addressValidationSuccess": "Address is valid and found in Google Maps", "addressValidationCustom": "Address is valid but not registered in Google Maps", "addressValidationError": "Please validate the address before saving changes", "addressValidationRequired": "Please provide a valid address", "addressChanged": "Address has been modified. Please validate before saving.", "addressValidationHint": "Click \"Validate Address\" to verify your address with Google Maps.", "smartSearchMode": "Enable Smart Search", "manualMode": "Switch to Manual Entry", "services": {"grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "retail": "Retail"}, "businessTypes": {"online_store": "Online Store", "family_based_business": "Family Based Business", "retail_business": "Retail Business", "commercial_business": "Commercial Business", "pet_store": "Pet Store", "veterinary_clinic": "Veterinary Clinic", "pet_grooming": "Pet Grooming", "pet_hotel": "Pet Hotel", "pet_training": "Pet Training", "comprehensive_service": "Comprehensive Service", "franchise": "Franchise", "mobile_service": "Mobile Service", "other": "Other"}, "weekDays": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "validation": {"storeNameRequired": "Store name is required", "businessTypeRequired": "Please select a business type", "phoneRequired": "Phone number is required", "emailRequired": "Email address is required", "emailInvalid": "Please enter a valid email address", "websiteInvalid": "Please enter a valid website URL", "addressRequired": "Please fill in all required address fields before validation", "addressValidationFailed": "Address validation failed", "addressValidationError": "Failed to validate address. Please try again."}, "success": {"saveSuccess": "Store updated successfully!", "redirecting": "Redirecting to store details..."}, "error": {"loadFailed": "Loading Failed", "loadErrorMessage": "Unable to load store details, please try again", "saveFailed": "Failed to update store", "saveErrorMessage": "An error occurred while updating store", "validationFailed": "Please fix the errors before saving"}, "loading": {"loadingStore": "Loading store details...", "savingChanges": "Saving changes..."}, "status": {"unsavedChanges": "Unsaved changes", "addressSource": "Source: {source}", "googleMaps": "Google Maps", "manualEntry": "Manual Entry"}}, "CustomerPage": {"petNamePlaceholder": "Enter pet name", "customerManagement": "Customer Management", "manageCustomerInfoAndAppointments": "Manage store customer information and appointment records", "importFromOneNata": "Import from OneNata", "addCustomer": "Add Customer", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "oneNataCustomers": "OneNata Customers", "manualAdd": "Manual Add", "searchCustomerNameEmailPhone": "Search customer name, email or phone...", "walkInCustomer": "Walk-in Customer", "allSources": "All Sources", "oneNataUser": "OneNata User", "portalCreated": "Portal Created", "customerInfo": "Customer Info", "contactInfo": "Contact Info", "appointmentStats": "Appointment Stats", "source": "Source", "actions": "Actions", "totalAppointments": "Total Appointments", "completedAppointments": "Completed", "customersNumbers": "Customers", "viewDetails": "View Details", "createAppointment": "Create Appointment", "noCustomerData": "No customer data", "clickAddCustomerToManage": "Click \"Add Customer\" to start managing your customer information", "addNewCustomer": "Add New Customer", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phoneNumber": "Phone Number", "password": "Password", "passwordPlaceholder": "Temporary password", "notes": "Notes", "customerNotes": "Customer notes...", "cancel": "Cancel", "createCustomer": "Create Customer", "oneNataUserSearch": "OneNata User Search", "oneNataUserSearchDesc": "This feature requires connection to OneNata user database", "oneNataUserSearchDesc2": "Will be implemented in future versions", "close": "Close", "pleaseFillRequiredInfo": "Please fill in required information", "pleaseLoginFirst": "Please login first", "customerCreatedSuccessfully": "Customer created successfully!", "customerCreationFailed": "Failed to create customer", "loadingCustomers": "Loading customer data...", "pets": "Pets", "petInfo": "Pet Information", "addPet": "Add Pet", "editPet": "Edit Pet", "viewPet": "View Pet", "noPets": "No pets", "addPetForCustomer": "Add pet for customer", "petName": "Pet Name", "petType": "Pet Type", "petBreed": "Breed", "petGender": "Gender", "petBirthday": "Birthday", "petAvatar": "Pet Avatar", "petNotes": "Pet Notes", "petStatus": "Status", "petActive": "Active", "petInactive": "Inactive", "petDetails": "Pet Details", "petCreatedSuccessfully": "Pet created successfully!", "petCreationFailed": "Failed to create pet", "petUpdatedSuccessfully": "Pet information updated successfully!", "petUpdateFailed": "Failed to update pet information", "petDeletedSuccessfully": "Pet deleted successfully!", "petDeleteFailed": "Failed to delete pet", "confirmDeletePet": "Are you sure you want to delete this pet?", "petAge": "Age", "petAgeMonths": "months", "petAgeYears": "years", "petAgeUnknown": "Unknown", "petVisibility": "Visibility", "petVisibilityPublic": "Public", "petVisibilityFriends": "Friends Only", "petVisibilityPrivate": "Private", "petRegId": "Registration ID", "petRegIdPlaceholder": "Pet registration ID (optional)", "petBreedPlaceholder": "e.g., Golden Retriever", "petNotesPlaceholder": "Pet special needs or notes...", "petAvatarUpload": "Upload Pet Photo", "petAvatarUploadHint": "Click to upload pet photo", "petAvatarRemove": "Remove Photo", "petAvatarPreview": "Pet Photo Preview", "petForm": {"nameRequired": "Pet name is required", "typeRequired": "Pet type is required", "breedRequired": "Breed is required", "genderRequired": "Gender is required", "birthdayInvalid": "Invalid birthday date", "avatarInvalid": "Invalid avatar format"}, "petTypes": {"dog": "Dog", "cat": "Cat", "bird": "<PERSON>", "fish": "Fish", "rabbit": "Rabbit", "hamster": "<PERSON><PERSON>", "other": "Other"}, "petGenders": {"male": "Male", "female": "Female", "unknown": "Unknown"}, "form": {"emailRequired": "Email is required", "emailInvalid": "Invalid email format", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "phoneInvalid": "Invalid phone number format", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters"}}, "CustomerDetail": {"customerDetail": "Customer Detail", "back": "Back", "backToCustomers": "Back to Customers", "customerNotFound": "Customer not found", "createAppointment": "Create Appointment", "pleaseLoginFirst": "Please login first", "fillAllRequiredFields": "Please fill all required fields", "appointmentCreatedSuccessfully": "Appointment created successfully", "appointmentCreationFailed": "Failed to create appointment", "customerInformation": "Customer Information", "email": "Email", "phoneNumber": "Phone Number", "memberSince": "Member Since", "notes": "Notes", "notSet": "Not Set", "pets": "Pets", "active": "Active", "inactive": "Inactive", "noPets": "No Pets", "addPetForCustomer": "Add Pet for Customer", "recentAppointments": "Recent Appointments", "service": "Service", "date": "Date", "time": "Time", "status": "Status", "actions": "Actions", "unknownService": "Unknown Service", "viewDetails": "View Details", "noAppointments": "No Appointments", "createFirstAppointment": "Create First Appointment", "selectService": "Select Service", "selectStaff": "Select Staff", "duration": "Duration", "minutes": "minutes", "appointmentNotes": "Appointment Notes", "cancel": "Cancel", "creating": "Creating...", "totalAppointments": "Total Appointments", "completedAppointments": "Completed Appointments", "upcomingAppointments": "Upcoming Appointments", "totalPets": "Total Pets", "oneNataUser": "OneNata User", "portalCreated": "Portal Created", "walkInCustomer": "Walk-in Customer", "draft": "Draft", "confirmed": "Confirmed", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "unknown": "Unknown"}, "AppointmentDetail": {"appointmentDetail": "Appointment Detail", "back": "Back", "backToAppointments": "Back to Appointments", "appointmentNotFound": "Appointment not found", "appointmentInformation": "Appointment Information", "serviceInformation": "Service Information", "customerInformation": "Customer Information", "staffInformation": "Staff Information", "date": "Date", "time": "Time", "duration": "Duration", "minutes": "minutes", "notes": "Notes", "serviceName": "Service Name", "serviceCategory": "Service Category", "price": "Price", "customerName": "Customer Name", "email": "Email", "phoneNumber": "Phone Number", "staffName": "Staff Name", "draft": "Draft", "confirmed": "Confirmed", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "unknown": "Unknown", "notSet": "Not Set", "unknownService": "Unknown Service"}, "appointments": {"title": "Appointment Management", "subtitle": "Manage store appointments and service scheduling", "loading": "Loading appointment data...", "enableSuccess": "Appointment system enabled!", "enableError": "Failed to enable appointment system", "statusUpdateSuccess": "Appointment status updated!", "statusUpdateError": "Failed to update appointment status", "notEnabled": {"title": "Appointment System Not Enabled", "description": "Enable the appointment system to start managing customer appointments and services", "features": {"title": "Appointment system features include:", "onlineBooking": "Online booking management", "staffSchedule": "Staff scheduling management", "customerManagement": "Customer management system", "serviceTime": "Service time management", "autoReminders": "Automatic reminder notifications", "onenataIntegration": "OneNata integration"}, "enableButton": "Enable Appointment System"}, "stats": {"totalAppointments": "Total Appointments", "upcoming": "Upcoming", "completed": "Completed", "activeServices": "Active Services"}, "filters": {"title": "Filters", "status": "Status", "date": "Date", "allStatuses": "All Statuses"}, "status": {"draft": "Draft", "confirmed": "Confirmed", "inProgress": "In Progress", "completed": "Completed", "cancelled": "Cancelled", "noShow": "No Show", "unknown": "Unknown"}, "actions": {"manageCustomers": "Manage Customers", "createAppointment": "Create Appointment", "viewDetails": "View Details", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "start": "Start", "complete": "Complete", "cancel": "Cancel"}, "table": {"appointmentInfo": "Appointment Info", "customer": "Customer", "service": "Service", "staff": "Staff", "status": "Status", "actions": "Actions", "duration": "Duration", "minutes": "minutes", "source": {"portal": "Portal Created", "onenataApp": "OneNata App", "external": "External Link"}}, "noAppointments": {"title": "No appointment data", "description": "Click \"Create Appointment\" to start managing appointments"}, "deleteConfirm": {"title": "Confirm Delete Appointment", "message": "Are you sure you want to delete this appointment? This action cannot be undone.", "confirm": "Confirm Delete", "cancel": "Cancel"}, "deleteSuccess": "Appointment deleted successfully", "deleteError": "Failed to delete appointment", "staffPermissionNote": "Staff can only view and manage their own appointments", "calendar": {"today": "Today", "tomorrow": "Tomorrow", "nextWeek": "Next Week", "selectDate": "Select Date", "pastDate": "Past date"}}, "editAppointment": {"title": "Edit Appointment", "loading": "Loading appointment data...", "saving": "Saving...", "appointmentNotFound": "Appointment not found", "backToAppointments": "Back to Appointments", "cannotEditStatus": "Cannot edit this appointment", "onlyEditDraftConfirmed": "Only draft and confirmed appointments can be edited", "backToDetails": "Back to Details", "back": "Back", "cancel": "Cancel", "saveChanges": "Save Changes", "selectCustomer": "Select Customer", "selectService": "Select Service", "selectStaff": "Select Staff", "selectDate": "Select Date", "selectTime": "Select Time", "serviceDuration": "Service Duration", "additionalInfo": "Additional Information", "chooseCustomer": "<PERSON>ose Customer", "chooseService": "Choose Service", "chooseStaff": "Choose <PERSON>", "unknownService": "Unknown Service", "minutes": "minutes", "morning": "AM", "afternoon": "PM", "noAvailableDates": "No available dates", "noAvailableTimeSlots": "No available time slots", "currentTimeSlot": "Current time slot", "current": "Current", "available": "Available", "booked": "Booked", "blocked": "Blocked", "clickToSelect": "Click to select", "timeSlotBooked": "Time slot is booked", "timeSlotUnavailable": "Time slot unavailable", "internalNotes": "Internal Notes", "customerNotes": "Customer Notes", "internalNotesPlaceholder": "Internal staff notes...", "customerNotesPlaceholder": "Customer visible notes...", "pleaseCompleteAllFields": "Please complete all required fields", "updateSuccess": "Appointment updated successfully!", "updateError": "Failed to update appointment"}, "todayCalendar": {"title": "Today's Appointments", "totalAppointments": "Total Appointments", "noAppointments": "No appointments today", "noAppointmentsDesc": "Enjoy your free day!"}, "calendar": {"today": "Today", "more": "more"}, "serviceDetail": {"title": "Service Detail", "serviceId": "Service ID", "active": "Active", "inactive": "Inactive", "goBack": "Go Back", "serviceNotFound": "Service Not Found", "serviceNotFoundDesc": "The requested service does not exist or has been deleted", "tabs": {"overview": "Overview", "staff": "Staff", "bookings": "Bookings", "settings": "Settings"}, "stats": {"totalBookings": "Total Bookings", "completedBookings": "Completed Bookings", "commission": "Commission Rate", "monthlyRevenue": "Monthly Revenue"}, "serviceInfo": "Service Information", "category": "Category", "breed": "Breed", "onlineBooking": "Online Booking", "enabled": "Enabled", "disabled": "Disabled", "requiresApproval": "Requires Approval", "yes": "Yes", "no": "No", "description": "Description", "monthlyPerformance": "Monthly Performance", "revenue": "Revenue", "averageRating": "Average Rating", "staffMembers": "Staff Members", "noPhone": "No Phone", "joined": "Joined", "status": "Status", "recentBookings": "Recent Bookings", "viewAllBookings": "View All Bookings", "bookingFeatureComingSoon": "Booking feature coming soon", "serviceSettings": "Service Settings", "editService": "Edit Service", "holidayManagement": "Holiday Management", "holidayManagementDesc": "Manage staff holidays and time off", "manageHolidays": "Manage Holidays", "currentStatus": "Current Status", "categories": {"grooming": "Grooming", "boarding": "Boarding", "veterinary": "Veterinary", "training": "Training", "wash": "Wash", "other": "Other"}}, "ProgressModal": {"creatingCustomer": "Creating Customer...", "step": "Step", "totalSteps": "Total Steps", "of": "of"}, "createAppointment": {"title": "Create Appointment", "step": "Step", "of": "of", "back": "Back", "cancel": "Cancel", "previous": "Previous", "next": "Next", "loading": "Loading...", "loadingStaff": "Loading staff...", "loadingServices": "Loading services...", "loadingTimeSlots": "Loading time slots...", "selectCustomerAndPet": "Select Customer & Pet", "selectCustomer": "Select Customer", "selectPet": "Select Pet", "optional": "Optional", "selectServiceCategory": "Select Service Category", "selectStoreService": "Select Store Service", "selectStaff": "Select Staff Member", "selectStaffService": "Select Specific Service", "selectDate": "Select Date", "selectTime": "Select Time", "confirmAppointment": "Confirm Appointment", "pleaseSelectCustomer": "Please select a customer", "pleaseSelectServiceCategory": "Please select a service category", "pleaseSelectStoreService": "Please select a store service", "pleaseSelectStaff": "Please select a staff member", "pleaseSelectStaffService": "Please select a specific service", "pleaseSelectDate": "Please select a date", "pleaseSelectTime": "Please select a time", "missingRequiredData": "Missing required data", "appointmentCreatedSuccessfully": "Appointment created successfully", "appointmentCreationFailed": "Failed to create appointment", "customer": "Customer", "pet": "Pet", "service": "Service", "serviceBreed": "Service Type", "staff": "Staff", "date": "Date", "time": "Time", "duration": "Duration", "price": "Price", "commission": "Commission", "minutes": "minutes", "services": "services", "availableStaff": "available staff", "active": "Active", "inactive": "Inactive", "notes": "Notes", "appointmentNotes": "Additional notes for this appointment...", "available": "Available", "booked": "Booked", "unavailable": "Unavailable", "unknownBreed": "Unknown breed", "noCustomersFound": "No customers found", "noStaffFound": "No staff found", "noServicesFound": "No services found", "noServicesAvailable": "No services available", "noServicesDescription": "Please set up store services in service management first, and ensure services are active and support online booking.", "goToServiceManagement": "Go to Service Management", "noAvailableDates": "No available dates", "noAvailableTimeSlots": "No available time slots", "noTimeSlotsDescription": "This staff member has no available time slots on the selected date. Please choose another date or staff member.", "workingHours": "Working Hours", "serviceDuration": "Service Duration", "availableTimeSlots": "Available Time Slots", "blocked": "Blocked", "clickToSelect": "Click to select", "timeSlotBooked": "Time slot is booked", "timeSlotUnavailable": "Time slot unavailable", "selectedTime": "Selected Time", "staffNotAvailable": "Staff not available on this date", "serviceNotAvailable": "Service not available", "selectAtLeastOneCustomer": "Please select at least one customer", "calendar": {"today": "Today", "tomorrow": "Tomorrow", "nextWeek": "Next Week", "selectDate": "Select Date", "pastDate": "Past date", "staffNotWorking": "Staff not working on this day", "availableDate": "Available for booking", "weekdays": {"monday": "Mon", "tuesday": "<PERSON><PERSON>", "wednesday": "Wed", "thursday": "<PERSON>hu", "friday": "<PERSON><PERSON>", "saturday": "Sat", "sunday": "Sun"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "legend": {"available": "Available", "today": "Today", "selected": "Selected", "notWorking": "Not working"}}, "categories": {"grooming": "Grooming", "veterinary": "Veterinary", "training": "Training", "boarding": "Boarding", "wash": "Wash", "day_care": "Day Care", "mobile_service": "Mobile Service", "other": "Other"}, "breeds": {"Dog": "Dog", "Cat": "Cat", "Other": "Other"}, "petTypes": {"DOG": "Dog", "CAT": "Cat", "BIRD": "<PERSON>", "FISH": "Fish", "RABBIT": "Rabbit", "HAMSTER": "<PERSON><PERSON>", "OTHER": "Other", "dog": "Dog", "cat": "Cat", "bird": "<PERSON>", "fish": "Fish", "rabbit": "Rabbit", "hamster": "<PERSON><PERSON>"}, "petGenders": {"MALE": "Male", "FEMALE": "Female", "UNKNOWN": "Unknown"}, "timeSlots": {"morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "selectTimeSlot": "Select Time Slot", "duration": "Duration: {duration} min", "price": "Price: ${price}"}, "steps": {"customerAndPet": "Customer & Pet", "serviceCategory": "Service Category", "storeService": "Store Service", "staff": "Select Staff", "service": "Specific Service", "date": "Select Date", "time": "Select Time", "confirm": "Confirm"}}, "editService": {"title": "Edit Service", "editService": "Edit Service", "back": "Back", "saveChanges": "Save Changes", "loading": "Loading...", "serviceNotFound": "Service not found", "errorLoadingData": "Failed to load data", "pleaseLogin": "Please login first", "serviceNameRequired": "Service name is required", "atLeastOneStaffRequired": "At least one staff member is required", "serviceUpdatedSuccessfully": "Service updated successfully", "updateFailed": "Update failed", "basicInfo": "Basic Information", "serviceName": "Service Name", "enterServiceName": "Enter service name", "serviceCategory": "Service Category", "serviceBreed": "Service Type", "commission": "Commission", "description": "Description", "enterDescription": "Enter service description", "settings": "Settings", "serviceStatus": "Service Status", "serviceStatusDesc": "Control whether service is visible to customers", "active": "Active", "inactive": "Inactive", "onlineBooking": "Online Booking", "onlineBookingDesc": "Allow customers to book this service online", "requiresApproval": "Requires Approval", "requiresApprovalDesc": "Bookings for this service require owner approval", "staffManagement": "Staff Management", "addStaff": "Add Staff", "noStaffAssigned": "No staff assigned", "searchStaff": "Search staff", "noStaffFound": "No staff found", "categories": {"grooming": "Grooming", "veterinary": "Veterinary", "training": "Training", "boarding": "Boarding", "wash": "Wash", "day_care": "Day Care", "mobile_service": "Mobile Service", "other": "Other"}, "breeds": {"dog": "Dog", "cat": "Cat", "other": "Other"}}}