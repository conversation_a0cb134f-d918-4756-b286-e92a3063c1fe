import { BaseModel, BaseModelImpl, JsonUtil } from './base_model';
import { 
  AppointmentStatus,
  AppointmentSource,
  AppointmentTimeInfo,
  StoreServiceStatus,
  ServiceCategory,
  ServiceBreed,
  Currency,
  Timestamp,
  formatTimestamp
} from './types';



/**
 * Bookable Service - 可预约服务
 * 存储在 Firestore collection "bookable-service"  
 */
export interface BookableService extends BaseModel {
  storeId: string;                    // 店铺ID
  serviceId: string;                  // 服务唯一ID
  serviceName: string;                // 服务名称
  serviceCategory: ServiceCategory;   // 服务类别
  serviceBreed: ServiceBreed;         // 服务对象
  description?: string;               // 服务描述
  status: StoreServiceStatus;         // 服务状态
  staffIds: string[];                 // 提供此服务的员工ID列表
  minDuration: number;                // 最短服务时间（分钟，必须是15的倍数）
  maxDuration: number;                // 最长服务时间（分钟，必须是15的倍数）
  defaultDuration: number;            // 默认服务时间（分钟，必须是15的倍数）
  basePrice: number;                  // 基础价格
  currency: Currency;                 // 货币单位
  maxCapacityPerSlot: number;         // 每个时间段最大接待数量
  isOnlineBookingEnabled: boolean;    // 是否开启在线预约
  requiresApproval: boolean;          // 是否需要审核
  cancellationPolicy?: string;        // 取消政策
  servicePhotos?: string[];           // 服务图片
  totalBookings: number;              // 总预约数
  completedBookings: number;          // 完成的预约数
}

export class BookableServiceImpl extends BaseModelImpl implements BookableService {
  storeId: string;
  serviceId: string;
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  description?: string;
  status: StoreServiceStatus;
  staffIds: string[];
  minDuration: number;
  maxDuration: number;
  defaultDuration: number;
  basePrice: number;
  currency: Currency;
  maxCapacityPerSlot: number;
  isOnlineBookingEnabled: boolean;
  requiresApproval: boolean;
  cancellationPolicy?: string;
  servicePhotos?: string[];
  totalBookings: number;
  completedBookings: number;

  constructor(data: Partial<BookableService>) {
    super(data);
    this.storeId = data.storeId || '';
    this.serviceId = data.serviceId || '';
    this.serviceName = data.serviceName || '';
    this.serviceCategory = data.serviceCategory || ServiceCategory.OTHER;
    this.serviceBreed = data.serviceBreed || ServiceBreed.OTHER;
    this.description = data.description;
    this.status = data.status || StoreServiceStatus.INACTIVE;
    this.staffIds = data.staffIds || [];
    this.minDuration = data.minDuration || 15;
    this.maxDuration = data.maxDuration || 60;
    this.defaultDuration = data.defaultDuration || 30;
    this.basePrice = data.basePrice || 0;
    this.currency = data.currency || Currency.CAD;
    this.maxCapacityPerSlot = data.maxCapacityPerSlot || 1;
    this.isOnlineBookingEnabled = data.isOnlineBookingEnabled || false;
    this.requiresApproval = data.requiresApproval || false;
    this.cancellationPolicy = data.cancellationPolicy;
    this.servicePhotos = data.servicePhotos || [];
    this.totalBookings = data.totalBookings || 0;
    this.completedBookings = data.completedBookings || 0;
  }

  static fromJson(json: Record<string, unknown>): BookableServiceImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new BookableServiceImpl({
      ...baseModel,
      storeId: JsonUtil.stringFromJson(json.storeId) || '',
      serviceId: JsonUtil.stringFromJson(json.serviceId) || '',
      serviceName: JsonUtil.stringFromJson(json.serviceName) || '',
      serviceCategory: (json.serviceCategory as ServiceCategory) || ServiceCategory.OTHER,
      serviceBreed: (json.serviceBreed as ServiceBreed) || ServiceBreed.OTHER,
      description: JsonUtil.stringFromJson(json.description),
      status: (json.status as StoreServiceStatus) || StoreServiceStatus.INACTIVE,
      staffIds: json.staffIds as string[] || [],
      minDuration: JsonUtil.numberFromJson(json.minDuration) || 15,
      maxDuration: JsonUtil.numberFromJson(json.maxDuration) || 60,
      defaultDuration: JsonUtil.numberFromJson(json.defaultDuration) || 30,
      basePrice: JsonUtil.numberFromJson(json.basePrice) || 0,
      currency: (json.currency as Currency) || Currency.CAD,
      maxCapacityPerSlot: JsonUtil.numberFromJson(json.maxCapacityPerSlot) || 1,
      isOnlineBookingEnabled: JsonUtil.boolFromJson(json.isOnlineBookingEnabled) || false,
      requiresApproval: JsonUtil.boolFromJson(json.requiresApproval) || false,
      cancellationPolicy: JsonUtil.stringFromJson(json.cancellationPolicy),
      servicePhotos: json.servicePhotos as string[] || [],
      totalBookings: JsonUtil.numberFromJson(json.totalBookings) || 0,
      completedBookings: JsonUtil.numberFromJson(json.completedBookings) || 0,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      storeId: this.storeId,
      serviceId: this.serviceId,
      serviceName: this.serviceName,
      serviceCategory: this.serviceCategory,
      serviceBreed: this.serviceBreed,
      description: JsonUtil.stringToJson(this.description),
      status: this.status,
      staffIds: this.staffIds,
      minDuration: this.minDuration,
      maxDuration: this.maxDuration,
      defaultDuration: this.defaultDuration,
      basePrice: this.basePrice,
      currency: this.currency,
      maxCapacityPerSlot: this.maxCapacityPerSlot,
      isOnlineBookingEnabled: this.isOnlineBookingEnabled,
      requiresApproval: this.requiresApproval,
      cancellationPolicy: JsonUtil.stringToJson(this.cancellationPolicy),
      servicePhotos: this.servicePhotos,
      totalBookings: this.totalBookings,
      completedBookings: this.completedBookings,
    };
  }

  isActive(): boolean {
    return this.status === StoreServiceStatus.ACTIVE;
  }

  isBookable(): boolean {
    return this.isActive() && this.isOnlineBookingEnabled && this.staffIds.length > 0;
  }

  validateDuration(duration: number): boolean {
    return duration >= this.minDuration && 
           duration <= this.maxDuration && 
           duration % 15 === 0;
  }
}

/**
 * Appointment - 预约
 * 存储在 Firestore collection "appointment"
 */
export interface Appointment extends BaseModel {
  appointmentId: string;              // 预约唯一ID
  storeId: string;                    // 店铺ID
  customerId: string;                 // 客户ID
  staffId: string;                    // 员工ID
  serviceId: string;                  // 服务ID
  status: AppointmentStatus;          // 预约状态
  source: AppointmentSource;          // 预约来源
  timeInfo: AppointmentTimeInfo;      // 时间信息
  customerInfo: {                     // 客户信息快照
    name: string;
    email?: string;
    phoneNumber?: string;
  };
  serviceInfo: {                      // 服务信息快照
    serviceName: string;
    serviceCategory: ServiceCategory;
    serviceBreed: ServiceBreed;
    duration: number;
    price: number;
    currency: Currency;
  };
  staffInfo: {                        // 员工信息快照
    staffName: string;
    staffEmail?: string;
  };
  notes?: string;                     // 预约备注
  customerNotes?: string;             // 客户备注
  staffNotes?: string;                // 员工备注
  cancellationReason?: string;        // 取消原因
  completionNotes?: string;           // 完成备注
  remindersSent: number[];            // 已发送的提醒（小时数）
  createdBy: string;                  // 创建者ID
  confirmedAt?: Timestamp;            // 确认时间
  startedAt?: Timestamp;              // 开始时间
  completedAt?: Timestamp;            // 完成时间
  cancelledAt?: Timestamp;            // 取消时间
}

export class AppointmentImpl extends BaseModelImpl implements Appointment {
  appointmentId: string;
  storeId: string;
  customerId: string;
  staffId: string;
  serviceId: string;
  status: AppointmentStatus;
  source: AppointmentSource;
  timeInfo: AppointmentTimeInfo;
  customerInfo: {
    name: string;
    email?: string;
    phoneNumber?: string;
  };
  serviceInfo: {
    serviceName: string;
    serviceCategory: ServiceCategory;
    serviceBreed: ServiceBreed;
    duration: number;
    price: number;
    currency: Currency;
  };
  staffInfo: {
    staffName: string;
    staffEmail?: string;
  };
  notes?: string;
  customerNotes?: string;
  staffNotes?: string;
  cancellationReason?: string;
  completionNotes?: string;
  remindersSent: number[];
  createdBy: string;
  confirmedAt?: Timestamp;
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  cancelledAt?: Timestamp;

  constructor(data: Partial<Appointment>) {
    super(data);
    this.appointmentId = data.appointmentId || '';
    this.storeId = data.storeId || '';
    this.customerId = data.customerId || '';
    this.staffId = data.staffId || '';
    this.serviceId = data.serviceId || '';
    this.status = data.status || AppointmentStatus.DRAFT;
    this.source = data.source || AppointmentSource.PORTAL;
    this.timeInfo = data.timeInfo || {
      date: '',
      startTime: '',
      endTime: '',
      duration: 30,
      timeSlots: []
    };
    this.customerInfo = data.customerInfo || { name: '' };
    this.serviceInfo = data.serviceInfo || {
      serviceName: '',
      serviceCategory: ServiceCategory.OTHER,
      serviceBreed: ServiceBreed.OTHER,
      duration: 30,
      price: 0,
      currency: Currency.CAD
    };
    this.staffInfo = data.staffInfo || { staffName: '' };
    this.notes = data.notes;
    this.customerNotes = data.customerNotes;
    this.staffNotes = data.staffNotes;
    this.cancellationReason = data.cancellationReason;
    this.completionNotes = data.completionNotes;
    this.remindersSent = data.remindersSent || [];
    this.createdBy = data.createdBy || '';
    this.confirmedAt = data.confirmedAt;
    this.startedAt = data.startedAt;
    this.completedAt = data.completedAt;
    this.cancelledAt = data.cancelledAt;
  }

  static fromJson(json: Record<string, unknown>): AppointmentImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new AppointmentImpl({
      ...baseModel,
      appointmentId: JsonUtil.stringFromJson(json.appointmentId) || '',
      storeId: JsonUtil.stringFromJson(json.storeId) || '',
      customerId: JsonUtil.stringFromJson(json.customerId) || '',
      staffId: JsonUtil.stringFromJson(json.staffId) || '',
      serviceId: JsonUtil.stringFromJson(json.serviceId) || '',
      status: (json.status as AppointmentStatus) || AppointmentStatus.DRAFT,
      source: (json.source as AppointmentSource) || AppointmentSource.PORTAL,
      timeInfo: json.timeInfo as AppointmentTimeInfo,
      customerInfo: json.customerInfo as { name: string; email?: string; phoneNumber?: string; },
      serviceInfo: json.serviceInfo as {
        serviceName: string;
        serviceCategory: ServiceCategory;
        serviceBreed: ServiceBreed;
        duration: number;
        price: number;
        currency: Currency;
      },
      staffInfo: json.staffInfo as { staffName: string; staffEmail?: string; },
      notes: JsonUtil.stringFromJson(json.notes),
      customerNotes: JsonUtil.stringFromJson(json.customerNotes),
      staffNotes: JsonUtil.stringFromJson(json.staffNotes),
      cancellationReason: JsonUtil.stringFromJson(json.cancellationReason),
      completionNotes: JsonUtil.stringFromJson(json.completionNotes),
      remindersSent: json.remindersSent as number[] || [],
      createdBy: JsonUtil.stringFromJson(json.createdBy) || '',
      confirmedAt: json.confirmedAt ? new Date(json.confirmedAt as string) : undefined,
      startedAt: json.startedAt ? new Date(json.startedAt as string) : undefined,
      completedAt: json.completedAt ? new Date(json.completedAt as string) : undefined,
      cancelledAt: json.cancelledAt ? new Date(json.cancelledAt as string) : undefined,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      appointmentId: this.appointmentId,
      storeId: this.storeId,
      customerId: this.customerId,
      staffId: this.staffId,
      serviceId: this.serviceId,
      status: this.status,
      source: this.source,
      timeInfo: this.timeInfo,
      customerInfo: this.customerInfo,
      serviceInfo: this.serviceInfo,
      staffInfo: this.staffInfo,
      notes: JsonUtil.stringToJson(this.notes),
      customerNotes: JsonUtil.stringToJson(this.customerNotes),
      staffNotes: JsonUtil.stringToJson(this.staffNotes),
      cancellationReason: JsonUtil.stringToJson(this.cancellationReason),
      completionNotes: JsonUtil.stringToJson(this.completionNotes),
      remindersSent: this.remindersSent,
      createdBy: this.createdBy,
      confirmedAt: this.confirmedAt ? formatTimestamp(this.confirmedAt) : undefined,
      startedAt: this.startedAt ? formatTimestamp(this.startedAt) : undefined,
      completedAt: this.completedAt ? formatTimestamp(this.completedAt) : undefined,
      cancelledAt: this.cancelledAt ? formatTimestamp(this.cancelledAt) : undefined,
    };
  }

  canBeCancelled(): boolean {
    return this.status === AppointmentStatus.CONFIRMED || 
           this.status === AppointmentStatus.DRAFT;
  }

  canBeStarted(): boolean {
    return this.status === AppointmentStatus.CONFIRMED;
  }

  canBeCompleted(): boolean {
    return this.status === AppointmentStatus.IN_PROGRESS;
  }

  getAppointmentDateTime(): Date {
    return new Date(`${this.timeInfo.date} ${this.timeInfo.startTime}`);
  }

  isUpcoming(): boolean {
    const appointmentTime = this.getAppointmentDateTime();
    return appointmentTime > new Date() && 
           (this.status === AppointmentStatus.CONFIRMED || this.status === AppointmentStatus.DRAFT);
  }

  isPast(): boolean {
    const appointmentTime = this.getAppointmentDateTime();
    return appointmentTime < new Date();
  }
}

/**
 * Staff Holiday - 员工休假
 * 存储在 Firestore collection "staff-holiday"
 */
export interface StaffHoliday extends BaseModel {
  storeId: string;                    // 店铺ID
  staffId: string;                    // 员工ID
  holidayId: string;                  // 休假唯一ID
  title: string;                      // 休假标题
  startDate: string;                  // 开始日期 YYYY-MM-DD
  endDate: string;                    // 结束日期 YYYY-MM-DD
  isFullDay: boolean;                 // 是否全天
  startTime?: string;                 // 开始时间 HH:MM
  endTime?: string;                   // 结束时间 HH:MM
  reason?: string;                    // 休假原因
  isRecurring: boolean;               // 是否循环
  recurringPattern?: string;          // 循环模式 (weekly, monthly, yearly)
  isApproved: boolean;                // 是否已批准
  approvedBy?: string;                // 批准人ID
  approvedAt?: Timestamp;             // 批准时间
}

export class StaffHolidayImpl extends BaseModelImpl implements StaffHoliday {
  storeId: string;
  staffId: string;
  holidayId: string;
  title: string;
  startDate: string;
  endDate: string;
  isFullDay: boolean;
  startTime?: string;
  endTime?: string;
  reason?: string;
  isRecurring: boolean;
  recurringPattern?: string;
  isApproved: boolean;
  approvedBy?: string;
  approvedAt?: Timestamp;

  constructor(data: Partial<StaffHoliday>) {
    super(data);
    this.storeId = data.storeId || '';
    this.staffId = data.staffId || '';
    this.holidayId = data.holidayId || '';
    this.title = data.title || '';
    this.startDate = data.startDate || '';
    this.endDate = data.endDate || '';
    this.isFullDay = data.isFullDay || true;
    this.startTime = data.startTime;
    this.endTime = data.endTime;
    this.reason = data.reason;
    this.isRecurring = data.isRecurring || false;
    this.recurringPattern = data.recurringPattern;
    this.isApproved = data.isApproved || false;
    this.approvedBy = data.approvedBy;
    this.approvedAt = data.approvedAt;
  }

  static fromJson(json: Record<string, unknown>): StaffHolidayImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new StaffHolidayImpl({
      ...baseModel,
      storeId: JsonUtil.stringFromJson(json.storeId) || '',
      staffId: JsonUtil.stringFromJson(json.staffId) || '',
      holidayId: JsonUtil.stringFromJson(json.holidayId) || '',
      title: JsonUtil.stringFromJson(json.title) || '',
      startDate: JsonUtil.stringFromJson(json.startDate) || '',
      endDate: JsonUtil.stringFromJson(json.endDate) || '',
      isFullDay: JsonUtil.boolFromJson(json.isFullDay) || true,
      startTime: JsonUtil.stringFromJson(json.startTime),
      endTime: JsonUtil.stringFromJson(json.endTime),
      reason: JsonUtil.stringFromJson(json.reason),
      isRecurring: JsonUtil.boolFromJson(json.isRecurring) || false,
      recurringPattern: JsonUtil.stringFromJson(json.recurringPattern),
      isApproved: JsonUtil.boolFromJson(json.isApproved) || false,
      approvedBy: JsonUtil.stringFromJson(json.approvedBy),
      approvedAt: json.approvedAt ? new Date(json.approvedAt as string) : undefined,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      storeId: this.storeId,
      staffId: this.staffId,
      holidayId: this.holidayId,
      title: this.title,
      startDate: this.startDate,
      endDate: this.endDate,
      isFullDay: this.isFullDay,
      startTime: JsonUtil.stringToJson(this.startTime),
      endTime: JsonUtil.stringToJson(this.endTime),
      reason: JsonUtil.stringToJson(this.reason),
      isRecurring: this.isRecurring,
      recurringPattern: JsonUtil.stringToJson(this.recurringPattern),
      isApproved: this.isApproved,
      approvedBy: JsonUtil.stringToJson(this.approvedBy),
      approvedAt: this.approvedAt ? formatTimestamp(this.approvedAt) : undefined,
    };
  }

  isActive(): boolean {
    const today = new Date().toISOString().split('T')[0];
    return this.isApproved && 
           this.startDate <= today && 
           this.endDate >= today;
  }

  conflictsWith(date: string, startTime?: string, endTime?: string): boolean {
    if (date < this.startDate || date > this.endDate) {
      return false;
    }

    if (this.isFullDay) {
      return true;
    }

    if (!startTime || !endTime || !this.startTime || !this.endTime) {
      return false;
    }

    return !(endTime <= this.startTime || startTime >= this.endTime);
  }
} 