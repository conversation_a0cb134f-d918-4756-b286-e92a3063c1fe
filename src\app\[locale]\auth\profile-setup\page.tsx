'use client';

import { useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';
import { CustomDatePicker } from '../../components/ui/CustomDatePicker';
import { PhotoUpload } from '../../components/ui/PhotoUpload';
import { useAuth, AuthenticatedRoute } from '../../../lib/firebase/context/AuthContext';
import { UserPreferences } from '../../../lib/models/types';
import { updateUserProfileCompletionStatus } from '../../../lib/firebase/services/auth';
import { FirestoreService } from "../../../lib/firebase/services";

export default function ProfileSetupPage() {
  return (
    <AuthenticatedRoute>
      <ProfileSetupContent />
    </AuthenticatedRoute>
  );
}

function ProfileSetupContent() {
  const params = useParams();
  const router = useRouter();
  const { userData, user, refreshUser } = useAuth();
  const locale = params.locale as string;
  const t = useTranslations('auth-profile-setup');

  // 只收集 PortalUserData 中缺少的字段，避免重复
  const [formData, setFormData] = useState({
    displayName: userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim(),
    dateOfBirth: userData?.dateOfBirth ? new Date(userData.dateOfBirth).toISOString().split('T')[0] : '',
    bio: userData?.bio || '',
    photoURL: userData?.photoURL || '',
    preferences: {
      notificationsEnabled: userData?.preferences?.notificationsEnabled ?? true,
      language: userData?.preferences?.language || locale,
      theme: userData?.preferences?.theme || 'system',
      emailNotifications: userData?.preferences?.emailNotifications ?? true,
      smsNotifications: userData?.preferences?.smsNotifications ?? true,
    } as UserPreferences,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // 处理照片上传成功
  const handlePhotoUploaded = (photoURL: string) => {
    setFormData({ ...formData, photoURL });
    setErrors({ ...errors, photo: '' });
  };

  // 处理照片上传错误
  const handlePhotoError = (error: string) => {
    setErrors({ ...errors, photo: error });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsLoading(true);
    setErrors({});

    // 验证出生日期不能超过今天
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 重置时间为当天开始
      
      if (birthDate > today) {
        setErrors({ dateOfBirth: t('dateOfBirthFuture') });
        setIsLoading(false);
        return;
      }
    }

    try {
      if (!user?.uid) {
        setErrors({ general: 'User not authenticated' });
        setIsLoading(false);
        return;
      }

      // 准备更新数据
      const updateData = {
        displayName: formData.displayName,
        dateOfBirth: formData.dateOfBirth,
        bio: formData.bio,
        photoURL: formData.photoURL,
        preferences: formData.preferences,
      };

      // 首先获取用户数据文档以获取正确的文档 ID
      const userDataQuery = await FirestoreService.getMany(
        'portal-user-data',
        {
          where: [{ field: 'fid', operator: '==', value: user.uid }],
          limit: 1
        }
      );

      if (!userDataQuery.success || !userDataQuery.data || userDataQuery.data.length === 0) {
        setErrors({ general: 'User data not found' });
        setIsLoading(false);
        return;
      }

      const userDataDoc = userDataQuery.data[0];
      const docId = userDataDoc.id;

      if (!docId) {
        setErrors({ general: 'User document ID not found' });
        setIsLoading(false);
        return;
      }

      // 直接使用 Firestore 服务更新用户资料
      const result = await FirestoreService.update(
        'portal-user-data',
        docId,
        updateData
      );

      if (result.success) {
        // 更新个人资料完成状态
        await updateUserProfileCompletionStatus(user.uid, true);
        // 刷新用户数据
        await refreshUser();
        // 资料完善成功，跳转到仪表板
        router.push(`/${locale}/dashboard`);
      } else {
        setErrors({ general: result.error || t('updateError') });
      }
    } catch (error) {
      console.error('Profile setup error:', error);
      setErrors({ general: t('updateError') });
    } finally {
      setIsLoading(false);
    }
  };



  const handleSkip = async () => {
    try {
      // 更新个人资料完成状态（跳过也算完成）
      if (user) {
        await updateUserProfileCompletionStatus(user.uid, true);
        // 刷新用户数据
        await refreshUser();
      }
      // 跳过资料完善，直接进入仪表板
      router.push(`/${locale}/dashboard`);
    } catch (error) {
      console.error('Error updating profile completion status:', error);
      // 即使更新失败，也允许跳过
      router.push(`/${locale}/dashboard`);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#FDECCE] to-[#F2D3A4]">
      <div className="w-full max-w-2xl mx-4">
        {/* 主设置卡片 */}
        <div className="backdrop-blur-md bg-white/80 rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
          <div className="p-8">
            {/* Logo和标题 */}
            <div className="text-center mb-8">
              <Link href={`/${locale}`} className="inline-block">
                <div className="w-16 h-16 bg-gradient-to-br from-[#A126FF] to-[#8a20d8] rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 mx-auto shadow-lg">
                  ON
                </div>
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
              <p className="text-gray-600 text-sm leading-relaxed">
                {t('subtitle')}
              </p>
            </div>

            {/* 当前用户信息显示 */}
            <div className="mb-8 p-4 bg-gradient-to-r from-[#A126FF]/10 to-[#8a20d8]/10 rounded-2xl border border-[#A126FF]/20">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-[#A126FF] to-[#8a20d8] rounded-xl flex items-center justify-center text-white font-bold">
                  {userData?.firstName?.[0] || 'U'}
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {userData?.firstName} {userData?.lastName}
                  </p>
                                     <p className="text-sm text-gray-600">
                     {userData?.phoneNumber}
                   </p>
                </div>
              </div>
            </div>
            
            {/* 错误消息 */}
            {errors.general && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl text-sm">
                {errors.general}
              </div>
            )}
            
            {/* 资料表单 */}
            <form className="space-y-6" onSubmit={handleSubmit}>
              {/* 头像上传 */}
              <PhotoUpload
                userId={user?.uid || ''}
                currentPhotoURL={userData?.photoURL}
                onPhotoUploaded={handlePhotoUploaded}
                onError={handlePhotoError}
                size="large"
                className="mb-4"
                userName={userData?.firstName || userData?.displayName || ''}
              />
              {errors.photo && (
                <p className="text-sm text-red-600 text-center">{errors.photo}</p>
              )}

              {/* 显示名称 */}
              <Input
                id="displayName"
                type="text"
                label={t('displayName')}
                placeholder={t('displayNamePlaceholder')}
                value={formData.displayName}
                onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
                error={errors.displayName}
              />

              {/* 出生日期 */}
              <CustomDatePicker
                id="dateOfBirth"
                label={t('dateOfBirth')}
                value={formData.dateOfBirth}
                onChange={(value: string) => setFormData({ ...formData, dateOfBirth: value })}
                error={errors.dateOfBirth}
                maxDate={new Date().toISOString().split('T')[0]}
                minDate={new Date(new Date().getFullYear() - 100, 0, 1).toISOString().split('T')[0]}
              />

              {/* 个人简介 */}
              <div>
                <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('bio')}
                </label>
                <textarea
                  id="bio"
                  rows={4}
                  placeholder={t('bioPlaceholder')}
                  value={formData.bio}
                  onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200"
                  maxLength={500}
                />
                <p className="mt-1 text-sm text-gray-500 text-right">
                  {formData.bio.length}/500
                </p>
              </div>

              {/* 偏好设置 */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">{t('notificationPreferences')}</h3>
                
                <div className="space-y-3">
                  <label className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <span className="text-sm font-medium text-gray-700">{t('enableNotifications')}</span>
                    <input
                      type="checkbox"
                      checked={formData.preferences.notificationsEnabled}
                      onChange={(e) => setFormData({
                        ...formData,
                        preferences: {
                          ...formData.preferences,
                          notificationsEnabled: e.target.checked
                        }
                      })}
                      className="w-4 h-4 text-[#A126FF] border-gray-300 rounded focus:ring-[#A126FF]"
                    />
                  </label>

                  <label className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <span className="text-sm font-medium text-gray-700">{t('emailNotifications')}</span>
                    <input
                      type="checkbox"
                      checked={formData.preferences.emailNotifications}
                      onChange={(e) => setFormData({
                        ...formData,
                        preferences: {
                          ...formData.preferences,
                          emailNotifications: e.target.checked
                        }
                      })}
                      className="w-4 h-4 text-[#A126FF] border-gray-300 rounded focus:ring-[#A126FF]"
                    />
                  </label>

                  <label className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                    <span className="text-sm font-medium text-gray-700">{t('smsNotifications')}</span>
                    <input
                      type="checkbox"
                      checked={formData.preferences.smsNotifications}
                      onChange={(e) => setFormData({
                        ...formData,
                        preferences: {
                          ...formData.preferences,
                          smsNotifications: e.target.checked
                        }
                      })}
                      className="w-4 h-4 text-[#A126FF] border-gray-300 rounded focus:ring-[#A126FF]"
                    />
                  </label>
                </div>

                {/* 主题选择 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('themePreference')}
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'light', label: t('themeLight') },
                      { value: 'dark', label: t('themeDark') },
                      { value: 'system', label: t('themeSystem') }
                    ].map((theme) => (
                      <label key={theme.value} className="flex items-center">
                        <input
                          type="radio"
                          name="theme"
                          value={theme.value}
                          checked={formData.preferences.theme === theme.value}
                          onChange={(e) => setFormData({
                            ...formData,
                            preferences: {
                              ...formData.preferences,
                              theme: e.target.value as 'light' | 'dark' | 'system'
                            }
                          })}
                          className="w-4 h-4 text-[#A126FF] border-gray-300 focus:ring-[#A126FF]"
                        />
                        <span className="ml-2 text-sm text-gray-700">{theme.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              {/* 按钮 */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <Button
                  type="button"
                  onClick={handleSkip}
                  className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 rounded-2xl py-4 transition-all duration-300"
                >
                  {t('skipButton')}
                </Button>
                
                <Button
                  type="submit"
                  className="flex-1 bg-gradient-to-r from-[#A126FF] to-[#8a20d8] hover:from-[#8a20d8] hover:to-[#6d1bb3] text-white font-semibold py-4 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg"
                  loading={isLoading}
                  disabled={isLoading}
                >
                  {isLoading ? t('saving') : t('saveProfile')}
                </Button>
              </div>
            </form>
          </div>
        </div>
        
        {/* 页脚 */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            © {new Date().getFullYear()} OneNata Pet Store. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
} 