'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';

interface NotificationPermissionModalProps {
  isOpen: boolean;
  onConfirm: (enabled: boolean) => void;
  onClose: () => void;
}

export default function NotificationPermissionModal({
  isOpen,
  onConfirm,
  onClose
}: NotificationPermissionModalProps) {
  const t = useTranslations('auth-signup');
  const [selectedOption, setSelectedOption] = useState<boolean>(true);

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm(selectedOption);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* 弹窗内容 */}
      <div className="relative bg-white rounded-2xl shadow-2xl p-8 mx-4 max-w-md w-full transform transition-all duration-300 scale-100">
        {/* 图标 */}
        <div className="flex justify-center mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-[#A126FF] to-[#C084FC] rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zM12 12l0 0" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636" />
            </svg>
          </div>
        </div>

        {/* 标题 */}
        <h3 className="text-xl font-bold text-gray-900 text-center mb-4">
          {t('notificationPermission.title')}
        </h3>

        {/* 描述 */}
        <p className="text-gray-600 text-center mb-6 leading-relaxed">
          {t('notificationPermission.description')}
        </p>

        {/* 选项 */}
        <div className="space-y-3 mb-8">
          <label className="flex items-center p-4 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-[#A126FF] transition-colors">
            <input
              type="radio"
              name="notification"
              checked={selectedOption === true}
              onChange={() => setSelectedOption(true)}
              className="w-5 h-5 text-[#A126FF] focus:ring-[#A126FF] focus:ring-2"
            />
            <div className="ml-4">
              <div className="font-medium text-gray-900">
                {t('notificationPermission.enableOption')}
              </div>
              <div className="text-sm text-gray-500">
                {t('notificationPermission.enableDescription')}
              </div>
            </div>
          </label>

          <label className="flex items-center p-4 border-2 border-gray-200 rounded-xl cursor-pointer hover:border-[#A126FF] transition-colors">
            <input
              type="radio"
              name="notification"
              checked={selectedOption === false}
              onChange={() => setSelectedOption(false)}
              className="w-5 h-5 text-[#A126FF] focus:ring-[#A126FF] focus:ring-2"
            />
            <div className="ml-4">
              <div className="font-medium text-gray-900">
                {t('notificationPermission.disableOption')}
              </div>
              <div className="text-sm text-gray-500">
                {t('notificationPermission.disableDescription')}
              </div>
            </div>
          </label>
        </div>

        {/* 按钮 */}
        <div className="flex gap-3">
          <button
            onClick={onClose}
            className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
          >
            {t('notificationPermission.later')}
          </button>
          <button
            onClick={handleConfirm}
            className="flex-1 px-6 py-3 bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white rounded-xl font-medium hover:from-[#9020E6] hover:to-[#B574E8] transition-all duration-200 transform hover:scale-105"
          >
            {t('notificationPermission.confirm')}
          </button>
        </div>
      </div>
    </div>
  );
} 