'use client';

import React, { useState } from 'react';

interface PhotoApiResponse {
  success: boolean;
  data?: {
    photoUrl: string;
    contentType: string;
  };
  error?: string;
}

const TestPhotoDebug = () => {
  const [photoUrl, setPhotoUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [result, setResult] = useState<PhotoApiResponse | null>(null);

  const testPhotoFetch = async () => {
    if (!photoUrl) return;
    
    setLoading(true);
    setError('');
    setResult(null);

    try {
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_photo_url',
          params: {
            photo_reference: photoUrl,
            maxwidth: '400',
            maxheight: '300'
          }
        })
      });

      const data = await response.json();
      setResult(data);
      
      if (!data.success) {
        setError(data.error || 'Unknown error');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">Photo Debug Test</h1>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Photo Reference:</label>
          <input
            type="text"
            value={photoUrl}
            onChange={(e) => setPhotoUrl(e.target.value)}
            placeholder="Enter photo reference..."
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>
        
        <button
          onClick={testPhotoFetch}
          disabled={loading || !photoUrl}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Photo Fetch'}
        </button>

        {error && (
          <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            Error: {error}
          </div>
        )}

        {result && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Result:</h3>
            <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
            
            {result.success && result.data?.photoUrl && (
              <div>
                <h4 className="font-medium mb-2">Image Preview:</h4>
                <div className="border border-gray-300 rounded p-4">
                  <img
                    src={result.data.photoUrl}
                    alt="Test photo"
                    className="max-w-full h-auto"
                    onError={(e) => {
                      console.error('Image load error:', e);
                      setError('Image failed to load');
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully');
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default TestPhotoDebug; 