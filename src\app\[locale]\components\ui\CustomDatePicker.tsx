'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

interface CustomDatePickerProps {
  id?: string;
  label?: string;
  value?: string; // ISO date string (YYYY-MM-DD)
  onChange?: (value: string) => void;
  error?: string;
  className?: string;
  required?: boolean;
  minDate?: string; // ISO date string
  maxDate?: string; // ISO date string
}

export const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  id,
  label,
  value = '',
  onChange,
  error,
  className = '',
  required = false,
  minDate,
  maxDate = new Date().toISOString().split('T')[0],
}) => {
  const t = useTranslations('datePicker');
  const [selectedDate, setSelectedDate] = useState({
    year: '',
    month: '',
    day: ''
  });

  // 解析初始值
  useEffect(() => {
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setSelectedDate({
          year: date.getFullYear().toString(),
          month: (date.getMonth() + 1).toString().padStart(2, '0'),
          day: date.getDate().toString().padStart(2, '0')
        });
      }
    } else {
      setSelectedDate({ year: '', month: '', day: '' });
    }
  }, [value]);

  // 生成年份选项 (受 minDate 和 maxDate 限制)
  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const maxYear = maxDate ? new Date(maxDate).getFullYear() : currentYear;
    const minYear = minDate ? new Date(minDate).getFullYear() : currentYear - 100;
    
    const years = [];
    for (let year = maxYear; year >= minYear; year--) {
      years.push(year);
    }
    return years;
  };

  // 生成月份选项 (受选中年份和日期限制影响)
  const generateMonths = () => {
    if (!selectedDate.year) return [];
    
    const year = parseInt(selectedDate.year);
    const months = [];
    
    for (let month = 1; month <= 12; month++) {
      const monthStr = month.toString().padStart(2, '0');
      
      // 检查是否超过最大日期限制
      if (maxDate) {
        const maxDateObj = new Date(maxDate);
        const maxYear = maxDateObj.getFullYear();
        const maxMonth = maxDateObj.getMonth() + 1;
        
        if (year > maxYear || (year === maxYear && month > maxMonth)) {
          continue;
        }
      }
      
      // 检查是否小于最小日期限制
      if (minDate) {
        const minDateObj = new Date(minDate);
        const minYear = minDateObj.getFullYear();
        const minMonth = minDateObj.getMonth() + 1;
        
        if (year < minYear || (year === minYear && month < minMonth)) {
          continue;
        }
      }
      
      months.push({
        value: monthStr,
        label: month.toString()
      });
    }
    return months;
  };

  // 生成日期选项 (受选中年份、月份和日期限制影响)
  const generateDays = () => {
    if (!selectedDate.year || !selectedDate.month) return [];
    
    const year = parseInt(selectedDate.year);
    const month = parseInt(selectedDate.month);
    const daysInMonth = new Date(year, month, 0).getDate();
    
    const days = [];
    for (let day = 1; day <= daysInMonth; day++) {
      const dayStr = day.toString().padStart(2, '0');
      
      // 检查是否超过最大日期限制
      if (maxDate) {
        const maxDateObj = new Date(maxDate);
        const currentDateObj = new Date(year, month - 1, day);
        
        if (currentDateObj > maxDateObj) {
          continue;
        }
      }
      
      // 检查是否小于最小日期限制
      if (minDate) {
        const minDateObj = new Date(minDate);
        const currentDateObj = new Date(year, month - 1, day);
        
        if (currentDateObj < minDateObj) {
          continue;
        }
      }
      
      days.push(dayStr);
    }
    return days;
  };

  // 处理日期变化
  const handleDateChange = (type: 'year' | 'month' | 'day', newValue: string) => {
    const newDate = { ...selectedDate, [type]: newValue };
    
    // 如果选择了新的年份或月份，重置日期（如果当前日期在新月份中不存在或超出限制）
    if (type === 'year' || type === 'month') {
      const year = parseInt(newDate.year || '2000');
      const month = parseInt(newDate.month || '1');
      const day = parseInt(newDate.day || '1');
      const daysInMonth = new Date(year, month, 0).getDate();
      
      if (day > daysInMonth) {
        newDate.day = '';
      } else if (newDate.day) {
        // 检查当前选择的日期是否超出限制
        const currentDateObj = new Date(year, month - 1, day);
        
        if (maxDate && currentDateObj > new Date(maxDate)) {
          newDate.day = '';
        }
        
        if (minDate && currentDateObj < new Date(minDate)) {
          newDate.day = '';
        }
      }
    }
    
    setSelectedDate(newDate);

    // 如果所有字段都有值，则触发onChange
    if (newDate.year && newDate.month && newDate.day) {
      const isoDate = `${newDate.year}-${newDate.month}-${newDate.day}`;
      
      // 最后验证整个日期是否在允许范围内
      const selectedDateObj = new Date(isoDate);
      let isValid = true;
      
      if (maxDate && selectedDateObj > new Date(maxDate)) {
        isValid = false;
      }
      
      if (minDate && selectedDateObj < new Date(minDate)) {
        isValid = false;
      }
      
      if (isValid) {
        onChange?.(isoDate);
      } else {
        onChange?.('');
      }
    } else {
      onChange?.('');
    }
  };

  const years = generateYears();
  const months = generateMonths();
  const days = generateDays();

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="grid grid-cols-3 gap-3">
        {/* 年份选择 */}
        <div className="relative">
          <select
            value={selectedDate.year}
            onChange={(e) => handleDateChange('year', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200 appearance-none bg-white"
          >
            <option value="">{t('year')}</option>
            {years.map(year => (
              <option key={year} value={year}>
                {year}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* 月份选择 */}
        <div className="relative">
          <select
            value={selectedDate.month}
            onChange={(e) => handleDateChange('month', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200 appearance-none bg-white disabled:bg-gray-50 disabled:text-gray-400"
            disabled={!selectedDate.year}
          >
            <option value="">{t('month')}</option>
            {months.map(month => (
              <option key={month.value} value={month.value}>
                {month.label}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* 日期选择 */}
        <div className="relative">
          <select
            value={selectedDate.day}
            onChange={(e) => handleDateChange('day', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200 appearance-none bg-white disabled:bg-gray-50 disabled:text-gray-400"
            disabled={!selectedDate.year || !selectedDate.month}
          >
            <option value="">{t('day')}</option>
            {days.map(day => (
              <option key={day} value={day}>
                {day}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* 错误消息 */}
      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}
    </div>
  );
};

export default CustomDatePicker; 