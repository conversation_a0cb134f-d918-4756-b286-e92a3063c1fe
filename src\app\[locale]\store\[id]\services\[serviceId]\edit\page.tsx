/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '../../../../../components/ui/Button';
import { Input } from '../../../../../components/ui/Input';
import { StoreHeader } from '../../../../../components/ui/StoreHeader';
import { useAuth, AuthenticatedRoute } from '../../../../../../lib/firebase/context/AuthContext';
import {
  ServiceCategory,
  ServiceBreed,
  StoreServiceStatus
} from '../../../../../../lib/models/types';
import { StaffMember } from '../../../../../../lib/services/staff_services';
import { StoreServiceData, CreateStoreServiceData } from '../../../../../../lib/services/store_services';
import storeService from '../../../../../../lib/services/store_services';
import staffService from '../../../../../../lib/services/staff_services';
import {
  FiArrowLeft,
  FiSave,
  FiUsers,
  FiPlus,
  FiX,
  FiEdit3,
  FiSettings,
  FiAlertCircle,
  FiCheck,
  FiTrash2,
  FiSearch
} from 'react-icons/fi';

interface EditServiceFormData {
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  description?: string;
  commission: number;
  status: StoreServiceStatus;
  isOnlineBookingEnabled: boolean;
  requiresApproval: boolean;
  staffIds: string[];
}

export default function EditServicePage() {
  return (
    <AuthenticatedRoute>
      <EditServiceContent />
    </AuthenticatedRoute>
  );
}

function EditServiceContent() {
  const params = useParams();
  const router = useRouter();
  const { userData } = useAuth();
  const t = useTranslations('editService');
  const storeId = params.id as string;
  const serviceId = params.serviceId as string;

  // State
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [service, setService] = useState<StoreServiceData | null>(null);
  const [allStaff, setAllStaff] = useState<StaffMember[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showStaffModal, setShowStaffModal] = useState(false);

  // Form data
  const [formData, setFormData] = useState<EditServiceFormData>({
    serviceName: '',
    serviceCategory: ServiceCategory.GROOMING,
    serviceBreed: ServiceBreed.DOG,
    description: '',
    commission: 0.1,
    status: StoreServiceStatus.ACTIVE,
    isOnlineBookingEnabled: true,
    requiresApproval: false,
    staffIds: []
  });

  // Load data
  useEffect(() => {
    loadData();
  }, [storeId, serviceId]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Load service
      const serviceResult = await storeService.getStoreServiceById(storeId, serviceId);
      if (!serviceResult.success || !serviceResult.data) {
        alert(t('serviceNotFound'));
        router.push(`/store/${storeId}/services`);
        return;
      }

      // Load all staff
      const staffResult = await staffService.getStoreStaff(storeId);
      
      setService(serviceResult.data);
      setAllStaff(staffResult.success ? staffResult.data || [] : []);
      
      // Set form data
      setFormData({
        serviceName: '',
        serviceCategory: serviceResult.data.serviceCategory,
        serviceBreed: serviceResult.data.serviceBreed,
        description: serviceResult.data.description || '',
        commission: serviceResult.data.commission || 0.1,
        status: serviceResult.data.status,
        isOnlineBookingEnabled: serviceResult.data.isOnlineBookingEnabled || false,
        requiresApproval: serviceResult.data.requiresApproval || false,
        staffIds: serviceResult.data.staffIds || []
      });

    } catch (error) {
      console.error('Error loading data:', error);
      alert(t('errorLoadingData'));
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!userData?.uid) {
      alert(t('pleaseLogin'));
      return;
    }

    if (!formData.serviceName.trim()) {
      alert(t('serviceNameRequired'));
      return;
    }

    if (formData.staffIds.length === 0) {
      alert(t('atLeastOneStaffRequired'));
      return;
    }

    try {
      setSaving(true);

      const updateData: Partial<CreateStoreServiceData> = {
        // serviceName: formData.serviceName.trim(), // TODO: check if this is correct
        serviceCategory: formData.serviceCategory,
        serviceBreed: formData.serviceBreed,
        description: formData.description?.trim(),
        commission: formData.commission,
        // status: formData.status,  
        isOnlineBookingEnabled: formData.isOnlineBookingEnabled,
        requiresApproval: formData.requiresApproval,
        staffIds: formData.staffIds
      };

      const result = await storeService.updateStoreService(
        storeId,
        serviceId,
        updateData,
        userData.uid
      );

      if (result.success) {
        alert(t('serviceUpdatedSuccessfully'));
        router.push(`/store/${storeId}/services/${serviceId}`);
      } else {
        alert(result.error || t('updateFailed'));
      }
    } catch (error) {
      console.error('Error updating service:', error);
      alert(t('updateFailed'));
    } finally {
      setSaving(false);
    }
  };

  const handleAddStaff = (staff: StaffMember) => {
    if (!formData.staffIds.includes(staff.userData.uid)) {
      setFormData(prev => ({
        ...prev,
        staffIds: [...prev.staffIds, staff.userData.uid]
      }));
    }
    setShowStaffModal(false);
  };

  const handleRemoveStaff = (staffId: string) => {
    setFormData(prev => ({
      ...prev,
      staffIds: prev.staffIds.filter(id => id !== staffId)
    }));
  };

  const getStaffDisplayName = (staff: StaffMember): string => {
    return staff.userData.displayName ||
           `${staff.userData.firstName || ''} ${staff.userData.lastName || ''}`.trim() ||
           staff.email || 'Unknown Staff';
  };

  const filteredStaff = allStaff.filter(staff => {
    if (formData.staffIds.includes(staff.userData.uid)) return false;
    if (!searchTerm) return true;
    
    const displayName = getStaffDisplayName(staff).toLowerCase();
    const email = (staff.email || '').toLowerCase();
    const search = searchTerm.toLowerCase();
    
    return displayName.includes(search) || email.includes(search);
  });

  const assignedStaff = allStaff.filter(staff => 
    formData.staffIds.includes(staff.userData.uid)
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="services" />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('title')} storeId={storeId} currentPage="services" />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push(`/store/${storeId}/services/${serviceId}`)}
                className="bg-slate-100 hover:bg-slate-200 text-slate-700"
              >
                <FiArrowLeft className="w-4 h-4 mr-2" />
                {t('back')}
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                  <FiEdit3 className="w-8 h-8 mr-3 text-violet-600" />
                  {t('editService')}
                </h1>
                <p className="text-slate-600 mt-1">
                  {/* {service?.serviceName} */}
                </p>
              </div>
            </div>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
            >
              {saving ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              ) : (
                <FiSave className="w-4 h-4 mr-2" />
              )}
              {t('saveChanges')}
            </Button>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white rounded-2xl shadow-lg p-6 space-y-6">
          {/* Basic Info */}
          <div>
            <h3 className="text-lg font-semibold text-slate-800 mb-4">{t('basicInfo')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  {t('serviceName')} *
                </label>
                <Input
                  value={formData.serviceName}
                  onChange={(e) => setFormData(prev => ({ ...prev, serviceName: e.target.value }))}
                  placeholder={t('enterServiceName')}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  {t('serviceCategory')} *
                </label>
                <select
                  value={formData.serviceCategory}
                  onChange={(e) => setFormData(prev => ({ ...prev, serviceCategory: e.target.value as ServiceCategory }))}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
                >
                  {Object.values(ServiceCategory).map(category => (
                    <option key={category} value={category}>
                      {t(`categories.${category.toLowerCase()}`)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  {t('serviceBreed')} *
                </label>
                <select
                  value={formData.serviceBreed}
                  onChange={(e) => setFormData(prev => ({ ...prev, serviceBreed: e.target.value as ServiceBreed }))}
                  className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
                >
                  {Object.values(ServiceBreed).map(breed => (
                    <option key={breed} value={breed}>
                      {t(`breeds.${breed.toLowerCase()}`)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  {t('commission')} (%)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={formData.commission * 100}
                  onChange={(e) => setFormData(prev => ({ ...prev, commission: parseFloat(e.target.value) / 100 }))}
                  placeholder="10"
                />
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-slate-700 mb-2">
                {t('description')}
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t('enterDescription')}
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
              />
            </div>
          </div>

          {/* Settings */}
          <div>
            <h3 className="text-lg font-semibold text-slate-800 mb-4">{t('settings')}</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                <div>
                  <p className="font-medium text-slate-900">{t('serviceStatus')}</p>
                  <p className="text-sm text-slate-500">{t('serviceStatusDesc')}</p>
                </div>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as StoreServiceStatus }))}
                  className="px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
                >
                  <option value={StoreServiceStatus.ACTIVE}>{t('active')}</option>
                  <option value={StoreServiceStatus.INACTIVE}>{t('inactive')}</option>
                </select>
              </div>

              <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                <div>
                  <p className="font-medium text-slate-900">{t('onlineBooking')}</p>
                  <p className="text-sm text-slate-500">{t('onlineBookingDesc')}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.isOnlineBookingEnabled}
                    onChange={(e) => setFormData(prev => ({ ...prev, isOnlineBookingEnabled: e.target.checked }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-violet-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                <div>
                  <p className="font-medium text-slate-900">{t('requiresApproval')}</p>
                  <p className="text-sm text-slate-500">{t('requiresApprovalDesc')}</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.requiresApproval}
                    onChange={(e) => setFormData(prev => ({ ...prev, requiresApproval: e.target.checked }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-violet-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-violet-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Staff Management */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-800">{t('staffManagement')}</h3>
              <Button
                onClick={() => setShowStaffModal(true)}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              >
                <FiPlus className="w-4 h-4 mr-2" />
                {t('addStaff')}
              </Button>
            </div>

            {assignedStaff.length === 0 ? (
              <div className="text-center py-8 bg-slate-50 rounded-lg">
                <FiUsers className="w-12 h-12 text-slate-300 mx-auto mb-2" />
                <p className="text-slate-500">{t('noStaffAssigned')}</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {assignedStaff.map(staff => (
                  <div key={staff.userData.uid} className="p-4 border border-slate-200 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-medium text-sm">
                            {getStaffDisplayName(staff).charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <p className="font-medium text-slate-900">{getStaffDisplayName(staff)}</p>
                          <p className="text-sm text-slate-500">{staff.email}</p>
                        </div>
                      </div>
                      <Button
                        onClick={() => handleRemoveStaff(staff.userData.uid)}
                        className="bg-red-100 hover:bg-red-200 text-red-600 p-2"
                      >
                        <FiX className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Staff Selection Modal */}
        {showStaffModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
              <div className="p-6 border-b border-slate-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-slate-900">{t('addStaff')}</h3>
                  <Button
                    onClick={() => setShowStaffModal(false)}
                    className="bg-slate-100 hover:bg-slate-200 text-slate-700 p-2"
                  >
                    <FiX className="w-4 h-4" />
                  </Button>
                </div>
                <div className="mt-4">
                  <div className="relative">
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
                    <Input
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder={t('searchStaff')}
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>
              
              <div className="p-6 overflow-y-auto max-h-96">
                {filteredStaff.length === 0 ? (
                  <div className="text-center py-8">
                    <FiUsers className="w-12 h-12 text-slate-300 mx-auto mb-2" />
                    <p className="text-slate-500">{t('noStaffFound')}</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredStaff.map(staff => (
                      <div
                        key={staff.userData.uid}
                        onClick={() => handleAddStaff(staff)}
                        className="p-4 border border-slate-200 rounded-lg hover:border-violet-300 hover:bg-violet-50 cursor-pointer transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-medium text-sm">
                              {getStaffDisplayName(staff).charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-slate-900">{getStaffDisplayName(staff)}</p>
                            <p className="text-sm text-slate-500">{staff.email}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 