/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/app/[locale]/components/ui/Button';
import { AuthenticatedRoute, useAuth } from '@/app/lib/firebase/context/AuthContext';
import { StoreHeader } from '@/app/[locale]/components/ui/StoreHeader';
import CalendarDatePicker from '@/app/[locale]/components/ui/CalendarDatePicker';
import { useTranslations } from 'next-intl';
import appointmentService from '@/app/lib/services/appointment_service';
import customerService from '@/app/lib/services/customer_service';
import staffService from '@/app/lib/services/staff_services';
import storeService from '@/app/lib/services/store_services';
import { Customer } from '@/app/lib/models/customer';
import { StoreServiceStatus, AppointmentStatus } from '@/app/lib/models/types';
import { StoreServiceData } from '@/app/lib/services/store_services';
import { StaffMember } from '@/app/lib/services/staff_services';
import { 
  FiArrowLeft, 
  FiCalendar,
  FiCheck,
  FiUser,
  FiHeart,
  FiSettings,
  FiUsers,
  FiClock,
  FiAlertCircle
} from 'react-icons/fi';

interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
  status: string;
}

export default function EditAppointmentPage() {
  return (
    <AuthenticatedRoute>
      <EditAppointmentContent />
    </AuthenticatedRoute>
  );
}

function EditAppointmentContent() {
  const router = useRouter();
  const params = useParams();
  const { userData } = useAuth();
  const t = useTranslations('editAppointment');
  const storeId = params.id as string;
  const appointmentId = params.appointmentId as string;

  // State
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [appointment, setAppointment] = useState<any>(null);
  
  // Form data
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedStoreService, setSelectedStoreService] = useState<StoreServiceData | null>(null);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [duration, setDuration] = useState<number>(60);
  const [notes, setNotes] = useState<string>('');
  const [customerNotes, setCustomerNotes] = useState<string>('');

  // Available options
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [storeServices, setStoreServices] = useState<StoreServiceData[]>([]);
  const [availableStaff, setAvailableStaff] = useState<StaffMember[]>([]);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);

  // Load appointment data
  const loadAppointmentData = async () => {
    try {
      setLoading(true);
      
      const result = await appointmentService.getAppointmentById(appointmentId);
      
      if (result.success && result.data) {
        const appt = result.data;
        setAppointment(appt);
        
        // Set form data from appointment
        setSelectedDate(appt.timeInfo?.date || '');
        setSelectedTime(appt.timeInfo?.startTime || '');
        setDuration(appt.timeInfo?.duration || 60);
        setNotes(appt.notes || '');
        setCustomerNotes(appt.customerNotes || '');
        
        // Load related data
        await Promise.all([
          loadCustomers(),
          loadStoreServices(), 
          loadStoreStaff()
        ]);

        // Find and set current selections
        await setCurrentSelections(appt);
        
      } else {
        console.error('Appointment not found');
        router.push(`/store/${storeId}/appointments`);
      }
    } catch (error) {
      console.error('Error loading appointment data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Set current selections based on appointment data
  const setCurrentSelections = async (appt: any) => {
    // Find current customer
    const customer = customers.find(c => c.customerData.uid === appt.customerId);
    if (customer) {
      setSelectedCustomer(customer);
    }

    // Find current service
    const service = storeServices.find(s => s.sid === appt.serviceId);
    if (service) {
      setSelectedStoreService(service);
      // Load staff for this service
      await loadStaffForService(service);
    }

    // Find current staff
    const staff = availableStaff.find(s => s.userData.uid === appt.staffId);
    if (staff) {
      setSelectedStaff(staff);
      // Load available dates for this staff
      await loadAvailableDates(staff.userData.uid);
      // Load time slots for the current date
      if (appt.timeInfo?.date && service) {
        await loadTimeSlots(staff.userData.uid, service.sid, appt.timeInfo.date, appt.timeInfo?.duration || 60);
      }
    }
  };

  // Load customers
  const loadCustomers = async () => {
    try {
      const result = await customerService.getCustomersByStore(storeId);
      if (result.success && result.data) {
        setCustomers(result.data);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
    }
  };

  // Load store services
  const loadStoreServices = async () => {
    try {
      const result = await storeService.getStoreServices(storeId);
      if (result.success && result.data) {
        const activeServices = result.data.filter(service => 
          service.status === StoreServiceStatus.ACTIVE && 
          service.isOnlineBookingEnabled &&
          service.staffIds && 
          service.staffIds.length > 0
        );
        setStoreServices(activeServices);
      }
    } catch (error) {
      console.error('Error loading store services:', error);
    }
  };

  // Load store staff
  const loadStoreStaff = async () => {
    try {
      const result = await staffService.getStoreStaff(storeId);
      if (result.success && result.data) {
        setAvailableStaff(result.data.filter(staff => staff.isActive));
      }
    } catch (error) {
      console.error('Error loading staff:', error);
    }
  };

  // Load staff for specific service
  const loadStaffForService = async (service: StoreServiceData) => {
    try {
      if (!service.staffIds || service.staffIds.length === 0) {
        return;
      }

      const allStaffResult = await staffService.getStoreStaff(storeId);
      
      if (allStaffResult.success && allStaffResult.data) {
        const staffForService = allStaffResult.data.filter(staff => 
          service.staffIds!.includes(staff.userData.uid) && staff.isActive
        );
        setAvailableStaff(staffForService);
      }
    } catch (error) {
      console.error('Error loading staff for service:', error);
    }
  };

  // Load available dates for staff
  const loadAvailableDates = async (staffId: string) => {
    try {
      const result = await appointmentService.getStaffAvailableDates(staffId, storeId);
      if (result.success && result.data) {
        setAvailableDates(result.data);
      }
    } catch (error) {
      console.error('Error loading available dates:', error);
    }
  };

  // Load time slots
  const loadTimeSlots = async (staffId: string, serviceId: string, date: string, serviceDuration: number) => {
    try {
      const result = await appointmentService.getAvailableTimeSlots({
        storeId,
        staffId,
        serviceId,
        date,
        duration: serviceDuration
      });
      
      if (result.success && result.data) {
        const slots: TimeSlot[] = result.data.timeSlots.map((slot: { startTime: string; endTime: string; available?: boolean; status?: string }) => ({
          startTime: slot.startTime,
          endTime: slot.endTime,
          available: slot.available || false,
          status: slot.status || 'blocked'
        }));
        setTimeSlots(slots);
      }
    } catch (error) {
      console.error('Error loading time slots:', error);
    }
  };

  // Handle service selection change
  const handleServiceChange = async (service: StoreServiceData) => {
    setSelectedStoreService(service);
    setSelectedStaff(null);
    setSelectedDate('');
    setSelectedTime('');
    setTimeSlots([]);
    setAvailableDates([]);
    
    await loadStaffForService(service);
  };

  // Handle staff selection change
  const handleStaffChange = async (staff: StaffMember) => {
    setSelectedStaff(staff);
    setSelectedDate('');
    setSelectedTime('');
    setTimeSlots([]);
    
    await loadAvailableDates(staff.userData.uid);
  };

  // Handle date selection change
  const handleDateChange = async (date: string) => {
    setSelectedDate(date);
    setSelectedTime('');
    
    if (selectedStaff && selectedStoreService) {
      await loadTimeSlots(selectedStaff.userData.uid, selectedStoreService.sid, date, duration);
    }
  };

  // Save appointment changes
  const handleSaveChanges = async () => {
    if (!userData?.uid || !appointment) return;

    if (!selectedCustomer || !selectedStoreService || !selectedStaff || !selectedDate || !selectedTime) {
      alert(t('pleaseCompleteAllFields'));
      return;
    }

    try {
      setSaving(true);

      const updateData = {
        customerId: selectedCustomer.customerData.uid,
        staffId: selectedStaff.userData.uid,
        serviceId: selectedStoreService.sid,
        date: selectedDate,
        startTime: selectedTime,
        duration,
        notes,
        customerNotes
      };

      const result = await appointmentService.updateAppointment(
        appointmentId,
        updateData,
        userData.uid
      );

      if (result.success) {
        alert(t('updateSuccess'));
        router.push(`/store/${storeId}/appointments/${appointmentId}`);
      } else {
        alert(result.error || t('updateError'));
      }
    } catch (error) {
      console.error('Error updating appointment:', error);
      alert(t('updateError'));
    } finally {
      setSaving(false);
    }
  };

  // Helper functions
  const getCustomerDisplayName = (customer: Customer): string => {
    const firstName = customer.customerData.firstName || '';
    const lastName = customer.customerData.lastName || '';
    return `${firstName} ${lastName}`.trim() || customer.customerData.email || 'Unknown Customer';
  };

  const getStaffDisplayName = (staff: StaffMember): string => {
    return staff.userData.displayName || 
           `${staff.userData.firstName || ''} ${staff.userData.lastName || ''}`.trim() ||
           staff.email || 'Unknown Staff';
  };



  useEffect(() => {
    if (storeId && appointmentId) {
      loadAppointmentData();
    }
  }, [storeId, appointmentId]);

  // Auto-reload data when dependencies change
  useEffect(() => {
    if (customers.length > 0 && storeServices.length > 0 && availableStaff.length > 0 && appointment) {
      setCurrentSelections(appointment);
    }
  }, [customers, storeServices, availableStaff, appointment]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <FiAlertCircle className="w-16 h-16 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 text-lg">{t('appointmentNotFound')}</p>
            <Button
              onClick={() => router.push(`/store/${storeId}/appointments`)}
              className="mt-4 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              {t('backToAppointments')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Check if appointment can be edited
  const canEdit = appointment.status === AppointmentStatus.DRAFT || appointment.status === AppointmentStatus.CONFIRMED;

  if (!canEdit) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <FiAlertCircle className="w-16 h-16 text-orange-400 mx-auto mb-4" />
            <p className="text-slate-600 text-lg">{t('cannotEditStatus')}</p>
            <p className="text-slate-500 mt-2">{t('onlyEditDraftConfirmed')}</p>
            <Button
              onClick={() => router.push(`/store/${storeId}/appointments/${appointmentId}`)}
              className="mt-4 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              {t('backToDetails')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push(`/store/${storeId}/appointments/${appointmentId}`)}
                className="bg-slate-100 hover:bg-slate-200 text-slate-700"
              >
                <FiArrowLeft className="w-4 h-4 mr-2" />
                {t('back')}
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                  <FiCalendar className="w-8 h-8 mr-3 text-violet-600" />
                  {t('title')}
                </h1>
                <p className="text-slate-600 font-medium mt-1">
                  {appointment.serviceInfo?.serviceName || t('unknownService')}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Selection */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiUser className="w-5 h-5 mr-2 text-violet-600" />
              {t('selectCustomer')}
            </h2>
            
            <select
              value={selectedCustomer?.customerData.uid || ''}
              onChange={(e) => {
                const customer = customers.find(c => c.customerData.uid === e.target.value);
                setSelectedCustomer(customer || null);
              }}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
            >
              <option value="">{t('chooseCustomer')}</option>
              {customers.map((customer) => (
                <option key={customer.customerData.uid} value={customer.customerData.uid}>
                  {getCustomerDisplayName(customer)}
                </option>
              ))}
            </select>
          </div>

          {/* Service Selection */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiHeart className="w-5 h-5 mr-2 text-violet-600" />
              {t('selectService')}
            </h2>
            
            <select
              value={selectedStoreService?.sid || ''}
              onChange={(e) => {
                const service = storeServices.find(s => s.sid === e.target.value);
                if (service) handleServiceChange(service);
              }}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
            >
              <option value="">{t('chooseService')}</option>
              {storeServices.map((service) => (
                <option key={service.sid} value={service.sid}>
                  {service.serviceCategory} - {service.serviceBreed}
                </option>
              ))}
            </select>
          </div>

          {/* Staff Selection */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiUsers className="w-5 h-5 mr-2 text-violet-600" />
              {t('selectStaff')}
            </h2>
            
            <select
              value={selectedStaff?.userData.uid || ''}
              onChange={(e) => {
                const staff = availableStaff.find(s => s.userData.uid === e.target.value);
                if (staff) handleStaffChange(staff);
              }}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
              disabled={!selectedStoreService}
            >
              <option value="">{t('chooseStaff')}</option>
              {availableStaff.map((staff) => (
                <option key={staff.userData.uid} value={staff.userData.uid}>
                  {getStaffDisplayName(staff)}
                </option>
              ))}
            </select>
          </div>

          {/* Duration Selection */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiClock className="w-5 h-5 mr-2 text-violet-600" />
              {t('serviceDuration')}
            </h2>
            
            <select
              value={duration}
              onChange={(e) => {
                const newDuration = parseInt(e.target.value);
                setDuration(newDuration);
                // Reload time slots with new duration
                if (selectedStaff && selectedStoreService && selectedDate) {
                  loadTimeSlots(selectedStaff.userData.uid, selectedStoreService.sid, selectedDate, newDuration);
                }
              }}
              className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
            >
              <option value={30}>30 {t('minutes')}</option>
              <option value={45}>45 {t('minutes')}</option>
              <option value={60}>60 {t('minutes')}</option>
              <option value={90}>90 {t('minutes')}</option>
              <option value={120}>120 {t('minutes')}</option>
            </select>
          </div>
        </div>

        {/* Date Selection */}
        {selectedStaff && (
          <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiCalendar className="w-5 h-5 mr-2 text-violet-600" />
              {t('selectDate')}
            </h2>
            
            {availableDates.length > 0 ? (
              <CalendarDatePicker
                availableDates={availableDates}
                selectedDate={selectedDate}
                onDateSelect={handleDateChange}
                className="max-w-md mx-auto"
              />
            ) : (
              <div className="text-center py-8 text-slate-500">
                {t('noAvailableDates')}
              </div>
            )}
          </div>
        )}

        {/* Time Selection */}
        {selectedDate && (
          <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiClock className="w-5 h-5 mr-2 text-violet-600" />
              {t('selectTime')}
            </h2>
            
            {timeSlots.length > 0 ? (
              <div className="max-h-80 overflow-y-auto border border-slate-200 rounded-lg p-3 bg-white">
                <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2">
                  {timeSlots.map((slot) => {
                    const isAvailable = slot.available && slot.status === 'available';
                    const isBooked = slot.status === 'booked';
                    const isSelected = selectedTime === slot.startTime;
                    const isCurrentSlot = appointment.timeInfo?.startTime === slot.startTime;

                    return (
                      <div
                        key={slot.startTime}
                        onClick={() => {
                          if (isAvailable || isCurrentSlot) {
                            setSelectedTime(slot.startTime);
                          }
                        }}
                        className={`p-2.5 text-center border rounded-lg transition-all duration-200 text-sm cursor-pointer ${
                          isSelected
                            ? 'border-violet-500 bg-violet-500 text-white shadow-md transform scale-105'
                            : isCurrentSlot
                            ? 'border-blue-300 bg-blue-50 text-blue-800 hover:border-blue-400'
                            : isAvailable
                            ? 'border-green-300 bg-green-50 text-green-800 hover:border-green-400 hover:bg-green-100'
                            : isBooked
                            ? 'border-red-300 bg-red-50 text-red-600 cursor-not-allowed'
                            : 'border-slate-300 bg-slate-100 text-slate-500 cursor-not-allowed'
                        }`}
                        title={
                          isCurrentSlot 
                            ? t('currentTimeSlot')
                            : isAvailable 
                            ? t('clickToSelect') 
                            : isBooked 
                            ? t('timeSlotBooked') 
                            : t('timeSlotUnavailable')
                        }
                      >
                        <div className="font-medium">{slot.startTime}</div>
                        <div className="text-xs mt-1 opacity-75">
                          {isCurrentSlot 
                            ? t('current')
                            : isAvailable 
                            ? t('available') 
                            : isBooked 
                            ? t('booked') 
                            : t('blocked')
                          }
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-slate-500">
                {t('noAvailableTimeSlots')}
              </div>
            )}
          </div>
        )}

        {/* Notes */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
          <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
            <FiSettings className="w-5 h-5 mr-2 text-violet-600" />
            {t('additionalInfo')}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                {t('internalNotes')}
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder={t('internalNotesPlaceholder')}
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                {t('customerNotes')}
              </label>
              <textarea
                value={customerNotes}
                onChange={(e) => setCustomerNotes(e.target.value)}
                placeholder={t('customerNotesPlaceholder')}
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between mt-6">
          <Button
            onClick={() => router.push(`/store/${storeId}/appointments/${appointmentId}`)}
            className="bg-slate-100 hover:bg-slate-200 text-slate-700"
          >
            <FiArrowLeft className="w-4 h-4 mr-2" />
            {t('cancel')}
          </Button>
          
          <Button
            onClick={handleSaveChanges}
            disabled={saving || !selectedCustomer || !selectedStoreService || !selectedStaff || !selectedDate || !selectedTime}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
          >
            {saving ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : (
              <FiCheck className="w-4 h-4 mr-2" />
            )}
            {saving ? t('saving') : t('saveChanges')}
          </Button>
        </div>
      </div>
    </div>
  );
} 