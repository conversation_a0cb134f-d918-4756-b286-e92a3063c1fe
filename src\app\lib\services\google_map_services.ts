import { 
  Place, 
  PlaceResponse, 
  PlacesResponse, 
  PlacePrediction,
  AddressComponent,
  PlaceResult,
  GeoPlace,
  GeoPlaceResponse,
  AddressValidationResult,
  BusinessStatus,
  Address
} from '../models/place';
import { v4 as uuidv4 } from 'uuid';
import { FirestoreService } from "../firebase/services";

// Additional interfaces for enhanced autocomplete functionality
export interface PlaceAutocompleteResult {
  placeId: string;
  description: string;
  mainText: string;
  secondaryText: string;
  types: string[];
}

export interface PlaceSearchResponse {
  success: boolean;
  data?: PlaceAutocompleteResult[];
  error?: string;
}

export interface AddressFromPlaceResponse {
  success: boolean;
  data?: {
    address: Address;
    placeId: string;
    formattedAddress: string;
    location?: { lat: number; lng: number };
    businessName?: string;
    website?: string;
    phoneNumber?: string;
    businessStatus?: string;
    rating?: number;
    userRatingsTotal?: number;
    types?: string[];
    openingHours?: {
      weekday_text?: string[];
      open_now?: boolean;
    };
    photos?: Array<{
      photo_reference: string;
      height: number;
      width: number;
      html_attributions: string[];
    }>;
  };
  error?: string;
}

/**
 * Google Maps Service Class
 * Handles all Google Maps API related functionality including address search, validation, and place creation
 */
export class GoogleMapsService {
  private static API_KEY: string;
  private static GOOGLE_MAPS_API_BASE = 'https://maps.googleapis.com/maps/api';
  private static PLACE_COLLECTION = 'place';

  constructor() {
    GoogleMapsService.API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';
    if (!GoogleMapsService.API_KEY) {
      console.error('Google Maps API key is not set in environment variables');
    }
  }

  /**
   * Search for pet-related business places using autocomplete with location context
   * This is called when user types in the address search field
   * @param query Search query string
   * @param location Location context for better results
   * @param radius Search radius in meters
   * @param sessionToken Session token for billing optimization
   */
  async searchPetBusinessPlaces(
    query: string, 
    location?: { lat: number; lng: number },
    radius: number = 5000,
    sessionToken?: string
  ): Promise<PlaceSearchResponse> {
    try {
      if (!query.trim()) {
        return {
          success: true,
          data: []
        };
      }

      // Add pet-related keywords to improve search results
      const petKeywords = [
        'pet', 'dog', 'cat', 'veterinary', 'vet', 'animal', 'grooming', 'boarding',
        'kennel', 'clinic', 'hospital', 'care', 'food', 'supplies', 'store', 'shop'
      ];

      // Check if query contains pet-related keywords
      const hasPetKeywords = petKeywords.some(keyword => 
        query.toLowerCase().includes(keyword.toLowerCase())
      );

      // If no pet keywords, add common pet business terms
      const enhancedQuery = hasPetKeywords ? query : `${query} pet`;

      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_autocomplete',
          params: {
            input: enhancedQuery,
            types: 'establishment',
            location: location,
            radius: radius,
            strictbounds: true,
            ...(sessionToken && { sessiontoken: sessionToken }),
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to search places'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK') {
        return {
          success: false,
          error: `Google Places API error: ${data.status}`
        };
      }

      const results: PlaceAutocompleteResult[] = data.predictions.map((prediction: PlacePrediction) => ({
        placeId: prediction.place_id,
        description: prediction.description,
        mainText: prediction.structured_formatting.main_text,
        secondaryText: prediction.structured_formatting.secondary_text || '',
        types: [] // Google Autocomplete API doesn't return types directly
      }));

      console.log('PlaceAutocompleteResult', results);

      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('Error searching pet business places:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search places'
      };
    }
  }

  /**
   * Search for business places using autocomplete
   * This is called when user types in the address search field
   * @param query Search query string
   * @param sessionToken Session token for billing optimization
   */
  async searchBusinessPlaces(query: string, sessionToken?: string): Promise<PlaceSearchResponse> {
    try {
      if (!query.trim()) {
        return {
          success: true,
          data: []
        };
      }

      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_autocomplete',
          params: {
            input: query,
            types: 'establishment', // Focus on business establishments
            ...(sessionToken && { sessiontoken: sessionToken }),
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to search places'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK') {
        return {
          success: false,
          error: `Google Places API error: ${data.status}`
        };
      }

      const results: PlaceAutocompleteResult[] = data.predictions.map((prediction: PlacePrediction) => ({
        placeId: prediction.place_id,
        description: prediction.description,
        mainText: prediction.structured_formatting.main_text,
        secondaryText: prediction.structured_formatting.secondary_text || '',
        types: [] // Google Autocomplete API doesn't return types directly
      }));
      console.log('searchBusinessPlaces', results);
      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('Error searching business places:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search places'
      };
    }
  }

  /**
   * Get address information by postal code
   * This is called when user enters a postal code in smart search mode
   * @param postalCode Postal code to search for
   */
  async getAddressByPostalCode(postalCode: string): Promise<{
    success: boolean;
    data?: {
      address: Address;
      location: { lat: number; lng: number };
      formattedAddress: string;
    };
    error?: string;
  }> {
    try {
      if (!postalCode.trim()) {
        return {
          success: false,
          error: 'Postal code is required'
        };
      }

      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'geocode',
          params: {
            address: postalCode,
            components: 'postal_code:' + postalCode
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to get address by postal code'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        return {
          success: false,
          error: `No address found for postal code: ${postalCode}`
        };
      }

      const placeResult = data.results[0] as PlaceResult;
      
      // Extract address components
      const address = this.extractAddressFromComponents(placeResult.address_components);
      
      return {
        success: true,
        data: {
          address,
          location: {
            lat: placeResult.geometry.location.lat,
            lng: placeResult.geometry.location.lng
          },
          formattedAddress: placeResult.formatted_address
        }
      };
    } catch (error) {
      console.error('Error getting address by postal code:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get address by postal code'
      };
    }
  }

  /**
   * Search for pet-related business places with location context
   * This is called when user enters business name after postal code is set
   * @param businessName Business name/keyword to search for
   * @param location Location context (lat, lng) from postal code
   * @param radius Search radius in meters (default: 5000)
   */
  async searchPetBusinessPlacesWithLocation(
    businessName: string,
    location: { lat: number; lng: number },
    radius: number = 5000
  ): Promise<PlaceSearchResponse> {
    try {
      if (!businessName.trim()) {
        return {
          success: true,
          data: []
        };
      }

      // Add pet-related keywords to improve search results
      const petKeywords = [
        'pet', 'dog', 'cat', 'veterinary', 'vet', 'animal', 'grooming', 'boarding',
        'kennel', 'clinic', 'hospital', 'care', 'food', 'supplies', 'store', 'shop'
      ];

      // Check if query contains pet-related keywords
      const hasPetKeywords = petKeywords.some(keyword => 
        businessName.toLowerCase().includes(keyword.toLowerCase())
      );

      // If no pet keywords, add common pet business terms
      const enhancedKeyword = hasPetKeywords ? businessName : `${businessName} pet`;

      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_nearby',
          params: {
            location: location,
            radius: radius,
            keyword: enhancedKeyword,
            type: 'establishment'
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to search business places'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK') {
        return {
          success: false,
          error: `Google Places API error: ${data.status}`
        };
      }

      // Convert results to our format and filter for pet-related businesses
      const results: PlaceAutocompleteResult[] = data.results
        .map((result: PlaceResult) => ({
          placeId: result.place_id,
          description: result.vicinity || result.formatted_address,
          mainText: result.name,
          secondaryText: result.vicinity || result.formatted_address,
          types: result.types || []
        }))
        .filter((place: PlaceAutocompleteResult) => {
          // Filter for pet-related businesses based on name and types
          const name = place.mainText.toLowerCase();
          const description = place.description.toLowerCase();
          const types = place.types.map((t: string) => t.toLowerCase());
          
          const petRelatedTypes = [
            'veterinary_care', 'pet_store', 'animal_shelter', 'pet_groomer',
            'pet_boarding', 'pet_supplies', 'pet_clinic', 'pet_hospital'
          ];
          
          const petKeywords = [
            'pet', 'dog', 'cat', 'veterinary', 'vet', 'animal', 'grooming', 'boarding',
            'kennel', 'clinic', 'hospital', 'care', 'food', 'supplies', 'store', 'shop'
          ];
          
          // Check if place has pet-related types
          const hasPetType = petRelatedTypes.some(type => types.includes(type));
          
          // Check if place name/description contains pet keywords
          const hasPetKeywords = petKeywords.some(keyword => 
            name.includes(keyword) || description.includes(keyword)
          );
          
          return hasPetType || hasPetKeywords;
        });

      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('Error searching pet business places with location:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search business places'
      };
    }
  }

  /**
   * Search for business places with location context
   * This is called when user enters business name after postal code is set
   * @param businessName Business name/keyword to search for
   * @param location Location context (lat, lng) from postal code
   * @param radius Search radius in meters (default: 5000)
   */
  async searchBusinessPlacesWithLocation(
    businessName: string,
    location: { lat: number; lng: number },
    radius: number = 5000
  ): Promise<PlaceSearchResponse> {
    try {
      if (!businessName.trim()) {
        return {
          success: true,
          data: []
        };
      }

      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_nearby',
          params: {
            location: location,
            radius: radius,
            keyword: businessName,
            type: 'establishment'
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to search business places'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK') {
        return {
          success: false,
          error: `Google Places API error: ${data.status}`
        };
      }

      // Convert results to our format
      const results: PlaceAutocompleteResult[] = data.results.map((result: PlaceResult) => ({
        placeId: result.place_id,
        description: result.vicinity || result.formatted_address,
        mainText: result.name,
        secondaryText: result.vicinity || result.formatted_address,
        types: result.types || []
      }));

      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('Error searching business places with location:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search business places'
      };
    }
  }

  /**
   * Get comprehensive place details including business information
   * This is called when user selects a place from search results
   * @param placeId Google Place ID
   */
  async getComprehensivePlaceDetails(placeId: string): Promise<AddressFromPlaceResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_details',
          params: {
            place_id: placeId,
            fields: 'address_component,formatted_address,geometry,name,place_id,business_status,website,formatted_phone_number,opening_hours,rating,user_ratings_total,types,photos,international_phone_number,url,vicinity'
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to get place details'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK' || !data.result) {
        return {
          success: false,
          error: `Failed to get place details: ${data.status}`
        };
      }

      const placeResult = data.result as PlaceResult;
      
      // Extract address components
      const address = this.extractAddressFromComponents(placeResult.address_components);
      
      return {
        success: true,
        data: {
          address,
          placeId: placeResult.place_id,
          formattedAddress: placeResult.formatted_address,
          location: {
            lat: placeResult.geometry.location.lat,
            lng: placeResult.geometry.location.lng
          },
          businessName: placeResult.name,
          // Additional business information
          website: placeResult.website,
          phoneNumber: placeResult.formatted_phone_number || placeResult.international_phone_number,
          businessStatus: placeResult.business_status,
          rating: placeResult.rating,
          userRatingsTotal: placeResult.user_ratings_total,
          types: placeResult.types,
          // Opening hours
          openingHours: placeResult.opening_hours ? {
            weekday_text: placeResult.opening_hours.weekday_text,
            open_now: placeResult.opening_hours.open_now
          } : undefined,
          // Photos
          photos: placeResult.photos
        }
      };
    } catch (error) {
      console.error('Error getting comprehensive place details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get place details'
      };
    }
  }

  /**
   * Get detailed address information from a Google Place ID
   * This is called when user selects a place from autocomplete results
   * @param placeId Google Place ID
   */
  async getAddressFromPlace(placeId: string): Promise<AddressFromPlaceResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_details',
          params: {
            place_id: placeId,
            fields: 'address_component,formatted_address,geometry,name,place_id,business_status'
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to get place details'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK' || !data.result) {
        return {
          success: false,
          error: `Failed to get place details: ${data.status}`
        };
      }

      const placeResult = data.result as PlaceResult;
      
      // Extract address components
      const address = this.extractAddressFromComponents(placeResult.address_components);
      
      return {
        success: true,
        data: {
          address,
          placeId: placeResult.place_id,
          formattedAddress: placeResult.formatted_address,
          location: {
            lat: placeResult.geometry.location.lat,
            lng: placeResult.geometry.location.lng
          },
          businessName: placeResult.name
        }
      };
    } catch (error) {
      console.error('Error getting address from place:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get place details'
      };
    }
  }

  /**
   * Create a GeoPlace from Google Place data
   * This is called when user selects a Google Place for their store
   * @param placeData Data from getAddressFromPlace
   * @param storeId Store ID to associate with this place
   */
  async createGeoPlaceFromGooglePlace(
    placeData: NonNullable<AddressFromPlaceResponse['data']>,
    storeId: string
  ): Promise<GeoPlaceResponse> {
    try {
      const ONPlaceId = uuidv4();
      
      const geoPlace: GeoPlace = {
        sid: ONPlaceId,
        name: placeData.businessName || placeData.formattedAddress.split(',')[0],
        ONPlaceId: ONPlaceId,
        GMapPlaceId: ONPlaceId, // Use ONPlaceId as GMapPlaceId for new places
        formattedAddress: placeData.formattedAddress,
        displayName: {
          text: placeData.businessName || placeData.address.addressLine1,
          languageCode: 'en'
        },
        postalAddress: {
          regionCode: placeData.address.country,
          postalCode: placeData.address.postCode,
          administrativeArea: placeData.address.province,
          locality: placeData.address.city,
          addressLines: [placeData.address.addressLine1, placeData.address.addressLine2].filter(Boolean) as string[]
        },
        location: placeData.location,
        businessStatus: BusinessStatus.OPERATIONAL,
        storeId: storeId,
        isValid: true,
        isSynced: true,
        created_by: storeId,
        updated_by: storeId
      };

      return {
        success: true,
        data: geoPlace,
        message: 'GeoPlace created from Google Place successfully'
      };
    } catch (error) {
      console.error('Error creating GeoPlace from Google Place:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create GeoPlace from Google Place'
      };
    }
  }

  /**
   * Validate address and check if it exists in Google Maps
   * This is called during the address entry form step
   * @param address Address object containing addressLine1, city, province, country, postCode
   */
  async validateAddress(address: Address): Promise<AddressValidationResult> {
    try {
      // Construct full address string from components
      const fullAddress = this.constructFullAddress(address);
      
      if (!fullAddress.trim()) {
        return {
          isValid: false,
          hasGooglePlace: false,
          message: 'Address cannot be empty',
          error: 'EMPTY_ADDRESS'
        };
      }

      // Call Google Maps Geocoding API to validate and get place information
      const geocodeResult = await this.geocodeAddress(fullAddress);
      
      if (geocodeResult.success && geocodeResult.data) {
        const place = geocodeResult.data;

        console.log('place', place);
        console.log('geocodeResult', geocodeResult);
        
        return {
          isValid: true,
          hasGooglePlace: !!place.placeId,
          googlePlaceId: place.placeId,
          formattedAddress: fullAddress,
          location: place.location,
          message: 'Address is valid and found in Google Maps'
        };
      } else {
        // If Google Maps doesn't recognize the address, 
        // we still consider it valid but mark as non-Google place
        return {
          isValid: true,
          hasGooglePlace: false,
          formattedAddress: fullAddress,
          message: 'Address is valid but not registered in Google Maps'
        };
      }
    } catch (error) {
      console.error('Error validating address:', error);
      return {
        isValid: false,
        hasGooglePlace: false,
        error: error instanceof Error ? error.message : 'Failed to validate address'
      };
    }
  }

  /**
   * Create a GeoPlace object from address and validation result
   * This is called when the whole store creation process is completed
   * @param address Original address input
   * @param validationResult Result from validateAddress
   * @param storeId Store ID to associate with this place
   */
  async createGeoPlace(
    address: Address, 
    validationResult: AddressValidationResult, 
    storeId: string
  ): Promise<GeoPlaceResponse> {
    try {
      const ONPlaceId = uuidv4();
      
      let geoPlace: GeoPlace;

      if (validationResult.hasGooglePlace && validationResult.googlePlaceId) {
        // Get detailed information from Google Places API
        const placeDetails = await this.getPlaceDetails(validationResult.googlePlaceId);
        
        if (placeDetails.success && placeDetails.data) {
                     // Create GeoPlace with Google Maps data
           geoPlace = {
             sid: ONPlaceId,
             name: placeDetails.data.name,
             ONPlaceId: ONPlaceId,
             GMapPlaceId: validationResult.googlePlaceId,
             formattedAddress: validationResult.formattedAddress,
             displayName: {
               text: placeDetails.data.name,
               languageCode: 'en'
             },
             location: validationResult.location,
             businessStatus: BusinessStatus.OPERATIONAL,
             storeId: storeId,
             isValid: true,
             isSynced: true,
             created_by: storeId,
             updated_by: storeId
           };
        } else {
          // Fallback to basic place creation
          geoPlace = this.createBasicGeoPlace(ONPlaceId, address, validationResult, storeId);
        }
      } else {
        // Create custom OneNata place for non-Google registered addresses
        geoPlace = this.createBasicGeoPlace(ONPlaceId, address, validationResult, storeId);
      }

      return {
        success: true,
        data: geoPlace,
        message: 'GeoPlace created successfully'
      };
    } catch (error) {
      console.error('Error creating GeoPlace:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create GeoPlace'
      };
    }
  }

  /**
   * Save GeoPlace to Firestore
   * This is called only after the entire store creation process is completed
   * @param geoPlace GeoPlace object to save
   */
  async saveGeoPlaceToFirestore(geoPlace: GeoPlace): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
                   const placeData = {
        ...geoPlace,
        id: undefined // Remove the id field to avoid type conflicts
      };
      
      const result = await FirestoreService.createWithId(
        GoogleMapsService.PLACE_COLLECTION,
        geoPlace.ONPlaceId,
        placeData as unknown as Record<string, unknown>
      );

      if (result.success) {
        return { success: true };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to save place to Firestore'
        };
      }
    } catch (error) {
      console.error('Error saving GeoPlace to Firestore:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save place to Firestore'
      };
    }
  }

  /**
   * Update existing GeoPlace with store ID
   * @param placeId Place ID to update
   * @param storeId Store ID to associate
   */
  async updateGeoPlaceWithStoreId(placeId: string, storeId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const result = await FirestoreService.update(
        GoogleMapsService.PLACE_COLLECTION,
        placeId,
        {
          storeId: storeId,
          updatedBy: storeId,
          updatedAt: new Date()
        }
      );

      return result;
    } catch (error) {
      console.error('Error updating GeoPlace with store ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update place'
      };
    }
  }

  /**
   * Get GeoPlace by store ID
   * @param storeId Store ID to search for
   */
  async getGeoPlaceByStoreId(storeId: string): Promise<GeoPlaceResponse> {
    try {
      const result = await FirestoreService.getMany(
        GoogleMapsService.PLACE_COLLECTION,
        {
          where: [{ field: 'storeId', operator: '==', value: storeId }],
          limit: 1
        }
      );

      if (result.success && result.data && result.data.length > 0) {
        return {
          success: true,
          data: result.data[0] as unknown as GeoPlace,
          message: 'GeoPlace found'
        };
      } else {
        return {
          success: false,
          error: 'GeoPlace not found for store ID'
        };
      }
    } catch (error) {
      console.error('Error getting GeoPlace by store ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get GeoPlace'
      };
    }
  }

  /**
   * Helper method to create basic GeoPlace for non-Google registered addresses
   * @param ONPlaceId Generated UUID for OneNata place
   * @param address Original address input
   * @param validationResult Validation result
   * @param storeId Store ID
   */
  private createBasicGeoPlace(
    ONPlaceId: string,
    address: Address,
    validationResult: AddressValidationResult,
    storeId: string
  ): GeoPlace {
    return {
      sid: ONPlaceId,
      ONPlaceId: ONPlaceId,
      GMapPlaceId: validationResult.googlePlaceId, // May be undefined for non-Google places
      name: `${address.addressLine1}, ${address.city}`,
      formattedAddress: validationResult.formattedAddress,
      displayName: {
        text: `${address.addressLine1}, ${address.city}`,
        languageCode: 'en'
      },
      postalAddress: {
        regionCode: address.country,
        postalCode: address.postCode,
        administrativeArea: address.province,
        locality: address.city,
                 addressLines: [address.addressLine1, address.addressLine2].filter(Boolean) as string[]
      },
             location: validationResult.location,
       businessStatus: BusinessStatus.OPERATIONAL,
       storeId: storeId,
       isValid: true,
       isSynced: true,
       created_by: storeId,
       updated_by: storeId
    };
  }

  /**
   * Helper method to construct full address string from components
   * @param address Address components
   */
  private constructFullAddress(address: Address): string {
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.province,
      address.country,
      address.postCode
    ].filter(part => part && part.trim() !== '');

    return parts.join(', ');
  }

  /**
   * 地点自动完成搜索
   * @param input 用户输入的搜索文本
   * @param sessionToken 会话令牌（用于计费优化）
   */
  async autocompletePlaces(input: string, sessionToken?: string): Promise<PlacesResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_autocomplete',
          params: {
            input: input,
            ...(sessionToken && { sessiontoken: sessionToken }),
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to fetch places autocomplete'
        };
      }

      const data = result.data;
      
      // 转换Google Places API的响应格式为我们的Place类型
      const places: Place[] = data.predictions.map((prediction: PlacePrediction) => ({
        placeId: prediction.place_id,
        name: prediction.structured_formatting.main_text,
        formattedAddress: prediction.description,
        location: { lat: 0, lng: 0 }, // 自动完成API不返回坐标，需要额外调用details API
        addressComponents: {}
      }));

      return {
        success: true,
        data: places,
        total: places.length,
        message: 'Places autocomplete fetched successfully'
      };
    } catch (error) {
      console.error('Error in places autocomplete:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch places autocomplete'
      };
    }
  }

  /**
   * 获取地点详细信息
   * @param placeId Google Place ID
   */
  async getPlaceDetails(placeId: string): Promise<PlaceResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_details',
          params: {
            place_id: placeId,
            fields: 'address_component,formatted_address,geometry,name,place_id'
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to fetch place details'
        };
      }

      const data = result.data;
      const placeResult = data.result as PlaceResult;

      // 转换为我们的Place类型
      const place: Place = {
        placeId: placeResult.place_id,
        name: placeResult.name,
        formattedAddress: {
          addressLine1: placeResult.address_components.find(component => component.types.includes('route'))?.long_name || '',
          city: placeResult.address_components.find(component => component.types.includes('locality'))?.long_name || '',
          province: placeResult.address_components.find(component => component.types.includes('administrative_area_level_1'))?.long_name || '',
          country: placeResult.address_components.find(component => component.types.includes('country'))?.long_name || '',
          postCode: placeResult.address_components.find(component => component.types.includes('postal_code'))?.long_name || ''
        },
        location: {
          lat: placeResult.geometry.location.lat,
          lng: placeResult.geometry.location.lng
        },
        addressComponents: this.extractAddressComponents(placeResult.address_components)
      };

      return {
        success: true,
        data: place,
        message: 'Place details fetched successfully'
      };
    } catch (error) {
      console.error('Error fetching place details:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch place details'
      };
    }
  }

  /**
   * 地理编码：将地址转换为坐标
   * @param address 地址字符串
   */
  async geocodeAddress(address: string): Promise<PlaceResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'geocode',
          params: {
            address: address
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to geocode address'
        };
      }

      const data = result.data;
      
      if (data.status !== 'OK' || !data.results || data.results.length === 0) {
        return {
          success: false,
          error: `Geocoding failed: ${data.status || 'No results found'}`
        };
      }

      const placeResult = data.results[0] as PlaceResult;

      const place: Place = {
        placeId: placeResult.place_id,
        name: placeResult.formatted_address.split(',')[0],
        formattedAddress: {
          addressLine1: placeResult.address_components.find(component => component.types.includes('route'))?.long_name || '',
          city: placeResult.address_components.find(component => component.types.includes('locality'))?.long_name || '',
          province: placeResult.address_components.find(component => component.types.includes('administrative_area_level_1'))?.long_name || '',
          country: placeResult.address_components.find(component => component.types.includes('country'))?.long_name || '',
          postCode: placeResult.address_components.find(component => component.types.includes('postal_code'))?.long_name || ''
        },
        location: {
          lat: placeResult.geometry.location.lat,
          lng: placeResult.geometry.location.lng
        },
        addressComponents: this.extractAddressComponents(placeResult.address_components)
      };

      return {
        success: true,
        data: place,
        message: 'Address geocoded successfully'
      };
    } catch (error) {
      console.error('Error geocoding address:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to geocode address'
      };
    }
  }

  /**
   * 反向地理编码：将坐标转换为地址
   * @param lat 纬度
   * @param lng 经度
   */
  async reverseGeocode(lat: number, lng: number): Promise<PlaceResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'geocode',
          params: {
            latlng: `${lat},${lng}`
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to reverse geocode coordinates'
        };
      }

      const data = result.data;
      const placeResult = data.results[0] as PlaceResult;

      const place: Place = {
        placeId: placeResult.place_id,
        name: placeResult.formatted_address.split(',')[0],
        formattedAddress: {
          addressLine1: placeResult.address_components.find(component => component.types.includes('route'))?.long_name || '',
          city: placeResult.address_components.find(component => component.types.includes('locality'))?.long_name || '',
          province: placeResult.address_components.find(component => component.types.includes('administrative_area_level_1'))?.long_name || '',
          country: placeResult.address_components.find(component => component.types.includes('country'))?.long_name || '',
          postCode: placeResult.address_components.find(component => component.types.includes('postal_code'))?.long_name || ''
        },
        location: {
          lat: placeResult.geometry.location.lat,
          lng: placeResult.geometry.location.lng
        },
        addressComponents: this.extractAddressComponents(placeResult.address_components)
      };

      return {
        success: true,
        data: place,
        message: 'Coordinates reverse geocoded successfully'
      };
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to reverse geocode coordinates'
      };
    }
  }

  /**
   * 搜索附近的地点
   * @param lat 纬度
   * @param lng 经度
   * @param radius 搜索半径（米）
   * @param type 地点类型（可选）
   */
  async searchNearbyPlaces(lat: number, lng: number, radius: number, type?: string): Promise<PlacesResponse> {
    try {
      // Use our API route to avoid CORS issues
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'place_nearby',
          params: {
            location: { lat, lng },
            radius: radius,
            ...(type && { type })
          }
        })
      });

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to search nearby places'
        };
      }

      const data = result.data;
      
      // 转换为我们的Place类型
      const places: Place[] = data.results.map((result: PlaceResult) => ({
        placeId: result.place_id,
        name: result.name,
        formattedAddress: result.vicinity || result.formatted_address,
        location: {
          lat: result.geometry.location.lat,
          lng: result.geometry.location.lng
        },
        addressComponents: result.address_components ? 
          this.extractAddressComponents(result.address_components) : {}
      }));

      return {
        success: true,
        data: places,
        total: places.length,
        message: 'Nearby places fetched successfully'
      };
    } catch (error) {
      console.error('Error searching nearby places:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search nearby places'
      };
    }
  }

  /**
   * 从Google Maps API的地址组件中提取结构化地址信息
   * @param addressComponents Google Maps API返回的地址组件数组
   */
  private extractAddressFromComponents(addressComponents: AddressComponent[]): Address {
    const address: Address = {
      addressLine1: '',
      addressLine2: '',
      city: '',
      province: '',
      country: '',
      postCode: ''
    };

    let streetNumber = '';
    let route = '';

    addressComponents.forEach(component => {
      const type = component.types[0];
      switch (type) {
        case 'street_number':
          streetNumber = component.long_name;
          break;
        case 'route':
          route = component.long_name;
          break;
        case 'subpremise':
          address.addressLine2 = component.long_name;
          break;
        case 'locality':
        case 'administrative_area_level_2':
          if (!address.city) {
            address.city = component.long_name;
          }
          break;
        case 'administrative_area_level_1':
          address.province = component.long_name;
          break;
        case 'country':
          address.country = component.long_name;
          break;
        case 'postal_code':
          address.postCode = component.long_name;
          break;
      }
    });

    // Combine street number and route for address line 1
    address.addressLine1 = [streetNumber, route].filter(Boolean).join(' ');

    return address;
  }

  /**
   * 从Google Maps API的地址组件中提取结构化地址信息
   * @param addressComponents Google Maps API返回的地址组件数组
   */
  private extractAddressComponents(addressComponents: AddressComponent[]): Place['addressComponents'] {
    const components: Place['addressComponents'] = {};
    
    addressComponents.forEach(component => {
      const type = component.types[0];
      switch (type) {
        case 'street_number':
          components.streetNumber = component.long_name;
          break;
        case 'route':
          components.street = component.long_name;
          break;
        case 'locality':
          components.city = component.long_name;
          break;
        case 'administrative_area_level_1':
          components.state = component.long_name;
          break;
        case 'country':
          components.country = component.long_name;
          break;
        case 'postal_code':
          components.postalCode = component.long_name;
          break;
      }
    });

    return components;
  }

  /**
   * Convert Google Maps opening hours to BusinessHours format
   * @param openingHours Google Maps opening hours
   * @returns BusinessHours object for store
   */
  convertOpeningHoursToBusinessHours(openingHours: {
    weekday_text?: string[];
    open_now?: boolean;
  }): Record<string, { open: string; close: string; closed: boolean }> {
    const businessHours: Record<string, { open: string; close: string; closed: boolean }> = {};
    
    if (!openingHours.weekday_text) {
      // Default business hours if no data available
      const defaultHours = {
        open: '09:00',
        close: '18:00',
        closed: false
      };
      
      ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
        businessHours[day] = defaultHours;
      });
      
      return businessHours;
    }

    // Map Google's weekday_text to our format
    const dayMapping: Record<string, string> = {
      'Monday': 'monday',
      'Tuesday': 'tuesday', 
      'Wednesday': 'wednesday',
      'Thursday': 'thursday',
      'Friday': 'friday',
      'Saturday': 'saturday',
      'Sunday': 'sunday'
    };

    openingHours.weekday_text.forEach(dayText => {
      // Parse format like "Monday: 9:00 AM – 6:00 PM" or "Monday: Closed"
      const [dayName, hoursText] = dayText.split(': ');
      
      const dayKey = dayMapping[dayName];
      if (!dayKey) return;

      if (hoursText.toLowerCase().includes('closed')) {
        businessHours[dayKey] = {
          open: '09:00',
          close: '18:00',
          closed: true
        };
      } else {
        // Parse time range like "9:00 AM – 6:00 PM"
        const timeMatch = hoursText.match(/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*–\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/);
        if (timeMatch) {
          const openTime = this.convertTo24Hour(timeMatch[1]);
          const closeTime = this.convertTo24Hour(timeMatch[2]);
          
          businessHours[dayKey] = {
            open: openTime,
            close: closeTime,
            closed: false
          };
        } else {
          // Fallback to default hours
          businessHours[dayKey] = {
            open: '09:00',
            close: '18:00',
            closed: false
          };
        }
      }
    });

    // Fill in missing days with default hours
    ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
      if (!businessHours[day]) {
        businessHours[day] = {
          open: '09:00',
          close: '18:00',
          closed: false
        };
      }
    });

    return businessHours;
  }

  /**
   * Convert 12-hour time format to 24-hour format
   * @param time12h Time in 12-hour format (e.g., "9:00 AM")
   * @returns Time in 24-hour format (e.g., "09:00")
   */
  private convertTo24Hour(time12h: string): string {
    const [time, period] = time12h.split(' ');
    let hours: number;
    const minutes: number = parseInt(time.split(':')[1]);
    
    if (period === 'PM' && parseInt(time.split(':')[0]) !== 12) {
      hours = parseInt(time.split(':')[0]) + 12;
    } else if (period === 'AM' && parseInt(time.split(':')[0]) === 12) {
      hours = 0;
    } else {
      hours = parseInt(time.split(':')[0]);
    }
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  /**
   * Get Google Places photo URL
   * @param photoReference Photo reference from Places API
   * @param maxWidth Maximum width of the photo
   * @param maxHeight Maximum height of the photo
   */
  async getPhotoUrl(
    photoReference: string,
    maxWidth: number = 800,
    maxHeight: number = 600
  ): Promise<{
    success: boolean;
    data?: { photoUrl: string };
    error?: string;
  }> {
    try {
      const response = await fetch('/api/google-maps', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'get_photo_url',
          params: {
            photo_reference: photoReference,
            maxwidth: maxWidth.toString(),
            maxheight: maxHeight.toString()
          }
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error getting photo URL:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get photo URL'
      };
    }
  }

  /**
   * Get multiple photo URLs from Google Places photos
   * @param photos Array of photo objects from Places API
   * @param maxPhotos Maximum number of photos to retrieve (default 5)
   */
  async getPlacePhotosUrls(
    photos: Array<{
      photo_reference: string;
      height: number;
      width: number;
    }>,
    maxPhotos: number = 5
  ): Promise<{
    success: boolean;
    data?: Array<{
      photoUrl: string;
      photo_reference: string;
      width: number;
      height: number;
    }>;
    error?: string;
  }> {
    try {
      const photosToProcess = photos.slice(0, maxPhotos);
      const photoUrls: Array<{
        photoUrl: string;
        photo_reference: string;
        width: number;
        height: number;
      }> = [];

      for (const photo of photosToProcess) {
        const result = await this.getPhotoUrl(photo.photo_reference);
        if (result.success && result.data) {
          photoUrls.push({
            photoUrl: result.data.photoUrl,
            photo_reference: photo.photo_reference,
            width: photo.width,
            height: photo.height
          });
        }
      }

      return {
        success: true,
        data: photoUrls
      };
    } catch (error) {
      console.error('Error getting place photos URLs:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get photos URLs'
      };
    }
  }
} 

// Export singleton instance
export default new GoogleMapsService(); 