{"emulators": {"dataconnect": {"dataDir": "dataconnect/.dataconnect/pgliteData", "port": 9399}, "apphosting": {"port": 5002, "rootDirectory": "./", "startCommand": "npm run dev"}, "auth": {"port": 9099}, "functions": {"port": 5001}, "firestore": {"port": 8080}, "database": {"port": 9000}, "pubsub": {"port": 8085}, "storage": {"port": 9199}, "eventarc": {"port": 9299}, "tasks": {"port": 9499}, "ui": {"enabled": true}, "singleProjectMode": true, "hosting": {"port": 5000}}, "dataconnect": {"source": "dataconnect"}, "firestore": {"database": "(default)", "location": "nam5", "rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "functions": [{"source": "functions", "codebase": "default", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, {"source": "portal_cloud", "codebase": "portal_cloud", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}], "apphosting": {"backendId": "danta-s-world", "rootDir": "/", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "functions"]}, "storage": {"rules": "storage.rules"}, "remoteconfig": {"template": "remoteconfig.template.json"}, "database": {"rules": "database.rules.json"}}