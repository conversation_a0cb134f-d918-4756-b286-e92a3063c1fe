/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";
import "./Component.css";

interface Props {
  text: string;
  variant: "one";
  className: any;
  textClassName: any;
}

export const Component = ({
  text = "Get the App",
  variant,
  className,
  textClassName,
}: Props): JSX.Element => {
  return (
    <div className={`component ${className}`}>
      <div className={`text ${textClassName}`}>{text}</div>
    </div>
  );
};
