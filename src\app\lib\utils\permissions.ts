import { StoreRole } from '../models/types';
import { FirestoreService } from '../firebase/services/firestore';

export interface UserStoreRole {
  role: StoreRole;
  isOwner: boolean;
  isAdmin: boolean;
  isStaff: boolean;
}

/**
 * 获取用户在当前店铺的角色
 */
export async function getUserStoreRole(userId: string, storeId: string): Promise<UserStoreRole | null> {
  try {
    // 首先获取用户的portal-user-data文档
    const userDataResult = await FirestoreService.getDocByQueryField(
      'portal-user-data',
      'uid',
      userId
    );

    if (!userDataResult.success || !userDataResult.data) {
      console.log('No portal user data found for user:', userId);
      return null;
    }

    const userDataId = userDataResult.data.sid;
    
    // 然后查询该用户的store-staff-info子集合
    const staffInfoResult = await FirestoreService.getMany(
      `portal-user-data/${userDataId}/store-staff-info`,
      {
        where: [
          { field: 'storeId', operator: '==', value: storeId },
          { field: 'active', operator: '==', value: true }
        ]
      }
    );

    if (!staffInfoResult.success || !staffInfoResult.data || staffInfoResult.data.length === 0) {
      console.log('No staff info found for user:', userId, 'store:', storeId);
      return null;
    }

    const staffInfo = staffInfoResult.data[0];
    const roleValue = staffInfo.role;
    
    console.log('Found staff info:', staffInfo);
    console.log('Role value from database:', roleValue);

    // 处理不同的角色值格式
    let role: StoreRole;
    if (roleValue === 'store-owner' || roleValue === 'STORE_OWNER') {
      role = StoreRole.STORE_OWNER;
    } else if (roleValue === 'store-admin' || roleValue === 'STORE_ADMIN') {
      role = StoreRole.STORE_ADMIN;
    } else if (roleValue === 'store-staff' || roleValue === 'STORE_STAFF') {
      role = StoreRole.STORE_STAFF;
    } else {
      console.warn('Unknown role value:', roleValue, 'defaulting to STORE_STAFF');
      role = StoreRole.STORE_STAFF;
    }

    const userRole = {
      role,
      isOwner: role === StoreRole.STORE_OWNER,
      isAdmin: role === StoreRole.STORE_ADMIN,
      isStaff: role === StoreRole.STORE_STAFF
    };

    console.log('Processed user role:', userRole);
    return userRole;
  } catch (error) {
    console.error('Error getting user store role:', error);
    return null;
  }
}

/**
 * 检查用户是否有权限执行特定操作
 */
export function hasPermission(userRole: UserStoreRole | null, requiredRole: StoreRole): boolean {
  if (!userRole) return false;

  const roleHierarchy = {
    [StoreRole.STORE_OWNER]: 3,
    [StoreRole.STORE_ADMIN]: 2,
    [StoreRole.STORE_STAFF]: 1
  };

  return roleHierarchy[userRole.role] >= roleHierarchy[requiredRole];
}

/**
 * 获取用户可见的导航项
 */
export function getVisibleNavigationItems(userRole: UserStoreRole | null) {
  if (!userRole) return [];

  const allItems = [
    { key: 'overview', label: 'nav.overview', icon: 'FiHome', href: '/store/[id]' },
    { key: 'staff', label: 'nav.staff', icon: 'FiUsers', href: '/store/[id]/staff' },
    { key: 'services', label: 'nav.services', icon: 'FiCalendar', href: '/store/[id]/services' },
    { key: 'customers', label: 'nav.customers', icon: 'FiBarChart', href: '/store/[id]/customers' },
    { key: 'appointments', label: 'nav.appointments', icon: 'FiCalendar', href: '/store/[id]/appointments' }
  ];

  if (userRole.isOwner || userRole.isAdmin) {
    // Owner和Admin可以看到所有导航项
    return allItems;
  } else if (userRole.isStaff) {
    // Staff只能看到部分导航项
    return allItems.filter(item => 
      ['overview', 'services', 'customers', 'appointments'].includes(item.key)
    );
  }

  return [];
}

/**
 * 检查用户是否可以管理员工
 */
export function canManageStaff(userRole: UserStoreRole | null): boolean {
  return userRole?.isOwner || userRole?.isAdmin || false;
}

/**
 * 检查用户是否可以查看所有数据
 */
export function canViewAllData(userRole: UserStoreRole | null): boolean {
  return userRole?.isOwner || userRole?.isAdmin || false;
}

/**
 * 检查用户是否可以编辑店铺信息
 */
export function canEditStore(userRole: UserStoreRole | null): boolean {
  return userRole?.isOwner || userRole?.isAdmin || false;
} 