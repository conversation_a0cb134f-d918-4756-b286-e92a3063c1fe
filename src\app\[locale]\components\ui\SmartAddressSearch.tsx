'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useTranslations } from 'next-intl';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiAlertCircle } from 'react-icons/fi';
import GoogleMapsService, { PlaceAutocompleteResult } from '../../../lib/services/google_map_services';
import { Address, AddressValidationResult, GeoPlace } from '../../../lib/models/place';
import PlaceService from '../../../lib/services/place_service';

interface SmartAddressSearchProps {
  onAddressSelected: (data: {
    address: Address;
    validationResult: AddressValidationResult;
    source: 'google_place' | 'manual';
    placeData?: GeoPlace;
  }) => void;
  onError: (error: string) => void;
  currentAddress?: Address;
  className?: string;
  storeId?: string;
}

const SmartAddressSearch: React.FC<SmartAddressSearchProps> = ({
  onAddressSelected,
  onError,
  currentAddress,
  className = '',
  storeId
}) => {
  const t = useTranslations('smartAddressSearch');
  const tAddress = useTranslations('Address');
  const [step, setStep] = useState<'postal_code' | 'business_search' | 'results' | 'manual'>('postal_code');
  const [postalCode, setPostalCode] = useState(currentAddress?.postCode || '');
  const [businessName, setBusinessName] = useState('');
  const [searchResults, setSearchResults] = useState<PlaceAutocompleteResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [locationContext, setLocationContext] = useState<{ lat: number; lng: number } | null>(null);
  const [addressContext, setAddressContext] = useState<Address | null>(null);
  const [selectedPlace, setSelectedPlace] = useState<PlaceAutocompleteResult | null>(null);
  const [manualAddress, setManualAddress] = useState<Address>({
    addressLine1: '',
    addressLine2: '',
    city: '',
    province: '',
    country: '',
    postCode: postalCode
  });

  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Step 1: Search by postal code
  const handlePostalCodeSearch = async () => {
    if (!postalCode.trim()) {
      onError(t('postalCodeRequired'));
      return;
    }

    setIsLoading(true);
    try {
      const result = await GoogleMapsService.getAddressByPostalCode(postalCode);
      
      if (result.success && result.data) {
        setLocationContext(result.data.location);
        setAddressContext(result.data.address);
        setManualAddress(prev => ({
          ...prev,
          ...result.data!.address,
          postCode: postalCode
        }));
        setStep('business_search');
      } else {
        onError(result.error || t('postalCodeNotFound'));
      }
    } catch {
      onError(t('postalCodeSearchError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Search for business with location context
  const handleBusinessSearch = async () => {
    if (!businessName.trim() || !locationContext) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await GoogleMapsService.searchPetBusinessPlacesWithLocation(
        businessName,
        locationContext,
        5000 // 5km radius
      );
      
      if (result.success) {
        setSearchResults(result.data || []);
        setStep('results');
      } else {
        onError(result.error || t('businessSearchError'));
      }
    } catch {
      onError(t('businessSearchError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle business name input change with debounced autocomplete
  const handleBusinessNameChange = (value: string) => {
    setBusinessName(value);
    
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // If user is typing, show autocomplete suggestions
    if (value.trim() && locationContext) {
      searchTimeoutRef.current = setTimeout(async () => {
        setIsLoading(true);
        try {
          const result = await GoogleMapsService.searchPetBusinessPlaces(
            value,
            locationContext,
            5000 // 5km radius
          );
          
          if (result.success) {
            setSearchResults(result.data || []);
            // Don't change step, keep user in business_search mode
          } else {
            // Don't show error for autocomplete, just clear results
            setSearchResults([]);
          }
        } catch {
          // Don't show error for autocomplete, just clear results
          setSearchResults([]);
        } finally {
          setIsLoading(false);
        }
      }, 300); // Shorter delay for autocomplete
    } else {
      setSearchResults([]);
    }
  };

  // Step 3: Select a place from results
  const handlePlaceSelection = async (place: PlaceAutocompleteResult) => {
    setIsLoading(true);
    try {
      const result = await GoogleMapsService.getComprehensivePlaceDetails(place.placeId);
      
      if (result.success && result.data) {
        const validationResult: AddressValidationResult = {
          isValid: true,
          hasGooglePlace: true,
          googlePlaceId: result.data.placeId,
          formattedAddress: result.data.formattedAddress,
          location: result.data.location,
          message: t('foundInGoogleMaps')
        };

        // Prepare place data for Firestore
        let placeData: GeoPlace | undefined;
        if (storeId) {
          try {
            const placeResult = await PlaceService.createOrUpdatePlaceFromGoogle({
              placeId: result.data.placeId,
              name: result.data.businessName || place.mainText,
              formattedAddress: result.data.formattedAddress,
              location: result.data.location!,
              address: result.data.address,
              businessName: result.data.businessName,
              website: result.data.website,
              phoneNumber: result.data.phoneNumber,
              businessStatus: result.data.businessStatus,
              rating: result.data.rating,
              userRatingsTotal: result.data.userRatingsTotal,
              types: result.data.types,
              openingHours: result.data.openingHours,
              photos: result.data.photos
            }, storeId);

            if (placeResult.success && placeResult.data) {
              placeData = placeResult.data;
            }
          } catch (placeError) {
            console.error('Error creating/updating place:', placeError);
            // Don't fail the address selection if place creation fails
          }
        }

        onAddressSelected({
          address: result.data.address,
          validationResult,
          source: 'google_place',
          placeData
        });

        setSelectedPlace(place);
      } else {
        onError(result.error || t('placeDetailsError'));
      }
    } catch {
      onError(t('placeDetailsError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Step 4: Manual address entry
  const handleManualAddressSubmit = async () => {
    if (!manualAddress.addressLine1 || !manualAddress.city || !manualAddress.province || !manualAddress.country) {
      onError(t('manualAddressRequired'));
      return;
    }

    setIsLoading(true);
    try {
      const result = await GoogleMapsService.validateAddress(manualAddress);
      
      onAddressSelected({
        address: manualAddress,
        validationResult: result,
        source: 'manual'
      });
    } catch {
      onError(t('manualAddressError'));
    } finally {
      setIsLoading(false);
    }
  };

  // Handle manual address field changes
  const handleManualAddressChange = (field: keyof Address, value: string) => {
    setManualAddress(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Reset to postal code step
  const handleReset = () => {
    setStep('postal_code');
    setBusinessName('');
    setSearchResults([]);
    setSelectedPlace(null);
    setLocationContext(null);
    setAddressContext(null);
  };

  // Switch to manual entry
  const handleSwitchToManual = () => {
    setStep('manual');
  };

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Step 1: Postal Code Search */}
      {step === 'postal_code' && (
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('postalCodeLabel')}
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={postalCode}
                onChange={(e) => setPostalCode(e.target.value)}
                placeholder={t('postalCodePlaceholder')}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button
                onClick={handlePostalCodeSearch}
                disabled={isLoading || !postalCode.trim()}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isLoading ? (
                  <FiLoader className="w-4 h-4 animate-spin" />
                ) : (
                  <FiSearch className="w-4 h-4" />
                )}
              </button>
            </div>
            <p className="mt-1 text-sm text-gray-500">{t('postalCodeHint')}</p>
          </div>
        </div>
      )}

      {/* Step 2: Business Search */}
      {step === 'business_search' && addressContext && (
        <div className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 mb-2">{t('locationFound')}</h3>
            <p className="text-sm text-blue-700">
              {addressContext.city}, {addressContext.province}, {addressContext.country}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('businessNameLabel')}
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={businessName}
                onChange={(e) => handleBusinessNameChange(e.target.value)}
                placeholder={t('businessNamePlaceholder')}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
              <button
                onClick={handleBusinessSearch}
                disabled={isLoading || !businessName.trim()}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {isLoading ? (
                  <FiLoader className="w-4 h-4 animate-spin" />
                ) : (
                  <FiSearch className="w-4 h-4" />
                )}
              </button>
              <button
                onClick={handleSwitchToManual}
                className="px-4 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200"
              >
                {t('manualEntry')}
              </button>
            </div>
            <p className="mt-1 text-sm text-gray-500">{t('businessNameHint')}</p>
          </div>

          {/* Autocomplete suggestions */}
          {searchResults.length > 0 && (
            <div className="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-lg p-2">
              <div className="text-xs text-gray-500 mb-2">{t('suggestions')}</div>
              {searchResults.slice(0, 5).map((place) => (
                <div
                  key={place.placeId}
                  onClick={() => handlePlaceSelection(place)}
                  className="p-2 border border-gray-100 rounded hover:border-purple-300 hover:bg-purple-50 cursor-pointer transition-colors"
                >
                  <div className="font-medium text-sm text-gray-900">{place.mainText}</div>
                  <div className="text-xs text-gray-600">{place.secondaryText}</div>
                </div>
              ))}
            </div>
          )}

          {isLoading && (
            <div className="flex items-center justify-center py-4">
              <FiLoader className="w-5 h-5 animate-spin text-purple-600" />
              <span className="ml-2 text-sm text-gray-600">{t('searchingBusinesses')}</span>
            </div>
          )}
        </div>
      )}

      {/* Step 3: Search Results */}
      {step === 'results' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">{t('searchResults')}</h3>
            <button
              onClick={handleReset}
              className="text-sm text-purple-600 hover:text-purple-700"
            >
              {t('newSearch')}
            </button>
          </div>

          {searchResults.length > 0 ? (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {searchResults.map((place) => (
                <div
                  key={place.placeId}
                  onClick={() => handlePlaceSelection(place)}
                  className="p-3 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 cursor-pointer transition-colors"
                >
                  <div className="font-medium text-gray-900">{place.mainText}</div>
                  <div className="text-sm text-gray-600">{place.secondaryText}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FiAlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">{t('noResultsFound')}</p>
              <button
                onClick={handleSwitchToManual}
                className="mt-2 text-sm text-purple-600 hover:text-purple-700"
              >
                {t('enterManually')}
              </button>
            </div>
          )}
        </div>
      )}

      {/* Step 4: Manual Address Entry */}
      {step === 'manual' && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">{t('manualAddressEntry')}</h3>
            <button
              onClick={handleReset}
              className="text-sm text-purple-600 hover:text-purple-700"
            >
              {t('newSearch')}
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('addressLine1')} *
              </label>
              <input
                type="text"
                value={manualAddress.addressLine1}
                onChange={(e) => handleManualAddressChange('addressLine1', e.target.value)}
                placeholder={tAddress('addressLine1Placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('addressLine2')}
              </label>
              <input
                type="text"
                value={manualAddress.addressLine2}
                onChange={(e) => handleManualAddressChange('addressLine2', e.target.value)}
                placeholder={tAddress('addressLine2Placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('city')} *
              </label>
              <input
                type="text"
                value={manualAddress.city}
                onChange={(e) => handleManualAddressChange('city', e.target.value)}
                placeholder={tAddress('cityPlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('province')} *
              </label>
              <input
                type="text"
                value={manualAddress.province}
                onChange={(e) => handleManualAddressChange('province', e.target.value)}
                placeholder={tAddress('provincePlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('country')} *
              </label>
              <input
                type="text"
                value={manualAddress.country}
                onChange={(e) => handleManualAddressChange('country', e.target.value)}
                placeholder={tAddress('countryPlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('postCode')} *
              </label>
              <input
                type="text"
                value={manualAddress.postCode}
                onChange={(e) => handleManualAddressChange('postCode', e.target.value)}
                placeholder={tAddress('postCodePlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>

          <button
            onClick={handleManualAddressSubmit}
            disabled={isLoading || !manualAddress.addressLine1 || !manualAddress.city || !manualAddress.province || !manualAddress.country}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isLoading ? (
              <>
                <FiLoader className="w-4 h-4 animate-spin mr-2" />
                {t('validating')}
              </>
            ) : (
              <>
                <FiCheck className="w-4 h-4 mr-2" />
                {t('useThisAddress')}
              </>
            )}
          </button>
        </div>
      )}

      {/* Selected Place Confirmation */}
      {selectedPlace && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <FiCheck className="w-5 h-5 text-green-600 mr-2" />
            <div>
              <h3 className="text-sm font-medium text-green-800">{t('addressSelected')}</h3>
              <p className="text-sm text-green-700">{selectedPlace.mainText}</p>
              <p className="text-sm text-green-600">{selectedPlace.secondaryText}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SmartAddressSearch; 