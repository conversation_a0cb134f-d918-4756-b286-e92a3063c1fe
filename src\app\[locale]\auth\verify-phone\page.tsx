'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Input } from '../../components/ui/Input';
import { Button } from '../../components/ui/Button';
import { sendPhoneVerificationCode, verifyPhoneCode } from '../../../lib/firebase/services/auth';
import { useAuth, AuthenticatedRoute } from '../../../lib/firebase/context/AuthContext';

export default function VerifyPhonePage() {
  return (
    <AuthenticatedRoute>
      <VerifyPhoneContent />
    </AuthenticatedRoute>
  );
}

function VerifyPhoneContent() {
  const t = useTranslations('auth-verify-phone');
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { userData } = useAuth();
  const locale = params.locale as string;
  const phoneNumber = searchParams.get('phone') || userData?.phoneNumber || '';

  const [verificationCode, setVerificationCode] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [isInitialSending, setIsInitialSending] = useState(false);
  const [hasInitialSent, setHasInitialSent] = useState(false);

  // 页面加载时自动发送验证码
  useEffect(() => {
    const sendInitialCode = async () => {
      if (!phoneNumber || hasInitialSent) return;

      setIsInitialSending(true);
      setErrors({});

      try {
        const result = await sendPhoneVerificationCode(phoneNumber);

        if (result.success) {
          setHasInitialSent(true);
          setResendCooldown(60); // 60秒冷却时间
        } else {
          setErrors({ general: result.message || t('errorGeneral') });
        }
      } catch (error) {
        console.error('Initial send error:', error);
        setErrors({ general: t('errorGeneral') });
      } finally {
        setIsInitialSending(false);
      }
    };

    sendInitialCode();
  }, [phoneNumber, hasInitialSent, t]);

  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!verificationCode.trim()) {
      setErrors({ code: t('errorRequiredCode') });
      return;
    }

    setIsLoading(true);
    setErrors({});

    try {
      const result = await verifyPhoneCode(phoneNumber, verificationCode);

      if (result.success) {
        // 验证成功，跳转到仪表板
        router.push(`/${locale}/dashboard`);
      } else {
        setErrors({ code: result.message || t('errorInvalidCode') });
      }
    } catch (error) {
      console.error('Verification error:', error);
      setErrors({ general: t('errorGeneral') });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendCooldown > 0 || !phoneNumber) return;

    setIsResending(true);
    setErrors({});

    try {
      const result = await sendPhoneVerificationCode(phoneNumber);

      if (result.success) {
        setResendCooldown(60); // 60秒冷却时间
      } else {
        setErrors({ general: result.message || t('errorGeneral') });
      }
    } catch (error) {
      console.error('Resend error:', error);
      setErrors({ general: t('errorGeneral') });
    } finally {
      setIsResending(false);
    }
  };

  // 如果正在发送初始验证码，显示加载状态
  if (isInitialSending) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#FDECCE] to-[#F2D3A4]">
        <div className="w-full max-w-md mx-4">
          <div className="backdrop-blur-md bg-white/80 rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
            <div className="p-8 text-center">
              <Link href={`/${locale}`} className="inline-block">
                <div className="w-16 h-16 bg-gradient-to-br from-[#A126FF] to-[#8a20d8] rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 mx-auto shadow-lg">
                  ON
                </div>
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
              <div className="flex items-center justify-center gap-3 mt-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#A126FF]"></div>
                <p className="text-gray-600">正在发送验证码到 {phoneNumber}...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#FDECCE] to-[#F2D3A4]">
      <div className="w-full max-w-md mx-4">
        {/* 主验证卡片 */}
        <div className="backdrop-blur-md bg-white/80 rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
          <div className="p-8">
            {/* Logo和标题 */}
            <div className="text-center mb-8">
              <Link href={`/${locale}`} className="inline-block">
                <div className="w-16 h-16 bg-gradient-to-br from-[#A126FF] to-[#8a20d8] rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 mx-auto shadow-lg">
                  ON
                </div>
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
              <p className="text-gray-600 text-sm leading-relaxed">
                {t('subtitle')}
              </p>
              {phoneNumber && (
                <p className="text-[#A126FF] font-medium mt-2">
                  {phoneNumber}
                </p>
              )}
            </div>
            
            {/* 发送成功提示 */}
            {hasInitialSent && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 text-green-700 rounded-xl text-sm text-center">
                验证码已发送到您的手机
              </div>
            )}
            
            {/* 说明文字 */}
            <div className="text-center mb-6">
              <p className="text-gray-500 text-sm">
                {t('instructions')}
              </p>
            </div>
            
            {/* 错误消息 */}
            {errors.general && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl text-sm">
                {errors.general}
              </div>
            )}
            
            {/* 验证表单 */}
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div className="relative">
                <Input
                  id="verificationCode"
                  type="text"
                  label={t('code')}
                  placeholder={t('codePlaceholder')}
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  error={errors.code}
                  maxLength={6}
                  className="text-center text-xl tracking-[0.5em] font-mono"
                  autoComplete="one-time-code"
                />
              </div>
              
              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-[#A126FF] to-[#8a20d8] hover:from-[#8a20d8] hover:to-[#6d1bb3] text-white font-semibold py-4 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg"
                loading={isLoading}
                disabled={isLoading || verificationCode.length !== 6}
              >
                {isLoading ? t('loading') : t('verifyButton')}
              </Button>
            </form>
            
            {/* 重新发送验证码 */}
            <div className="text-center mt-6">
              <button
                type="button"
                onClick={handleResendCode}
                disabled={isResending || resendCooldown > 0 || !phoneNumber}
                className="text-sm text-[#A126FF] hover:text-[#8a20d8] disabled:text-gray-400 disabled:cursor-not-allowed transition-colors duration-200 font-medium"
              >
                {isResending ? 
                  t('resending') : 
                  resendCooldown > 0 ? 
                    `${t('resendButton')} (${resendCooldown}s)` : 
                    t('resendButton')
                }
              </button>
            </div>
            
            {/* 返回登录链接 */}
            <div className="text-center mt-8 pt-6 border-t border-gray-200">
              <Link 
                href={`/${locale}/auth/login`} 
                className="text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {t('backToLogin')}
              </Link>
            </div>
          </div>
        </div>
        
        {/* 隐藏的reCAPTCHA容器，用于生产环境 */}
        <div id="recaptcha-container" className="hidden"></div>
        
        {/* 页脚 */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            © {new Date().getFullYear()} OneNata Pet Store. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
} 