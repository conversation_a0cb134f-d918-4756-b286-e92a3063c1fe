/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useAuth } from '../../../lib/firebase/context/AuthContext';
import StoreService, { StoreListItem } from '../../../lib/services/store_services';
import { StoreVerifiedStatus } from '../../../lib/models/types';
import { StoreHeader } from '../../components/ui/StoreHeader';
import appointmentService from '../../../lib/services/appointment_service';
import TodayAppointmentCalendar from '../../components/ui/TodayAppointmentCalendar';
import { 
  FiArrowLeft, 
  FiEdit3, 
  FiMapPin, 
  FiPhone, 
  FiMail, 
  FiGlobe, 
  FiUsers, 
  FiCalendar,
  FiCheck,
  FiX,
  FiAlertCircle,
  FiMoreHorizontal,
  FiImage,
  FiDollarSign,
  FiTrendingUp,
  FiPackage,
  FiClock,
  FiShoppingBag,
  FiStar
} from 'react-icons/fi';

// 安全图片组件，处理加载失败的情况
const SafeImage = ({ src, alt, className, fallback }: {
  src?: string;
  alt: string;
  className?: string;
  fallback?: React.ReactNode;
}) => {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  // 如果没有src或者加载失败，显示fallback
  if (!src || hasError) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100`}>
        {fallback || <FiImage className="w-8 h-8 text-gray-400" />}
      </div>
    );
  }

  return (
    <div className="relative">
      {isLoading && (
        <div className={`${className} absolute inset-0 flex items-center justify-center bg-gray-100 animate-pulse`}>
          <div className="w-6 h-6 border-2 border-gray-300 border-t-purple-500 rounded-full animate-spin"></div>
        </div>
      )}
      <img
        src={src}
        alt={alt}
        className={className}
        onError={handleError}
        onLoad={handleLoad}
        style={{ display: isLoading ? 'none' : 'block' }}
      />
    </div>
  );
};

const StoreDetailPage = () => {
  const t = useTranslations('storeDetails');
  const tCommon = useTranslations('common');
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();

  const [store, setStore] = useState<StoreListItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'overview' | 'info' | 'photos' | 'activity'>('overview');
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Mock business data - in real app, this would come from API
  const businessData = {
    todayRevenue: 1250.50,
    monthlyRevenue: 28430.75,
    todayAppointments: 8,
    pendingOrders: 12,
    completedOrders: 156,
    revenueGrowth: 15.2,
    customerSatisfaction: 4.8,
    averageOrderValue: 85.40,
    salesTrend: [
      { day: 'Mon', revenue: 850, orders: 12 },
      { day: 'Tue', revenue: 1200, orders: 18 },
      { day: 'Wed', revenue: 950, orders: 14 },
      { day: 'Thu', revenue: 1400, orders: 22 },
      { day: 'Fri', revenue: 1650, orders: 28 },
      { day: 'Sat', revenue: 2100, orders: 35 },
      { day: 'Sun', revenue: 1250, orders: 20 }
    ],
    appointments: {
      scheduled: 24,
      completed: 18,
      cancelled: 2
    },
    orderStatus: {
      pending: 12,
      processing: 8,
      shipped: 15,
      delivered: 156
    }
  };

  const storeId = params.id as string;

  useEffect(() => {
    if (!user || !storeId) return;
    
    loadStoreDetails();
    loadDashboardData();
  }, [user, storeId]);

  const loadStoreDetails = async () => {
    try {
      setLoading(true);
      const response = await StoreService.getStoreDetails(storeId);
      
      if (response.success && response.data) {
        setStore(response.data);
      } else {
        setError(response.error || 'Failed to load store details');
      }
    } catch (err) {
      console.error('Error loading store details:', err);
      setError('Failed to load store details');
    } finally {
      setLoading(false);
    }
  };

  const loadDashboardData = async () => {
    try {
      setDashboardLoading(true);
      const response = await appointmentService.getStoreDashboardStats(storeId);
      
      if (response.success && response.data) {
        setDashboardData(response.data);
      } else {
        console.error('Failed to load dashboard data:', response.error);
      }
    } catch (err) {
      console.error('Error loading dashboard data:', err);
    } finally {
      setDashboardLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={tCommon('loading')} storeId={storeId} currentPage="overview" />
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500"></div>
        </div>
      </div>
    );
  }

  if (error || !store) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={tCommon('error')} storeId={storeId} currentPage="overview" />
        <div className="flex flex-col items-center justify-center py-20">
          <div className="text-center">
            <FiAlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">{tCommon('loadingError')}</h3>
            <p className="text-gray-500 mb-4">{error}</p>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
            >
              <FiArrowLeft className="mr-2 h-4 w-4" />
              {tCommon('back')}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={(typeof store.storeName === 'string' ? store.storeName : '') || (typeof store.name === 'string' ? store.name : '')} storeId={storeId} currentPage="overview" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 店铺信息卡片 */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden mb-8">
          {/* 店铺头部信息 */}
          <div className="relative h-48 bg-gradient-to-br from-purple-500 to-pink-500">
            {/* 背景图片 - 使用第一张店铺照片 */}
            {store.storePhotos && store.storePhotos.length > 0 && (
              <div className="absolute inset-0 overflow-hidden">
                <SafeImage
                  src={store.storePhotos[0]}
                  alt="Store background"
                  className="w-full h-full object-cover object-center transform scale-105"
                  fallback={null}
                />
                <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/70"></div>
              </div>
            )}
            <div className="absolute inset-0 bg-black bg-opacity-20"></div>
            <div className="absolute bottom-6 left-6 right-6">
              <div className="flex items-end space-x-4">
                <div className="w-20 h-20 rounded-xl bg-white shadow-lg flex items-center justify-center overflow-hidden">
                  <SafeImage
                    src={(store.logoUrl as string) || (store.avatarUrl as string)}
                    alt={(store.storeName as string) || (store.name as string) || 'Store'}
                    className="w-full h-full object-cover"
                    fallback={<FiMapPin className="w-8 h-8 text-purple-500" />}
                  />
                </div>
                <div className="flex-1 text-white">
                  <h1 className="text-2xl font-bold">{(store.storeName as string) || (store.name as string)}</h1>
                  <p className="text-purple-100 text-sm">{store.businessType ? String(store.businessType) : 'Pet Store'}</p>
                </div>
                <button
                  onClick={() => router.push(`/store/${storeId}/edit`)}
                  className="flex items-center space-x-2 px-4 py-2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg transition-colors backdrop-blur-sm border border-white border-opacity-30"
                >
                  <FiEdit3 className="w-4 h-4" />
                  <span className="text-sm font-medium">{tCommon('actions.edit')}</span>
                </button>
              </div>
            </div>
          </div>

          {/* 标签页导航 */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { key: 'overview', label: t('tabs.overview') },
                { key: 'info', label: t('tabs.info') },
                { key: 'photos', label: t('tabs.photos') },
                { key: 'activity', label: t('tabs.activity') },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.key
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* 标签页内容 */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-8">
                {/* Revenue and Key Metrics */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('dashboard.title')}</h3>
                  {dashboardLoading ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="h-32 bg-slate-200 rounded-lg animate-pulse"></div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {/* Today's Revenue */}
                      <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6 border border-green-200">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-green-600">{t('stats.todayRevenue')}</p>
                            <p className="text-2xl font-bold text-green-900">
                              ${dashboardData?.todayRevenue?.toFixed(2) || '0.00'}
                            </p>
                            <p className="text-xs text-green-700 mt-1">
                              {t('fromCompletedAppointments')}
                            </p>
                          </div>
                          <FiDollarSign className="h-8 w-8 text-green-500" />
                        </div>
                      </div>

                      {/* Today's Appointments */}
                      <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6 border border-blue-200">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-blue-600">{t('stats.todayAppointments')}</p>
                            <p className="text-2xl font-bold text-blue-900">
                              {dashboardData?.todayAppointments || 0}
                            </p>
                            <p className="text-xs text-blue-700 mt-1">
                              {t('appointmentsScheduled')}
                            </p>
                          </div>
                          <FiCalendar className="h-8 w-8 text-blue-500" />
                        </div>
                      </div>

                      {/* Monthly Revenue */}
                      <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm font-medium text-purple-600">{t('stats.monthlyRevenue')}</p>
                            <p className="text-2xl font-bold text-purple-900">
                              ${dashboardData?.monthlyRevenue?.toFixed(2) || '0.00'}
                            </p>
                            <p className="text-xs text-purple-700 mt-1">
                              {t('thisMonth')}
                            </p>
                          </div>
                          <FiTrendingUp className="h-8 w-8 text-purple-500" />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Appointment Trends Chart */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-4">{t('dashboard.appointmentTrends.title')}</h4>
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    {dashboardLoading ? (
                      <div className="h-48 bg-slate-200 rounded animate-pulse"></div>
                    ) : (
                      <>
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <p className="text-sm text-gray-600">{t('weeklyAppointments')}</p>
                            <p className="text-lg font-semibold text-gray-900">
                              {dashboardData?.appointmentTrends?.reduce((sum: number, day: any) => sum + day.appointments, 0) || 0}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">{t('completedAppointments')}</p>
                            <p className="text-lg font-semibold text-gray-900">
                              {dashboardData?.appointmentTrends?.reduce((sum: number, day: any) => sum + day.completed, 0) || 0}
                            </p>
                          </div>
                        </div>
                        
                        {/* Simple bar chart */}
                        <div className="flex items-end space-x-2 h-32">
                          {dashboardData?.appointmentTrends?.map((day: any, index: number) => (
                            <div key={index} className="flex-1 flex flex-col items-center">
                              <div 
                                className="w-full bg-gradient-to-t from-purple-400 to-purple-600 rounded-t-sm"
                                style={{ 
                                  height: `${Math.max((day.appointments / Math.max(...(dashboardData?.appointmentTrends?.map((d: any) => d.appointments) || [1]))) * 100, 8)}%`
                                }}
                                title={`${day.day}: ${day.appointments} appointments`}
                              ></div>
                              <span className="text-xs text-gray-500 mt-2">{day.day}</span>
                            </div>
                          )) || (
                            <div className="flex-1 text-center text-gray-500">{t('noData')}</div>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                </div>

                {/* Additional Metrics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Appointment Status */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6">
                    <h4 className="text-md font-semibold text-gray-900 mb-4">{t('dashboard.appointmentStatus.title')}</h4>
                    {dashboardLoading ? (
                      <div className="space-y-3">
                        {[...Array(5)].map((_, i) => (
                          <div key={i} className="h-6 bg-slate-200 rounded animate-pulse"></div>
                        ))}
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FiClock className="w-4 h-4 text-gray-500 mr-2" />
                            <span className="text-sm text-gray-600">{t('appointmentStatus.draft')}</span>
                          </div>
                          <span className="text-sm font-medium">{dashboardData?.appointmentStats?.draft || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FiCheck className="w-4 h-4 text-blue-500 mr-2" />
                            <span className="text-sm text-gray-600">{t('appointmentStatus.confirmed')}</span>
                          </div>
                          <span className="text-sm font-medium">{dashboardData?.appointmentStats?.confirmed || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FiClock className="w-4 h-4 text-yellow-500 mr-2" />
                            <span className="text-sm text-gray-600">{t('appointmentStatus.inProgress')}</span>
                          </div>
                          <span className="text-sm font-medium">{dashboardData?.appointmentStats?.inProgress || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FiCheck className="w-4 h-4 text-green-500 mr-2" />
                            <span className="text-sm text-gray-600">{t('appointmentStatus.completed')}</span>
                          </div>
                          <span className="text-sm font-medium">{dashboardData?.appointmentStats?.completed || 0}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <FiX className="w-4 h-4 text-red-500 mr-2" />
                            <span className="text-sm text-gray-600">{t('appointmentStatus.cancelled')}</span>
                          </div>
                          <span className="text-sm font-medium">{dashboardData?.appointmentStats?.cancelled || 0}</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Today's Appointment Calendar */}
                  <div>
                    {dashboardLoading ? (
                      <div className="h-96 bg-slate-200 rounded-lg animate-pulse"></div>
                    ) : (
                      <TodayAppointmentCalendar 
                        appointments={dashboardData?.todayAppointmentsList || []}
                        storeId={storeId}
                      />
                    )}
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white rounded-lg border border-gray-200 p-6">
                  <h4 className="text-md font-semibold text-gray-900 mb-4">{t('quickActions.title')}</h4>
                  
                  <div className="space-y-3">
                    <button
                      onClick={() => router.push(`/store/${storeId}/staff`)}
                      className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
                    >
                      <div className="flex items-center space-x-3">
                        <FiUsers className="w-4 h-4 text-purple-500" />
                        <span className="text-sm font-medium text-gray-900">{t('actions.manageStaff')}</span>
                      </div>
                      <FiMoreHorizontal className="w-4 h-4 text-gray-400 group-hover:text-purple-500" />
                    </button>

                    <button
                      onClick={() => router.push(`/store/${storeId}/services`)}
                      className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
                    >
                      <div className="flex items-center space-x-3">
                        <FiCalendar className="w-4 h-4 text-purple-500" />
                        <span className="text-sm font-medium text-gray-900">{t('actions.manageServices')}</span>
                      </div>
                      <FiMoreHorizontal className="w-4 h-4 text-gray-400 group-hover:text-purple-500" />
                    </button>

                    <button
                      onClick={() => router.push(`/store/${storeId}/edit`)}
                      className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
                    >
                      <div className="flex items-center space-x-3">
                        <FiEdit3 className="w-4 h-4 text-purple-500" />
                        <span className="text-sm font-medium text-gray-900">{t('actions.editStore')}</span>
                      </div>
                      <FiMoreHorizontal className="w-4 h-4 text-gray-400 group-hover:text-purple-500" />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'info' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 基本信息 */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900">{t('basicInfo.title')}</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3">
                      <FiMapPin className="w-5 h-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{t('address')}</p>
                        <p className="text-sm text-gray-600">
                          {(typeof store.address === 'string' ? store.address : '') || 
                           (typeof store.currentAddress?.addressLine1 === 'string' ? store.currentAddress.addressLine1 : '') || 
                           t('notProvided')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <FiPhone className="w-5 h-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{t('phone')}</p>
                        <p className="text-sm text-gray-600">
                          {store.phone || t('notProvided')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <FiMail className="w-5 h-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{t('email')}</p>
                        <p className="text-sm text-gray-600">
                          {store.email || t('notProvided')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-3">
                      <FiGlobe className="w-5 h-5 text-gray-400 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{t('website')}</p>
                        <p className="text-sm text-gray-600">
                          {store.website ? (
                            <a
                              href={store.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-purple-600 hover:text-purple-700 hover:underline"
                            >
                              {store.website}
                            </a>
                          ) : (
                            t('notProvided')
                          )}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 营业信息 */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900">{t('businessInfo')}</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-900 mb-2">{t('businessType.label')}</p>
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                        {store.businessType ? t('businessType.' + store.businessType) : 'Pet Store'}
                      </span>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-900 mb-2">{t('description')}</p>
                      <p className="text-sm text-gray-600">
                        {store.description || t('notProvided')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'photos' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">{t('photos.title')}</h3>
                  <button
                    onClick={() => router.push(`/store/${storeId}/edit`)}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                  >
                    <FiEdit3 className="w-4 h-4 mr-2" />
                    {t('photos.addPhotos')}
                  </button>
                </div>

                {store.storePhotos && store.storePhotos.length > 0 ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {store.storePhotos.map((photoUrl, index) => (
                      <div key={index} className="group relative aspect-square rounded-lg overflow-hidden bg-gray-100">
                        <SafeImage
                          src={photoUrl}
                          alt={`Store photo ${index + 1}`}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          fallback={
                            <div className="w-full h-full flex items-center justify-center">
                              <FiImage className="w-8 h-8 text-gray-400" />
                            </div>
                          }
                        />
                        
                        {/* 照片覆盖层 */}
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <button
                              onClick={() => {
                                // 可以在这里添加查看大图的功能
                                window.open(photoUrl, '_blank');
                              }}
                              className="p-2 bg-white bg-opacity-80 rounded-full hover:bg-opacity-100 transition-colors"
                            >
                              <FiImage className="w-4 h-4 text-gray-700" />
                            </button>
                          </div>
                        </div>

                        {/* 照片序号 */}
                        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                          {index + 1}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FiImage className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{t('photos.noPhotos')}</h3>
                    <p className="text-gray-500 mb-6">{t('photos.noPhotosDesc')}</p>
                    <button
                      onClick={() => router.push(`/store/${storeId}/edit`)}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                    >
                      <FiImage className="w-4 h-4 mr-2" />
                      {t('photos.addFirstPhoto')}
                    </button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'activity' && (
              <div className="text-center py-12">
                <FiCalendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">{t('noActivity')}</h3>
                <p className="text-gray-500">{t('noActivityDesc')}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreDetailPage;
