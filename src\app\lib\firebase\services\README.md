# Firestore 服务使用指南

这是一个完整的 Firestore 数据库操作服务，提供了常用的增删改查功能。

## 快速开始

```typescript
import FirestoreService from './firestore';

// 定义您的数据类型
interface User {
  id?: string;
  name: string;
  email: string;
  age: number;
  tags: string[];
  createdAt?: Date | null;
  updatedAt?: Date | null;
}
```

## 基本操作

### 1. 创建文档

```typescript
// 自动生成ID
const result = await FirestoreService.create<User>('users', {
  name: '<PERSON>',
  email: '<EMAIL>',
  age: 30,
  tags: ['developer', 'javascript']
});

if (result.success) {
  console.log('文档ID:', result.data); // 返回生成的文档ID
}

// 指定ID
const result2 = await FirestoreService.createWithId<User>('users', 'user123', {
  name: '<PERSON>',
  email: '<EMAIL>',
  age: 25,
  tags: ['designer', 'ui/ux']
});
```

### 2. 读取文档

```typescript
// 获取单个文档
const user = await FirestoreService.getById<User>('users', 'user123');
if (user.success) {
  console.log('用户信息:', user.data);
}

// 获取多个文档
const users = await FirestoreService.getMany<User>('users', {
  where: [
    { field: 'age', operator: '>=', value: 18 }
  ],
  orderBy: [
    { field: 'createdAt', direction: 'desc' }
  ],
  limit: 10
});

if (users.success) {
  console.log('用户列表:', users.data);
  console.log('是否有更多数据:', users.hasMore);
}
```

### 3. 更新文档

```typescript
const result = await FirestoreService.update<User>('users', 'user123', {
  age: 26,
  tags: ['designer', 'ui/ux', 'frontend']
});

if (result.success) {
  console.log('更新成功');
}
```

### 4. 删除文档

```typescript
const result = await FirestoreService.delete('users', 'user123');
if (result.success) {
  console.log('删除成功');
}
```

## 高级功能

### 1. 复杂查询

```typescript
// 多条件查询
const result = await FirestoreService.getMany<User>('users', {
  where: [
    { field: 'age', operator: '>=', value: 18 },
    { field: 'age', operator: '<=', value: 65 },
    { field: 'tags', operator: 'array-contains', value: 'developer' }
  ],
  orderBy: [
    { field: 'age', direction: 'asc' },
    { field: 'name', direction: 'desc' }
  ],
  limit: 20
});
```

### 2. 分页查询

```typescript
// 第一页
const firstPage = await FirestoreService.getMany<User>('users', {
  orderBy: [{ field: 'createdAt', direction: 'desc' }],
  limit: 10
});

// 第二页
if (firstPage.success && firstPage.hasMore) {
  const secondPage = await FirestoreService.getMany<User>('users', {
    orderBy: [{ field: 'createdAt', direction: 'desc' }],
    limit: 10,
    startAfter: firstPage.lastDoc
  });
}
```

### 3. 批量操作

```typescript
const operations = [
  {
    type: 'create' as const,
    collectionName: 'users',
    data: { name: 'User 1', email: '<EMAIL>' }
  },
  {
    type: 'update' as const,
    collectionName: 'users',
    docId: 'user123',
    data: { age: 30 }
  },
  {
    type: 'delete' as const,
    collectionName: 'users',
    docId: 'user456'
  }
];

const result = await FirestoreService.batchWrite(operations);
```

### 4. 数组操作

```typescript
// 添加数组元素
await FirestoreService.arrayAdd('users', 'user123', 'tags', 'react');

// 删除数组元素
await FirestoreService.arrayRemoveValue('users', 'user123', 'tags', 'vue');
```

### 5. 数值操作

```typescript
// 增加数值
await FirestoreService.incrementField('users', 'user123', 'loginCount', 1);

// 减少数值
await FirestoreService.incrementField('users', 'user123', 'credits', -10);
```

## 实时监听

### 1. 监听单个文档

```typescript
const unsubscribe = FirestoreService.subscribeToDocument<User>(
  'users',
  'user123',
  (userData, error) => {
    if (error) {
      console.error('监听错误:', error);
    } else if (userData) {
      console.log('文档更新:', userData);
    } else {
      console.log('文档不存在');
    }
  }
);

// 取消监听
unsubscribe();
```

### 2. 监听集合

```typescript
const unsubscribe = FirestoreService.subscribeToCollection<User>(
  'users',
  {
    where: [{ field: 'age', operator: '>=', value: 18 }],
    orderBy: [{ field: 'createdAt', direction: 'desc' }],
    limit: 10
  },
  (users, error) => {
    if (error) {
      console.error('监听错误:', error);
    } else {
      console.log('集合更新:', users);
    }
  }
);

// 取消监听
unsubscribe();
```

## 工具函数

### 1. 检查文档是否存在

```typescript
const exists = await FirestoreService.exists('users', 'user123');
console.log('文档存在:', exists);
```

### 2. 获取文档数量

```typescript
const count = await FirestoreService.getCount('users', {
  where: [{ field: 'age', operator: '>=', value: 18 }]
});
console.log('成年用户数量:', count);
```

## 错误处理

所有方法都返回统一的响应格式：

```typescript
interface FirestoreResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
```

使用示例：

```typescript
const result = await FirestoreService.create('users', userData);

if (result.success) {
  console.log('操作成功:', result.message);
  console.log('数据:', result.data);
} else {
  console.error('操作失败:', result.error);
}
```

## 类型安全

服务使用 TypeScript 泛型确保类型安全：

```typescript
// 定义数据类型
interface Product {
  id?: string;
  name: string;
  price: number;
  category: string;
  inStock: boolean;
}

// 类型安全的操作
const product = await FirestoreService.getById<Product>('products', 'prod123');
if (product.success) {
  // product.data 的类型是 Product
  console.log(product.data.name); // TypeScript 会提供自动完成
}
```

## 最佳实践

1. **始终检查操作结果**：
   ```typescript
   const result = await FirestoreService.create('users', userData);
   if (!result.success) {
     // 处理错误
     console.error(result.error);
     return;
   }
   ```

2. **使用类型定义**：
   ```typescript
   interface User extends FirestoreDocument {
     name: string;
     email: string;
   }
   ```

3. **合理使用分页**：
   ```typescript
   // 避免一次性加载大量数据
   const users = await FirestoreService.getMany<User>('users', {
     limit: 20 // 限制返回数量
   });
   ```

4. **及时取消监听**：
   ```typescript
   useEffect(() => {
     const unsubscribe = FirestoreService.subscribeToDocument(/* ... */);
     return () => unsubscribe(); // 组件卸载时取消监听
   }, []);
   ```

5. **使用批量操作**：
   ```typescript
   // 多个相关操作应该使用批量操作
   await FirestoreService.batchWrite([
     { type: 'create', collectionName: 'orders', data: orderData },
     { type: 'update', collectionName: 'products', docId: 'prod123', data: { stock: newStock } }
   ]);
   ``` 