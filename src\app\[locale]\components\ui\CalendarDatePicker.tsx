'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

interface CalendarDay {
  date: string;
  day: number;
  isToday: boolean;
  isAvailable: boolean;
  isPast: boolean;
  isOtherMonth: boolean;
}

interface CalendarDatePickerProps {
  availableDates: string[];
  selectedDate?: string;
  onDateSelect: (date: string) => void;
  className?: string;
}

export default function CalendarDatePicker({
  availableDates,
  selectedDate,
  onDateSelect,
  className = ''
}: CalendarDatePickerProps) {
  const t = useTranslations('createAppointment.calendar');
  const [currentDate, setCurrentDate] = useState(new Date());

  const today = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  // Get first day of the month and how many days in the month
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);
  const daysInMonth = lastDayOfMonth.getDate();
  const startingDayOfWeek = firstDayOfMonth.getDay();

  // Get previous month's last few days
  const prevMonthLastDay = new Date(currentYear, currentMonth, 0).getDate();
  const prevMonthDays = [];
  for (let i = startingDayOfWeek - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i;
    const date = new Date(currentYear, currentMonth - 1, day);
    const dateString = date.toISOString().split('T')[0];
    
    prevMonthDays.push({
      date: dateString,
      day,
      isToday: false,
      isAvailable: availableDates.includes(dateString),
      isPast: date < today,
      isOtherMonth: true
    });
  }

  // Get current month days
  const currentMonthDays = [];
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(currentYear, currentMonth, day);
    const dateString = date.toISOString().split('T')[0];
    const isToday = dateString === today.toISOString().split('T')[0];
    
    currentMonthDays.push({
      date: dateString,
      day,
      isToday,
      isAvailable: availableDates.includes(dateString),
      isPast: date < today && !isToday,
      isOtherMonth: false
    });
  }

  // Get next month's first few days to complete the 6-week grid
  const nextMonthDays = [];
  const totalCells = 42; // 6 weeks × 7 days
  const remainingCells = totalCells - prevMonthDays.length - currentMonthDays.length;
  
  for (let day = 1; day <= remainingCells; day++) {
    const date = new Date(currentYear, currentMonth + 1, day);
    const dateString = date.toISOString().split('T')[0];
    
    nextMonthDays.push({
      date: dateString,
      day,
      isToday: false,
      isAvailable: availableDates.includes(dateString),
      isPast: date < today,
      isOtherMonth: true
    });
  }

  const allDays = [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];

  const navigateMonth = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
    } else {
      setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
    }
  };

  const monthNames = [
    t('months.january'), t('months.february'), t('months.march'),
    t('months.april'), t('months.may'), t('months.june'),
    t('months.july'), t('months.august'), t('months.september'),
    t('months.october'), t('months.november'), t('months.december')
  ];

  const weekdayNames = [
    t('weekdays.sunday'), t('weekdays.monday'), t('weekdays.tuesday'),
    t('weekdays.wednesday'), t('weekdays.thursday'), t('weekdays.friday'), t('weekdays.saturday')
  ];

  const handleDateClick = (day: CalendarDay) => {
    if (day.isAvailable && !day.isPast && !day.isOtherMonth) {
      onDateSelect(day.date);
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-slate-200 p-4 ${className}`}>
      {/* Calendar Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => navigateMonth('prev')}
          className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <FiChevronLeft className="w-4 h-4 text-slate-600" />
        </button>
        
        <div className="font-semibold text-slate-900">
          {monthNames[currentMonth]} {currentYear}
        </div>
        
        <button
          onClick={() => navigateMonth('next')}
          className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
        >
          <FiChevronRight className="w-4 h-4 text-slate-600" />
        </button>
      </div>

      {/* Weekday Headers */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {weekdayNames.map((day) => (
          <div key={day} className="text-center text-xs font-medium text-slate-500 py-2">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1">
        {allDays.map((day, index) => {
          const isSelected = selectedDate === day.date;
          const canSelect = day.isAvailable && !day.isPast && !day.isOtherMonth;
          const isUnavailableWorkDay = !day.isAvailable && !day.isPast && !day.isOtherMonth; // 不可用的工作日
          
          return (
            <button
              key={index}
              onClick={() => handleDateClick(day)}
              disabled={!canSelect}
              title={
                day.isPast 
                  ? t('pastDate')
                  : !day.isAvailable && !day.isOtherMonth
                  ? t('staffNotWorking')
                  : day.isAvailable
                  ? t('availableDate')
                  : ''
              }
              className={`
                h-10 w-10 text-sm rounded-lg transition-colors relative
                ${day.isOtherMonth 
                  ? 'text-slate-300' 
                  : isUnavailableWorkDay
                  ? 'text-slate-400 bg-slate-50 cursor-not-allowed'
                  : 'text-slate-900'
                }
                ${!canSelect && !isUnavailableWorkDay
                  ? 'cursor-not-allowed text-slate-300'
                  : canSelect
                  ? 'hover:bg-slate-100 cursor-pointer'
                  : ''
                }
                ${isSelected
                  ? 'bg-violet-500 text-white hover:bg-violet-600'
                  : ''
                }
                ${day.isToday && !isSelected
                  ? canSelect
                    ? 'bg-blue-100 text-blue-600 font-semibold'
                    : 'bg-slate-200 text-slate-500 font-semibold'
                  : ''
                }
                ${isUnavailableWorkDay
                  ? 'relative overflow-hidden'
                  : ''
                }
              `}
            >
              {day.day}
              
              {/* Unavailable work day indicator - diagonal line */}
              {isUnavailableWorkDay && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-0.5 bg-slate-400 transform rotate-45"></div>
                </div>
              )}
              
              {/* Available indicator */}
              {day.isAvailable && !day.isPast && !isSelected && !day.isOtherMonth && (
                <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-green-500 rounded-full"></div>
              )}
              
              {/* Today indicator */}
              {day.isToday && !isSelected && (
                <div className="absolute top-1 right-1 w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
              )}
            </button>
          );
        })}
      </div>

      {/* Legend */}
      <div className="mt-4 flex items-center justify-center space-x-3 text-xs text-slate-500">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>{t('legend.available')}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          <span>{t('legend.today')}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-violet-500 rounded-full"></div>
          <span>{t('legend.selected')}</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="relative w-3 h-3 bg-slate-100 rounded">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-2 h-0.5 bg-slate-400 transform rotate-45"></div>
            </div>
          </div>
          <span>{t('legend.notWorking')}</span>
        </div>
      </div>
    </div>
  );
} 