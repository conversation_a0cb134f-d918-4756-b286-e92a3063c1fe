/**
 * 基础模型接口
 * 对应 Dart 中的基础模型结构
 */
export interface BaseModel {
  id?: number;
  sid?: string;
  name?: string;
  isValid?: boolean;
  isSynced?: boolean;
  created_by?: string;
  create_date?: Date;
  updated_by?: string;
  update_date?: Date;
}

/**
 * JSON 工具类，提供类型安全的转换函数
 * 对应 Dart 中的 JsonUtil
 */
export class JsonUtil {
  /**
   * 从 JSON 值转换为布尔值
   * 处理各种可能的输入类型
   */
  static boolFromJson(value: unknown): boolean | undefined {
    if (value === null || value === undefined) {
      return undefined;
    }
    
    if (typeof value === 'boolean') {
      return value;
    }
    
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true' || lowerValue === '1') {
        return true;
      }
      if (lowerValue === 'false' || lowerValue === '0') {
        return false;
      }
    }
    
    if (typeof value === 'number') {
      return value !== 0;
    }
    
    return undefined;
  }

  /**
   * 将布尔值转换为 JSON 值
   */
  static boolToJson(value: boolean | undefined): boolean | undefined {
    return value;
  }

  /**
   * 从 JSON 值转换为数字
   */
  static numberFromJson(value: unknown): number | undefined {
    if (value === null || value === undefined) {
      return undefined;
    }
    
    if (typeof value === 'number') {
      return value;
    }
    
    if (typeof value === 'string') {
      const parsed = parseInt(value, 10);
      return isNaN(parsed) ? undefined : parsed;
    }
    
    return undefined;
  }

  /**
   * 将数字转换为 JSON 值
   */
  static numberToJson(value: number | undefined): number | undefined {
    return value;
  }

  /**
   * 从 JSON 值转换为字符串
   */
  static stringFromJson(value: unknown): string | undefined {
    if (value === null || value === undefined) {
      return undefined;
    }
    
    if (typeof value === 'string') {
      return value;
    }
    
    return String(value);
  }

  /**
   * 将字符串转换为 JSON 值
   */
  static stringToJson(value: string | undefined): string | undefined {
    return value;
  }
}

/**
 * 基础模型类，实现序列化和反序列化
 */
export class BaseModelImpl implements BaseModel {
  id?: number;
  sid?: string;
  name?: string;
  isValid?: boolean;
  isSynced?: boolean;

  constructor(data: Partial<BaseModel> = {}) {
    this.id = data.id;
    this.sid = data.sid;
    this.name = data.name;
    this.isValid = data.isValid;
    this.isSynced = data.isSynced;
  }

  /**
   * 从 JSON 对象创建模型实例
   */
  static fromJson(json: Record<string, unknown>): BaseModelImpl {
    return new BaseModelImpl({
      id: JsonUtil.numberFromJson(json.id),
      sid: JsonUtil.stringFromJson(json.sid),
      name: JsonUtil.stringFromJson(json.name),
      isValid: JsonUtil.boolFromJson(json.isValid),
      isSynced: JsonUtil.boolFromJson(json.isSynced),
    });
  }

  /**
   * 转换为 JSON 对象
   */
  toJson(): Record<string, unknown> {
    return {
      id: JsonUtil.numberToJson(this.id),
      sid: JsonUtil.stringToJson(this.sid),
      name: JsonUtil.stringToJson(this.name),
      isValid: JsonUtil.boolToJson(this.isValid),
      isSynced: JsonUtil.boolToJson(this.isSynced),
    };
  }

  /**
   * 创建模型的副本
   */
  copyWith(updates: Partial<BaseModel>): BaseModelImpl {
    return new BaseModelImpl({
      id: updates.id !== undefined ? updates.id : this.id,
      sid: updates.sid !== undefined ? updates.sid : this.sid,
      name: updates.name !== undefined ? updates.name : this.name,
      isValid: updates.isValid !== undefined ? updates.isValid : this.isValid,
      isSynced: updates.isSynced !== undefined ? updates.isSynced : this.isSynced,
    });
  }

  /**
   * 检查模型是否有效
   */
  isValidModel(): boolean {
    return this.isValid === true && this.isSynced === true;
  }

  /**
   * 转换为字符串表示
   */
  toString(): string {
    return `BaseModel(id: ${this.id}, sid: ${this.sid}, name: ${this.name}, isValid: ${this.isValid}, isSynced: ${this.isSynced})`;
  }
}

/**
 * 类型守卫函数，检查对象是否符合 BaseModel 接口
 */
export function isBaseModel(obj: unknown): obj is BaseModel {
  if (typeof obj !== 'object' || obj === null) {
    return false;
  }
  
  const record = obj as Record<string, unknown>;
  return (
    (record.id === undefined || typeof record.id === 'number') &&
    (record.sid === undefined || typeof record.sid === 'string') &&
    (record.name === undefined || typeof record.name === 'string') &&
    (record.isValid === undefined || typeof record.isValid === 'boolean') &&
    (record.isSynced === undefined || typeof record.isSynced === 'boolean')
  );
}

/**
 * 默认的空基础模型
 */
export const defaultBaseModel: BaseModel = {
  id: undefined,
  sid: undefined,
  name: undefined,
  isValid: false,
  isSynced: false,
};
