# OneNata Admin 数据模型

这个目录包含了 OneNata 商家管理系统的完整数据模型结构，基于 Firebase Firestore 设计。

## 文件结构

### 核心文件

- **`base_model.ts`** - 基础模型接口和实现类，包含 JsonUtil 工具类
- **`types.ts`** - 通用类型定义、枚举和工具函数
- **`index.ts`** - 模块导出索引文件

### 数据模型

- **`portal-user.ts`** - Portal User 相关模型（商家系统用户）
  - PortalUserAccount - 用户账户信息
  - PortalUserData - 用户详细信息
  - StoreStaffInfo - 员工店铺信息
  - UserAuthRecord - 用户认证记录
  - EmployeeServiceTransaction - 员工服务交易记录
  - EmployeeReview - 员工评价
  - EmployeeService - 员工服务
  - EmployeeSchedule - 员工排班
  - BankInfo - 银行信息
  - PortalUserAddress - 用户地址

- **`customer.ts`** - OneNata App 客户和宠物模型
  - UserAccount - OneNata App 用户账户
  - UserData - OneNata App 用户数据（包含社区相关字段）
  - Pet - 宠物信息
  - CustomerService - 客户服务类

- **`store.ts`** - 店铺相关模型
  - StoreAccount - 店铺账户
  - StoreInfo - 店铺信息
  - Product - 产品
  - Inventory - 库存
  - StoreService - 店铺服务
  - StoreServiceTransaction - 店铺服务交易
  - StoreReview - 店铺评价
  - SpecialEvent - 特殊活动

- **`order.ts`** - 订单相关模型
  - Order - 订单
  - TrackingRecord - 跟踪记录
  - OrderProducts - 订单商品
  - OrderService - 订单服务类

- **`payment.ts`** - 支付相关模型
  - Payment - 支付记录
  - PaymentService - 支付服务类

## 设计特点

### 1. 类型安全
- 所有模型都有对应的 TypeScript 接口和实现类
- 使用枚举定义状态和类型
- 提供类型守卫函数

### 2. JSON 序列化
- 每个模型都有 `fromJson()` 和 `toJson()` 方法
- JsonUtil 提供类型安全的转换函数
- 支持复杂对象的嵌套序列化

### 3. 继承结构
- 所有模型继承自 BaseModel
- 包含 id, sid, name, isValid, isSynced 等基础字段
- 提供 copyWith() 方法用于不可变更新

### 4. 业务方法
- 每个实现类都包含相关的业务逻辑方法
- 格式化显示方法（如 getFormattedPrice()）
- 状态检查方法（如 isActive(), isValid()）

### 5. 服务类
- 提供搜索、过滤、统计、验证等功能
- 支持批量数据处理
- 包含数据分析和报告功能

## 使用示例

```typescript
import { 
  PortalUserDataImpl, 
  StoreAccountImpl, 
  OrderImpl, 
  PaymentImpl,
  UserType,
  StoreStatus,
  OrderStatus,
  PaymentStatus 
} from '@/app/lib/models';

// 创建用户
const user = new PortalUserDataImpl({
  fid: 'firebase-uid',
  uid: 'portal-user-id',
  displayName: '张三',
  firstName: '三',
  lastName: '张',
  userType: UserType.PETSTORE_STAFF
});

// 创建店铺
const store = new StoreAccountImpl({
  ownerId: 'owner-id',
  storeName: '宠物之家',
  storeStatus: StoreStatus.ACTIVE,
  googlePlaceId: 'google-place-id'
});

// 序列化和反序列化
const json = user.toJson();
const userFromJson = PortalUserDataImpl.fromJson(json);
```

## Firestore 集合映射

| 模型 | Firestore 集合 |
|------|----------------|
| PortalUserAccount | portal-user |
| PortalUserData | portal-user-data |
| UserAccount | UserAccount |
| UserData | UserData |
| Pet | pet |
| StoreAccount | store-account |
| StoreInfo | store-info |
| Order | orders |
| Payment | payments |

## 枚举类型

系统定义了以下主要枚举：

- **UserType** - 用户类型（管理员、商家、员工等）
- **AuthChannel** - 认证渠道（邮箱、手机、Google 等）
- **PetType/PetGender** - 宠物类型和性别
- **StoreStatus** - 店铺状态
- **OrderStatus** - 订单状态
- **PaymentStatus** - 支付状态
- **DeliveryStatus** - 配送状态
- **EventType** - 活动类型

## 国际化支持

- 所有状态文本都有中文翻译
- 支持多货币格式化
- 时间戳格式化支持本地化 