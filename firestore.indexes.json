{"indexes": [{"collectionGroup": "portal-user-account", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "portal-user-account", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userType", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "lastLoginAt", "order": "DESCENDING"}]}, {"collectionGroup": "portal-user-data", "queryScope": "COLLECTION", "fields": [{"fieldPath": "phoneNumber", "order": "ASCENDING"}]}, {"collectionGroup": "portal-user-data", "queryScope": "COLLECTION", "fields": [{"fieldPath": "businessName", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "portal-user-account", "fieldPath": "email", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}, {"collectionGroup": "portal-user-data", "fieldPath": "phoneNumber", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}