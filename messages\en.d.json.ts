// This file is auto-generated by next-intl, do not edit directly.
// See: https://next-intl.dev/docs/workflows/typescript#messages-arguments

declare const messages: {
  "HomePage": {
    "title": "Hello world!",
    "about": "Go to the about page",
    "selectLanguage": "Select Language",
    "welcome": "Welcome to OneNata Pet Store Admin",
    "description": "Manage your pet store with ease",
    "getStarted": "Get Started",
    "login": "Login",
    "signup": "Sign Up"
  },
  "Auth": {
    "login": {
      "title": "Login to Your Account",
      "subtitle": "Welcome back! Please enter your credentials.",
      "email": "Email Address",
      "emailPlaceholder": "Enter your email",
      "password": "Password", 
      "passwordPlaceholder": "Enter your password",
      "phoneNumber": "Phone Number",
      "phoneNumberPlaceholder": "Enter your phone number",
      "rememberMe": "Remember me",
      "forgotPassword": "Forgot password?",
      "loginButton": "Login",
      "loading": "Loading...",
      "noAccount": "Don't have an account?",
      "signUp": "Sign up",
      "continueWith": "Or continue with",
      "errorRequiredEmail": "Email is required",
      "errorInvalidEmail": "Please enter a valid email address",
      "errorRequiredPassword": "Password is required",
      "errorInvalidCredentials": "Invalid email or password",
      "errorTooManyAttempts": "Too many failed attempts. Please try again later.",
      "errorGeneral": "An error occurred. Please try again.",
      "resetPassword": "Reset Password",
      "resetPasswordSuccess": "Password reset email sent successfully!",
      "resetPasswordInstructions": "Enter your email address and we'll send you a link to reset your password.",
      "close": "Close",
      "cancel": "Cancel",
      "send": "Send",
      "sending": "Sending..."
    },
    "signup": {
      "title": "Create Your Account",
      "subtitle": "Join OneNata Pet Store Admin today!",
      "firstName": "First Name",
      "firstNamePlaceholder": "Enter your first name",
      "lastName": "Last Name", 
      "lastNamePlaceholder": "Enter your last name",
      "displayName": "Display Name",
      "displayNamePlaceholder": "Enter your display name",
      "email": "Email Address",
      "emailPlaceholder": "Enter your email",
      "phoneNumber": "Phone Number",
      "phoneNumberPlaceholder": "Enter your phone number",
      "password": "Password",
      "passwordPlaceholder": "Enter your password",
      "confirmPassword": "Confirm Password",
      "confirmPasswordPlaceholder": "Confirm your password",
      "invitationCode": "Invitation Code",
      "invitationCodePlaceholder": "Enter your invitation code",
      "signUpButton": "Create Account",
      "loading": "Creating account...",
      "hasAccount": "Already have an account?",
      "login": "Login",
      "terms": "By creating an account, you agree to our",
      "termsLink": "Terms of Service",
      "and": "and",
      "privacyLink": "Privacy Policy",
      "errorRequiredFirstName": "First name is required",
      "errorRequiredLastName": "Last name is required",
      "errorRequiredEmail": "Email is required",
      "errorInvalidEmail": "Please enter a valid email address",
      "errorRequiredPhoneNumber": "Phone number is required",
      "errorRequiredPassword": "Password is required",
      "errorPasswordLength": "Password must be at least 8 characters",
      "errorPasswordMatch": "Passwords do not match",
      "errorRequiredInvitationCode": "Invitation code is required",
      "errorInvalidInvitationCode": "Invalid invitation code",
      "errorEmailInUse": "Email is already in use",
      "errorGeneral": "An error occurred. Please try again."
    },
    "forgotPassword": {
      "title": "Forgot Password",
      "subtitle": "Enter your email to reset your password",
      "email": "Email Address",
      "emailPlaceholder": "Enter your email",
      "phoneNumber": "Phone Number",
      "phoneNumberPlaceholder": "Enter your phone number",
      "sendButton": "Send Reset Link",
      "loading": "Sending...",
      "backToLogin": "Back to Login",
      "success": "Reset link sent successfully!",
      "successMessage": "We've sent a password reset link to your email address.",
      "errorRequiredEmail": "Email is required",
      "errorInvalidEmail": "Please enter a valid email address",
      "errorUserNotFound": "No account found with this email address",
      "errorGeneral": "An error occurred. Please try again."
    },
    "resetPassword": {
      "title": "Reset Password",
      "subtitle": "Enter your new password",
      "newPassword": "New Password",
      "newPasswordPlaceholder": "Enter new password",
      "confirmPassword": "Confirm Password",
      "confirmPasswordPlaceholder": "Confirm new password",
      "resetButton": "Reset Password",
      "loading": "Resetting...",
      "success": "Password reset successfully!",
      "backToLogin": "Back to Login",
      "errorRequiredPassword": "Password is required",
      "errorPasswordLength": "Password must be at least 8 characters",
      "errorPasswordMatch": "Passwords do not match",
      "errorInvalidToken": "Invalid or expired reset token",
      "errorGeneral": "An error occurred. Please try again."
    },
    "profile": {
      "title": "Complete Your Profile",
      "subtitle": "Please fill in your information",
      "firstName": "First Name",
      "firstNamePlaceholder": "Enter your first name",
      "lastName": "Last Name",
      "lastNamePlaceholder": "Enter your last name",
      "displayName": "Display Name",
      "displayNamePlaceholder": "Enter your display name",
      "dateOfBirth": "Date of Birth",
      "bio": "Bio",
      "bioPlaceholder": "Tell us about yourself",
      "address": "Address",
      "addressLine1": "Address Line 1",
      "addressLine1Placeholder": "Enter your address",
      "addressLine2": "Address Line 2",
      "addressLine2Placeholder": "Apartment, suite, etc.",
      "city": "City",
      "cityPlaceholder": "Enter your city",
      "province": "Province/State",
      "provincePlaceholder": "Enter your province",
      "country": "Country",
      "countryPlaceholder": "Enter your country",
      "postCode": "Postal Code",
      "postCodePlaceholder": "Enter your postal code",
      "saveButton": "Save Profile",
      "loading": "Saving...",
      "success": "Profile saved successfully!",
      "errorRequiredFirstName": "First name is required",
      "errorRequiredLastName": "Last name is required",
      "errorGeneral": "An error occurred. Please try again."
    },
    "verification": {
      "title": "Verify Your Email",
      "subtitle": "We've sent a verification code to your email",
      "code": "Verification Code",
      "codePlaceholder": "Enter verification code",
      "verifyButton": "Verify",
      "resendButton": "Resend Code",
      "loading": "Verifying...",
      "resending": "Resending...",
      "success": "Email verified successfully!",
      "errorRequiredCode": "Verification code is required",
      "errorInvalidCode": "Invalid verification code",
      "errorExpiredCode": "Verification code has expired",
      "errorGeneral": "An error occurred. Please try again."
    }
  }
};
export default messages;