import React from "react";
import { Component4 } from "../../../components/Component4";
import { VariantHoverTrueWrapper } from "../../../components/VariantHoverTrueWrapper";
import "./ContainerWrapper.css";

export const ContainerWrapper = (): JSX.Element => {
  return (
    <div className="container-wrapper">
      <div className="container-2">
        <p className="heading-ready-to">Ready to Transform Your Pet Care?</p>

        <div className="join-the-onenata-wrapper">
          <p className="join-the-onenata">
            Join the OneNata community today and experience the future of smart
            <br />
            pet living. Download the app now!
          </p>
        </div>

        <VariantHoverTrueWrapper className="component-3" variant="one" />
        <Component4
          className="component-5"
          text="Get on Google Play"
          textClassName="component-6"
          variant="one"
          variant1Color="white"
        />
      </div>
    </div>
  );
};

