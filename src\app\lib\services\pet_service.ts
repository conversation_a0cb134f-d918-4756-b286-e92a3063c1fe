import { Pet } from "../models/customer";
import { PetGender, PetType, PetVisibility } from "../models/types";
import { FirestoreDocument, FirestoreService } from "../firebase/services/firestore";
import { WhereFilterOp } from "firebase/firestore";
import { v4 as uuidv4 } from 'uuid';

// ==================== Types and Interfaces ====================

export interface CreatePetData {
  owner: string; // 主人ID
  name: string; // 宠物名称
  regId?: string; // 注册ID
  breed?: string; // 品种
  gender?: PetGender;
  type?: PetType;
  avatar?: string;
  isLive?: boolean;
  birthday?: Date;
  notes?: string; // 宠物备注
  visibility?: PetVisibility;
}

export interface UpdatePetData {
  regId?: string;
  breed?: string;
  gender?: PetGender;
  type?: PetType;
  avatar?: string;
  isLive?: boolean;
  birthday?: Date;
  visibility?: PetVisibility;
}

export interface PetSearchOptions {
  owner?: string;
  type?: PetType;
  gender?: PetGender;
  isLive?: boolean;
  limit?: number;
  offset?: number;
}

export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ServiceListResponse<T> extends ServiceResponse<T[]> {
  total?: number;
}

// ==================== Pet Service Class ====================

/**
 * Pet Service - 宠物管理服务类
 * 负责处理宠物信息的增删改查操作
 */
export class PetService {
  private readonly PET_COLLECTION = 'pet';

  // ==================== Create Operations ====================

  /**
   * Create new pet
   * 创建新宠物
   */
  async createPet(data: CreatePetData, createdBy: string): Promise<ServiceResponse<string>> {
    try {
      // Validate required fields
      if (!data.owner) {
        return {
          success: false,
          error: 'Owner ID is required'
        };
      }

      const petId = uuidv4();
      const petData: Partial<Pet> = {
        sid: petId,
        ownerId: data.owner,
        name: data.name,
        regId: data.regId ?? '',
        breed: data.breed,
        gender: data.gender,
        type: data.type,
        avatar: data.avatar ?? '',
        isLive: data.isLive ?? true,
        birthday: data.birthday,
        notes: data.notes ?? '',
        visibility: data.visibility || PetVisibility.PUBLIC,
        create_date: new Date(),
        update_date: new Date(),
        created_by: createdBy,
        updated_by: createdBy
      };

      const result = await FirestoreService.createWithId(
        this.PET_COLLECTION,
        petId,
        petData
      );

      if (result.success) {
        return {
          success: true,
          data: petId,
          message: 'Pet created successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to create pet'
        };
      }

    } catch (error) {
      console.error('Error creating pet:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create pet'
      };
    }
  }

  // ==================== Read Operations ====================

  /**
   * Get pet by ID
   * 根据ID获取宠物信息
   */
  async getPetById(petId: string): Promise<ServiceResponse<Pet>> {
    try {
      const result = await FirestoreService.getById<FirestoreDocument>(
        this.PET_COLLECTION,
        petId
      );

      if (result.success && result.data) {
        const pet = result.data as unknown as Pet;
        return {
          success: true,
          data: pet,
          message: 'Pet retrieved successfully'
        };
      }

      return {
        success: false,
        error: 'Pet not found'
      };

    } catch (error) {
      console.error('Error getting pet by ID:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get pet'
      };
    }
  }

  /**
   * Get pets by owner
   * 根据主人ID获取宠物列表
   */
  async getPetsByOwner(ownerId: string): Promise<ServiceListResponse<Pet>> {
    try {
      const result = await FirestoreService.getMany<FirestoreDocument>(
        this.PET_COLLECTION,
        {
          where: [{ field: 'owner', operator: '==', value: ownerId }],
          orderBy: [{ field: 'create_date', direction: 'desc' }]
        }
      );

      if (result.success && result.data) {
        const pets = result.data as unknown as Pet[];
        return {
          success: true,
          data: pets,
          total: pets.length,
          message: 'Pets retrieved successfully'
        };
      }

      return {
        success: false,
        error: result.error || 'Failed to get pets'
      };

    } catch (error) {
      console.error('Error getting pets by owner:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get pets'
      };
    }
  }

  /**
   * Search pets with filters
   * 搜索宠物（支持多种过滤条件）
   */
  async searchPets(options: PetSearchOptions): Promise<ServiceListResponse<Pet>> {
    try {
      const whereConditions: Array<{ field: string; operator: WhereFilterOp; value: unknown }> = [];

      if (options.owner) {
        whereConditions.push({ field: 'owner', operator: '==', value: options.owner });
      }

      if (options.type) {
        whereConditions.push({ field: 'type', operator: '==', value: options.type });
      }

      if (options.gender) {
        whereConditions.push({ field: 'gender', operator: '==', value: options.gender });
      }

      if (options.isLive !== undefined) {
        whereConditions.push({ field: 'isLive', operator: '==', value: options.isLive });
      }

      const queryOptions: {
        where: Array<{ field: string; operator: WhereFilterOp; value: unknown }>;
        orderBy: Array<{ field: string; direction: 'asc' | 'desc' }>;
        limit?: number;
        offset?: number;
      } = {
        where: whereConditions,
        orderBy: [{ field: 'create_date', direction: 'desc' }]
      };

      if (options.limit) {
        queryOptions.limit = options.limit;
      }

      if (options.offset) {
        queryOptions.offset = options.offset;
      }

      const result = await FirestoreService.getMany<FirestoreDocument>(
        this.PET_COLLECTION,
        queryOptions
      );

      if (result.success && result.data) {
        const pets = result.data as unknown as Pet[];
        return {
          success: true,
          data: pets,
          total: pets.length,
          message: 'Pets retrieved successfully'
        };
      }

      return {
        success: false,
        error: result.error || 'Failed to search pets'
      };

    } catch (error) {
      console.error('Error searching pets:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search pets'
      };
    }
  }

  /**
   * Get all pets (with pagination)
   * 获取所有宠物（支持分页）
   */
  async getAllPets(limit?: number, offset?: number): Promise<ServiceListResponse<Pet>> {
    try {
      const queryOptions: {
        orderBy: Array<{ field: string; direction: 'asc' | 'desc' }>;
        limit?: number;
        offset?: number;
      } = {
        orderBy: [{ field: 'create_date', direction: 'desc' }]
      };

      if (limit) {
        queryOptions.limit = limit;
      }

      if (offset) {
        queryOptions.offset = offset;
      }

      const result = await FirestoreService.getMany<FirestoreDocument>(
        this.PET_COLLECTION,
        queryOptions
      );

      if (result.success && result.data) {
        const pets = result.data as unknown as Pet[];
        return {
          success: true,
          data: pets,
          total: pets.length,
          message: 'Pets retrieved successfully'
        };
      }

      return {
        success: false,
        error: result.error || 'Failed to get pets'
      };

    } catch (error) {
      console.error('Error getting all pets:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get pets'
      };
    }
  }

  // ==================== Update Operations ====================

  /**
   * Update pet information
   * 更新宠物信息
   */
  async updatePet(petId: string, data: UpdatePetData, updatedBy: string): Promise<ServiceResponse<void>> {
    try {
      // Check if pet exists
      const existingPet = await this.getPetById(petId);
      if (!existingPet.success) {
        return {
          success: false,
          error: 'Pet not found'
        };
      }

      const updateData: Partial<Pet> = {
        ...data,
        update_date: new Date(),
        updated_by: updatedBy
      };

      const result = await FirestoreService.update(
        this.PET_COLLECTION,
        petId,
        updateData
      );

      if (result.success) {
        return {
          success: true,
          message: 'Pet updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update pet'
        };
      }

    } catch (error) {
      console.error('Error updating pet:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update pet'
      };
    }
  }

  /**
   * Update pet avatar
   * 更新宠物头像
   */
  async updatePetAvatar(petId: string, avatarUrl: string, updatedBy: string): Promise<ServiceResponse<void>> {
    try {
      const result = await FirestoreService.update(
        this.PET_COLLECTION,
        petId,
        {
          avatar: avatarUrl,
          update_date: new Date(),
          updated_by: updatedBy
        }
      );

      if (result.success) {
        return {
          success: true,
          message: 'Pet avatar updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update pet avatar'
        };
      }

    } catch (error) {
      console.error('Error updating pet avatar:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update pet avatar'
      };
    }
  }

  /**
   * Update pet location
   * 更新宠物位置信息
   */
  async updatePetLocation(
    petId: string, 
    location: { latitude: number; longitude: number; geohash?: string },
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const result = await FirestoreService.update(
        this.PET_COLLECTION,
        petId,
        {
          latestLocation: location,
          latestLocationAt: new Date(),
          update_date: new Date(),
          updated_by: updatedBy
        }
      );

      if (result.success) {
        return {
          success: true,
          message: 'Pet location updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update pet location'
        };
      }

    } catch (error) {
      console.error('Error updating pet location:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update pet location'
      };
    }
  }

  /**
   * Update pet battery level
   * 更新宠物设备电池电量
   */
  async updatePetBatteryLevel(
    petId: string, 
    batteryLevel: number,
    updatedBy: string
  ): Promise<ServiceResponse<void>> {
    try {
      const result = await FirestoreService.update(
        this.PET_COLLECTION,
        petId,
        {
          latestBatteryLevel: batteryLevel,
          latestBatteryLevelAt: new Date(),
          update_date: new Date(),
          updated_by: updatedBy
        }
      );

      if (result.success) {
        return {
          success: true,
          message: 'Pet battery level updated successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to update pet battery level'
        };
      }

    } catch (error) {
      console.error('Error updating pet battery level:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update pet battery level'
      };
    }
  }

  // ==================== Delete Operations ====================

  /**
   * Delete pet
   * 删除宠物
   */
  async deletePet(petId: string): Promise<ServiceResponse<void>> {
    try {
      // Check if pet exists
      const existingPet = await this.getPetById(petId);
      if (!existingPet.success) {
        return {
          success: false,
          error: 'Pet not found'
        };
      }

      const result = await FirestoreService.delete(
        this.PET_COLLECTION,
        petId
      );

      if (result.success) {
        return {
          success: true,
          message: 'Pet deleted successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to delete pet'
        };
      }

    } catch (error) {
      console.error('Error deleting pet:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete pet'
      };
    }
  }

  /**
   * Soft delete pet (mark as not live)
   * 软删除宠物（标记为非活跃）
   */
  async softDeletePet(petId: string, updatedBy: string): Promise<ServiceResponse<void>> {
    try {
      const result = await FirestoreService.update(
        this.PET_COLLECTION,
        petId,
        {
          isLive: false,
          update_date: new Date(),
          updated_by: updatedBy
        }
      );

      if (result.success) {
        return {
          success: true,
          message: 'Pet soft deleted successfully'
        };
      } else {
        return {
          success: false,
          error: result.error || 'Failed to soft delete pet'
        };
      }

    } catch (error) {
      console.error('Error soft deleting pet:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to soft delete pet'
      };
    }
  }

  // ==================== Utility Methods ====================

  /**
   * Get pet statistics
   * 获取宠物统计信息
   */
  async getPetStats(ownerId?: string): Promise<ServiceResponse<{
    totalPets: number;
    activePets: number;
    inactivePets: number;
    petsByType: Record<string, number>;
    petsByGender: Record<string, number>;
  }>> {
    try {
      const whereConditions: Array<{ field: string; operator: WhereFilterOp; value: unknown }> = 
        ownerId ? [{ field: 'owner', operator: '==', value: ownerId }] : [];
      
      const result = await FirestoreService.getMany<FirestoreDocument>(
        this.PET_COLLECTION,
        {
          where: whereConditions
        }
      );

      if (result.success && result.data) {
        const pets = result.data as unknown as Pet[];
        
        const stats = {
          totalPets: pets.length,
          activePets: pets.filter(pet => pet.isLive).length,
          inactivePets: pets.filter(pet => !pet.isLive).length,
          petsByType: {} as Record<string, number>,
          petsByGender: {} as Record<string, number>
        };

        // Count by type
        pets.forEach(pet => {
          if (pet.type) {
            stats.petsByType[pet.type] = (stats.petsByType[pet.type] || 0) + 1;
          }
        });

        // Count by gender
        pets.forEach(pet => {
          if (pet.gender) {
            stats.petsByGender[pet.gender] = (stats.petsByGender[pet.gender] || 0) + 1;
          }
        });

        return {
          success: true,
          data: stats,
          message: 'Pet statistics retrieved successfully'
        };
      }

      return {
        success: false,
        error: result.error || 'Failed to get pet statistics'
      };

    } catch (error) {
      console.error('Error getting pet statistics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get pet statistics'
      };
    }
  }

  /**
   * Check if pet exists
   * 检查宠物是否存在
   */
  async petExists(petId: string): Promise<boolean> {
    try {
      const result = await this.getPetById(petId);
      return result.success;
    } catch (error) {
      console.error('Error checking if pet exists:', error);
      return false;
    }
  }

  /**
   * Get pets by multiple IDs
   * 根据多个ID获取宠物列表
   */
  async getPetsByIds(petIds: string[]): Promise<ServiceListResponse<Pet>> {
    try {
      if (petIds.length === 0) {
        return {
          success: true,
          data: [],
          total: 0,
          message: 'No pet IDs provided'
        };
      }

      const pets: Pet[] = [];
      const errors: string[] = [];

      for (const petId of petIds) {
        const result = await this.getPetById(petId);
        if (result.success && result.data) {
          pets.push(result.data);
        } else {
          errors.push(`Failed to get pet ${petId}: ${result.error}`);
        }
      }

      return {
        success: errors.length === 0,
        data: pets,
        total: pets.length,
        message: errors.length > 0 ? `Retrieved ${pets.length} pets with ${errors.length} errors` : 'Pets retrieved successfully',
        error: errors.length > 0 ? errors.join('; ') : undefined
      };

    } catch (error) {
      console.error('Error getting pets by IDs:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get pets by IDs'
      };
    }
  }
}

// Export default instance
export default new PetService();