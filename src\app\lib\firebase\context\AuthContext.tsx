'use client';

import React, { createContext, useContext, useState, useEffect, useRef, useCallback, ReactNode } from 'react';
import { 
  User,
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut
} from 'firebase/auth';
import { auth } from '../config';
import { getUserData, getUserAccountData } from '../services/auth';
import { PortalUserData, PortalUserAccount } from '../../models/portal-user';
import { UserType } from '../../models/types';

interface AuthContextType {
  user: User | null;
  userData: PortalUserData | null;
  userAccount: PortalUserAccount | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  setRememberMe: (remember: boolean) => void;
  refreshUser: () => Promise<void>;
  // 新增：手动重置非活跃计时器（已禁用）
  resetInactivityTimer: () => void;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType>({
  user: null,
  userData: null,
  userAccount: null,
  loading: true,
  signIn: async () => {},
  signOut: async () => {},
  setRememberMe: () => {},
  refreshUser: async () => {},
  resetInactivityTimer: () => {}
});

// 导出自定义Hook，方便在组件中使用
export const useAuth = () => useContext(AuthContext);

// 认证提供者组件
export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userData, setUserData] = useState<PortalUserData | null>(null);
  const [userAccount, setUserAccount] = useState<PortalUserAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [rememberMe, setRememberMe] = useState(false);
  
  // 新增：非活跃计时器相关状态（已禁用）
  const inactivityTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 配置项（已禁用）
  // const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30分钟非活跃自动登出
  // const SESSION_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查一次会话状态

  // 重置非活跃计时器（已禁用）
  const resetInactivityTimer = useCallback(() => {
    // 已禁用自动登出功能
    /*
    if (!rememberMe && user) {
      // 更新最后活动时间到localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('last_activity', Date.now().toString());
      }
      
      // 清除旧的计时器
      if (inactivityTimerRef.current) {
        clearTimeout(inactivityTimerRef.current);
      }
      
      // 设置新的计时器
      const newTimer = setTimeout(() => {
        console.log('用户长时间未活跃，自动登出');
        signOut();
      }, INACTIVITY_TIMEOUT);
      
      inactivityTimerRef.current = newTimer;
    }
    */
  }, [rememberMe, user]);

  // 处理用户活动事件（已禁用）
  /*
  const handleUserActivity = useCallback(() => {
    if (!rememberMe && user) {
      resetInactivityTimer();
    }
  }, [rememberMe, user, resetInactivityTimer]);

  // 页面可见性变化处理（已禁用）
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden) {
      // 页面隐藏时记录时间
      if (typeof window !== 'undefined') {
        localStorage.setItem('page_hidden_time', Date.now().toString());
      }
    } else {
      // 页面重新可见时检查是否需要登出
      if (!rememberMe && user && typeof window !== 'undefined') {
        const hiddenTime = localStorage.getItem('page_hidden_time');
        if (hiddenTime) {
          const timeDiff = Date.now() - parseInt(hiddenTime);
          // 如果隐藏时间超过非活跃超时时间，自动登出
          if (timeDiff > INACTIVITY_TIMEOUT) {
            console.log('页面隐藏时间过长，自动登出');
            signOut();
            return;
          }
        }
        // 重置非活跃计时器
        resetInactivityTimer();
      }
    }
  }, [rememberMe, user, resetInactivityTimer]);

  // 页面关闭前处理（已禁用）
  const handleBeforeUnload = useCallback(() => {
    if (!rememberMe && user) {
      // 在非记住我模式下，页面关闭时清除会话标记
      sessionStorage.removeItem('auth_session');
    }
  }, [rememberMe, user]);
  */

  // 监听用户活动和页面状态（已禁用）
  useEffect(() => {
    // 已禁用自动登出功能
    /*
    if (typeof window !== 'undefined' && user && !rememberMe) {
      // 用户活动事件
      const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
      
      activityEvents.forEach(event => {
        document.addEventListener(event, handleUserActivity, true);
      });

      // 页面可见性变化
      document.addEventListener('visibilitychange', handleVisibilityChange);
      
      // 页面关闭前事件
      window.addEventListener('beforeunload', handleBeforeUnload);
      
      // 初始化非活跃计时器
      resetInactivityTimer();

      return () => {
        // 清理事件监听器
        activityEvents.forEach(event => {
          document.removeEventListener(event, handleUserActivity, true);
        });
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('beforeunload', handleBeforeUnload);
        
        // 清理计时器
        if (inactivityTimerRef.current) {
          clearTimeout(inactivityTimerRef.current);
        }
      };
    }
    */
  }, [user, rememberMe]);

  // 定期检查会话状态（已禁用）
  useEffect(() => {
    // 已禁用自动登出功能
    /*
    if (typeof window !== 'undefined' && user && !rememberMe) {
      const sessionCheckInterval = setInterval(() => {
        const hasSession = sessionStorage.getItem('auth_session');
        const lastActivityTime = parseInt(localStorage.getItem('last_activity') || '0');
        const currentTime = Date.now();
        
        // 检查会话是否存在
        if (!hasSession) {
          console.log('会话标记不存在，自动登出');
          signOut();
          return;
        }
        
        // 检查最后活动时间
        if (lastActivityTime && (currentTime - lastActivityTime) > INACTIVITY_TIMEOUT) {
          console.log('超过非活跃时间限制，自动登出');
          signOut();
          return;
        }
        
        // 更新最后活动时间
        localStorage.setItem('last_activity', currentTime.toString());
      }, SESSION_CHECK_INTERVAL);

      return () => clearInterval(sessionCheckInterval);
    }
    */
  }, [user, rememberMe]);

  useEffect(() => {
    // 会话管理逻辑（部分禁用）
    if (typeof window !== 'undefined') {
      if (rememberMe) {
        // 记住我模式：使用localStorage持久化
        localStorage.setItem('remember_me', 'true');
      } else {
        // 非记住我模式：使用sessionStorage
        localStorage.removeItem('remember_me');
        
        // 已禁用会话自动清理功能
        /*
        if (user) {
          // 设置会话标记
          sessionStorage.setItem('auth_session', 'true');
          localStorage.setItem('last_activity', Date.now().toString());
        } else {
          // 用户未登录时清除所有会话数据
          sessionStorage.removeItem('auth_session');
          localStorage.removeItem('last_activity');
          localStorage.removeItem('page_hidden_time');
        }
        */
      }
    }
  }, [rememberMe, user]);

  // 页面加载时检查会话状态（已禁用）
  useEffect(() => {
    // 已禁用自动登出功能
    /*
    if (typeof window !== 'undefined') {
      const savedRememberMe = localStorage.getItem('remember_me') === 'true';
      setRememberMe(savedRememberMe);
      
      // 如果不是记住我模式，检查会话状态
      if (!savedRememberMe) {
        const hasSession = sessionStorage.getItem('auth_session');
        const lastActivity = localStorage.getItem('last_activity');
        
        if (!hasSession && lastActivity) {
          // 没有会话标记但有上次活动记录，说明页面被关闭过
          console.log('检测到页面关闭，清除认证状态');
          localStorage.removeItem('last_activity');
          localStorage.removeItem('page_hidden_time');
          if (auth.currentUser) {
            firebaseSignOut(auth);
          }
        } else if (lastActivity) {
          // 检查上次活动时间
          const timeDiff = Date.now() - parseInt(lastActivity);
          if (timeDiff > INACTIVITY_TIMEOUT) {
            console.log('检测到长时间未活跃，清除认证状态');
            localStorage.removeItem('last_activity');
            localStorage.removeItem('page_hidden_time');
            if (auth.currentUser) {
              firebaseSignOut(auth);
            }
          }
        }
      }
    }
    */
    
    // 只保留基本的记住我状态恢复
    if (typeof window !== 'undefined') {
      const savedRememberMe = localStorage.getItem('remember_me') === 'true';
      setRememberMe(savedRememberMe);
    }
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setUser(firebaseUser);
      
      if (firebaseUser) {
        try {
          // 使用 getUserData 函数获取 portal-user-data 集合中的用户数据
          const portalUserData = await getUserData(firebaseUser.uid);
          const portalUserAccount = await getUserAccountData(firebaseUser.uid);

          if (portalUserData) {
            setUserData(portalUserData);
            setUserAccount(portalUserAccount);
            
            // 保存用户信息到本地存储（如果记住我）
            if (rememberMe && typeof window !== 'undefined') {
              localStorage.setItem('auth_user', JSON.stringify({
                uid: portalUserData.uid,
                email: firebaseUser.email,
                fid: portalUserData.fid,
              }));
            }
          } else {
            console.warn('Portal user data not found for user:', firebaseUser.uid);
            setUserData(null);
            setUserAccount(null);
          }
        } catch (error) {
          console.error('Error fetching portal user data:', error);
          setUserData(null);
          setUserAccount(null);
        }
      } else {
        setUserData(null);
        setUserAccount(null);
        // 清除所有存储的用户信息
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_user');
          // 已禁用自动会话清理
          /*
          sessionStorage.removeItem('auth_session');
          localStorage.removeItem('last_activity');
          localStorage.removeItem('page_hidden_time');
          */
        }
        
        // 清理计时器（如果有的话）
        if (inactivityTimerRef.current) {
          clearTimeout(inactivityTimerRef.current);
          inactivityTimerRef.current = null;
        }
      }
      
      setLoading(false);
    });

    return () => unsubscribe();
  }, [rememberMe]);

  const signIn = async (email: string, password: string) => {
    await signInWithEmailAndPassword(auth, email, password);
  };

  const signOut = async () => {
    await firebaseSignOut(auth);
    setUser(null);
    setUserData(null);
    setUserAccount(null);
    
    // 清除所有会话和本地存储
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_user');
      localStorage.removeItem('remember_me');
      // 已禁用自动会话清理
      /*
      sessionStorage.removeItem('auth_session');
      localStorage.removeItem('last_activity');
      localStorage.removeItem('page_hidden_time');
      */
    }
    
    // 清理计时器
    if (inactivityTimerRef.current) {
      clearTimeout(inactivityTimerRef.current);
      inactivityTimerRef.current = null;
    }
  };

  const refreshUser = async () => {
    const currentUser = auth.currentUser;
    if (currentUser) {
      await currentUser.reload();
      // 手动更新用户状态，确保最新的 emailVerified 状态
      setUser({ ...currentUser });
      
      // 同时刷新 userData
      try {
        const freshUserData = await getUserData(currentUser.uid);
        const freshUserAccount = await getUserAccountData(currentUser.uid);
        setUserData(freshUserData);
        setUserAccount(freshUserAccount);
      } catch (error) {
        console.error('Error refreshing user data:', error);
      }
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      userData, 
      userAccount,
      loading, 
      signIn, 
      signOut, 
      setRememberMe, 
      refreshUser,
      resetInactivityTimer 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

// 受保护的路由组件
export const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { user, userData, userAccount, loading } = useAuth();
  
  // 在客户端进行重定向逻辑
  useEffect(() => {
    if (!loading && typeof window !== 'undefined') {
      if (!user) {
        // 用户未登录，重定向到登录页面
        window.location.href = '/auth/login';
      } else if (!user.emailVerified) {
        // 用户已登录但邮箱未验证，重定向到邮箱验证页面
        window.location.href = '/auth/verify-email';
      } else if (userAccount?.needChangePassword) {
        // 用户需要修改密码，重定向到密码修改页面
        window.location.href = '/auth/change-password';
      } else if (!userData?.isProfileCompleted) {
        // 用户已登录且邮箱已验证但个人资料未完成，重定向到个人资料设置页面
        window.location.href = '/auth/profile-setup';
      } else if (userData) {
        // 用户已完成所有验证，跳转到通用仪表板路由，由路由器自动分发
        window.location.href = '/dashboard';
      }
    }
  }, [user, userData, userAccount, loading]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  return user && user.emailVerified && !userAccount?.needChangePassword && userData?.isProfileCompleted ? <>{children}</> : null;
};

// 仅需要登录的路由组件（不需要邮箱验证）
export const AuthenticatedRoute = ({ children }: { children: ReactNode }) => {
  const { user, loading } = useAuth();
  
  // 在客户端如果用户未登录，则重定向到登录页面
  useEffect(() => {
    if (!loading && !user && typeof window !== 'undefined') {
      console.log('AuthenticatedRoute: User not found, redirecting to login');
      window.location.href = '/auth/login';
    }
  }, [user, loading]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  return <>{children}</>;
};

// 仅管理员路由组件
export const AdminRoute = ({ children }: { children: ReactNode }) => {
  const { userData, loading } = useAuth();
  
  // 在客户端如果用户不是管理员，则重定向到首页
  useEffect(() => {
    if (!loading && (!userData || userData.userType !== UserType.ONENATA_ADMIN) && typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }, [userData, loading]);  

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  return userData && userData.userType === UserType.ONENATA_ADMIN ? <>{children}</> : null;
};

// 业务仪表板路由组件（适用于所有店铺相关角色）
export const BusinessDashboardRoute = ({ children }: { children: ReactNode }) => {
  const { userData, loading } = useAuth();
  
  // 在客户端如果用户不是店铺相关角色，则重定向到首页
  useEffect(() => {
    if (!loading && (!userData || ![UserType.PETSTORE_OWNER, UserType.PETSTORE_ADMIN, UserType.PETSTORE_STAFF].includes(userData.userType)) && typeof window !== 'undefined') {
      if (userData?.userType === UserType.ONENATA_ADMIN) {
        window.location.href = '/dashboard-internal';
      } else {
        window.location.href = '/auth/login';
      }
    }
  }, [userData, loading]);  

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  return userData && [UserType.PETSTORE_OWNER, UserType.PETSTORE_ADMIN, UserType.PETSTORE_STAFF].includes(userData.userType) ? <>{children}</> : null;
};

// 宠物店管理员路由组件
export const PetStoreAdminRoute = ({ children }: { children: ReactNode }) => {
  const { userData, loading } = useAuth();
  
  // 在客户端如果用户不是宠物店管理员，则重定向到首页
  useEffect(() => {
    if (!loading && (!userData || userData.userType !== UserType.PETSTORE_OWNER) && typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }, [userData, loading]);  

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  return userData && userData.userType === UserType.PETSTORE_OWNER ? <>{children}</> : null;
}; 