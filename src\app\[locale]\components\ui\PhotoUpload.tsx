'use client';

import React, { useState, useRef, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { uploadUserAvatar, validateImageFile, compressImage, UploadProgress } from "../../../lib/firebase/services";

interface PhotoUploadProps {
  userId: string;
  currentPhotoURL?: string;
  onPhotoUploaded?: (photoURL: string) => void;
  onError?: (error: string) => void;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  showProgress?: boolean;
  autoCompress?: boolean;
  userName?: string;
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  userId,
  currentPhotoURL,
  onPhotoUploaded,
  onError,
  className = '',
  size = 'medium',
  showProgress = true,
  autoCompress = true,
  userName = '',
}) => {
  const t = useTranslations('photoUpload');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [previewURL, setPreviewURL] = useState<string>(currentPhotoURL || '');
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({ progress: 0, isUploading: false });
  const [isDragging, setIsDragging] = useState(false);

  // 尺寸配置
  const sizeConfig = {
    small: { width: 'w-16', height: 'h-16', text: 'text-xs' },
    medium: { width: 'w-24', height: 'h-24', text: 'text-sm' },
    large: { width: 'w-32', height: 'h-32', text: 'text-base' },
  };

  const config = sizeConfig[size];

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      onError?.(validation.error || 'fail to validate file');
      return;
    }

    try {
      // 创建本地预览
      const previewUrl = URL.createObjectURL(file);
      setPreviewURL(previewUrl);

      // 压缩图片（如果启用）
      let fileToUpload = file;
      if (autoCompress && file.size > 1024 * 1024) { // 大于1MB才压缩
        fileToUpload = await compressImage(file, 800, 0.8);
      }

      // 上传到Firebase Storage
      const result = await uploadUserAvatar(
        fileToUpload,
        userId,
        (progress) => {
          setUploadProgress(progress);
          if (progress.error) {
            onError?.(progress.error);
          }
        }
      );

      // 上传成功
      setPreviewURL(result.url);
      onPhotoUploaded?.(result.url);
      
      // 清理本地预览URL
      URL.revokeObjectURL(previewUrl);
    } catch (error) {
      console.error('Photo upload error:', error);
      onError?.(error instanceof Error ? error.message : '上传失败');
    }
  }, [userId, autoCompress, onPhotoUploaded, onError]);

  // 处理文件输入变化
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  // 点击上传
  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  // 获取显示的用户名首字母
  const getInitial = () => {
    if (userName && userName.length > 0) {
      return userName.charAt(0).toUpperCase();
    }
    return 'U';
  };

  // 调试信息
  console.log('PhotoUpload render:', {
    userName,
    previewURL,
    initial: getInitial(),
    size,
    config
  });

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* 照片预览区域 */}
      <div
        className={`relative ${config.width} ${config.height} rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center text-white font-bold shadow-lg cursor-pointer transition-all duration-200 hover:scale-105 ${
          isDragging ? 'ring-4 ring-purple-500 ring-opacity-50' : ''
        } ${uploadProgress.isUploading ? 'opacity-75' : ''}`}
        onClick={handleUploadClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        style={{ backgroundColor: '#A126FF' }}
      >
        {previewURL ? (
          <Image
            src={previewURL}
            alt={t('avatarPreview')}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="flex items-center justify-center w-full h-full">
            <span className="text-2xl font-bold text-white z-10">
              {getInitial()}
            </span>
          </div>
        )}
        
        {/* 上传覆盖层 - 只在有照片时显示 */}
        {previewURL && (
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
            <div className="opacity-0 hover:opacity-100 transition-opacity duration-200">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
          </div>
        )}

        {/* 上传按钮 */}
        <button
          type="button"
          className="absolute -bottom-2 -right-2 w-8 h-8 bg-white rounded-full shadow-lg border-2 border-[#A126FF] flex items-center justify-center text-[#A126FF] hover:bg-[#A126FF] hover:text-white transition-colors duration-200"
          onClick={(e) => {
            e.stopPropagation();
            handleUploadClick();
          }}
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
        </button>
      </div>

      {/* 上传进度 */}
      {showProgress && uploadProgress.isUploading && (
        <div className="w-full max-w-xs">
          <div className="flex justify-between items-center mb-2">
            <span className={`${config.text} text-gray-600`}>{t('uploading')}</span>
            <span className={`${config.text} text-gray-600`}>{uploadProgress.progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-[#A126FF] to-[#8a20d8] h-2 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress.progress}%` }}
            />
          </div>
        </div>
      )}

      {/* 上传提示 */}
      <div className="text-center">
        <p className={`${config.text} text-gray-600 mb-1`}>
          {t('uploadHint')}
        </p>
        <p className={`text-xs text-gray-500`}>
          {t('supportedFormats')}
        </p>
        {/* 调试信息 */}
        <div className="mt-2 text-xs text-gray-400">
          <p>用户名: {userName || '(空)'}</p>
          <p>首字母: {getInitial()}</p>
          <p>预览URL: {previewURL ? '有' : '无'}</p>
        </div>
      </div>

      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default PhotoUpload; 