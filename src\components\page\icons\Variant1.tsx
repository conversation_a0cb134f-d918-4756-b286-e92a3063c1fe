/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";

interface Props {
  color: string;
  className: any;
}

export const Variant1 = ({
  color = "#333333",
  className,
}: Props): JSX.Element => {
  return (
    <svg
      className={`variant-1 ${className}`}
      fill="none"
      height="25"
      viewBox="0 0 25 25"
      width="25"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        className="path"
        d="M23.0822 13.0938C23.0822 12.3138 23.0122 11.5638 22.8822 10.8438H12.5222V15.1038H18.4422C18.1822 16.4738 17.4022 17.6337 16.2322 18.4137V21.1838H19.8022C21.8822 19.2638 23.0822 16.4437 23.0822 13.0938Z"
        fill={color}
      />

      <path
        className="path"
        d="M12.5222 23.8453C15.4922 23.8453 17.9822 22.8653 19.8022 21.1853L16.2322 18.4153C15.2522 19.0753 14.0022 19.4753 12.5222 19.4753C9.66221 19.4753 7.23221 17.5453 6.36221 14.9453H2.70221V17.7853C4.51221 21.3753 8.22221 23.8453 12.5222 23.8453Z"
        fill={color}
      />

      <path
        className="path"
        d="M6.36222 14.9341C6.14222 14.2741 6.01222 13.5741 6.01222 12.8441C6.01222 12.1141 6.14222 11.4141 6.36222 10.7541V7.91406H2.70222C1.95222 9.39406 1.52222 11.0641 1.52222 12.8441C1.52222 14.6241 1.95222 16.2941 2.70222 17.7741L5.55222 15.5541L6.36222 14.9341Z"
        fill={color}
      />

      <path
        className="path"
        d="M12.5222 6.22375C14.1522 6.22375 15.5822 6.78375 16.7322 7.86375L19.8822 4.71375C17.9722 2.93375 15.4922 1.84375 12.5222 1.84375C8.22221 1.84375 4.51221 4.31375 2.70221 7.91375L6.36221 10.7537C7.23221 8.15375 9.66221 6.22375 12.5222 6.22375Z"
        fill={color}
      />
    </svg>
  );
};
