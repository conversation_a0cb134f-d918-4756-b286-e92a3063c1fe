'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { signUp } from '../../../lib/firebase/services/auth';
import { AuthError } from '../../../lib/types/common';
import { UserType } from '../../../lib/models/types';
import NotificationPermissionModal from '../../components/NotificationPermissionModal';
import { validateEmail } from '../../../lib/utils/validations';
import { PhoneNumberInput, validatePhoneNumber } from '../../components/ui/PhoneNumberInput';
import { useAuth } from '../../../lib/firebase/context/AuthContext';

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  confirmPassword: string;
  invitationCode: string;
}

export default function SignupPage() {
  const t = useTranslations('auth-signup');
  const locale = useLocale();
  const router = useRouter();
  const { user, loading } = useAuth();
  let userType = UserType.PETSTORE_OWNER;

  // 如果用户已登录，重定向到仪表板
  useEffect(() => {
    if (!loading && user) {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, loading, router, locale]);
  
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '+1 ',
    password: '',
    confirmPassword: '',
    invitationCode: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [showProgress, setShowProgress] = useState(false);



  const handleInputChange = (field: keyof FormData, value: string) => {
    // PhoneNumberInput 组件已经处理了格式化，直接使用
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = t('validation.firstNameRequired');
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('validation.lastNameRequired');
    }
    if (!formData.email.trim()) {
      newErrors.email = t('validation.emailRequired');
    } else if (!validateEmail(formData.email)) {
      newErrors.email = t('validation.emailInvalid');
    }
    if (formData.phoneNumber && formData.phoneNumber.trim() !== '+1 ' && !validatePhoneNumber(formData.phoneNumber)) {
      newErrors.phoneNumber = t('validation.phoneNumberInvalid');
    }
    if (!formData.password) {
      newErrors.password = t('validation.passwordRequired');
    } else if (formData.password.length < 6) {
      newErrors.password = t('validation.passwordTooShort');
    }
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('validation.passwordMismatch');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    // 显示通知权限弹窗
    setShowNotificationModal(true);
  };

  const handleNotificationPermissionConfirm = async (enabled: boolean) => {
    await proceedWithSignup(enabled);
  };

  const proceedWithSignup = async (notificationsEnabled: boolean) => {
    setIsLoading(true);
    setShowProgress(true);
    setMessage('');
    setErrors({});
    setProgress(0);

    try {
      // 步骤1: 验证用户类型
      setCurrentStep(t('signup.progress.step1'));
      setProgress(10);
      await new Promise(resolve => setTimeout(resolve, 300));

      if(formData.invitationCode == "ONENATA2025") {
        userType = UserType.ONENATA_ADMIN;
      } else if(formData.invitationCode.startsWith("ONBC-")) {
        userType = UserType.PETSTORE_OWNER;
      } else {
        userType = UserType.PETSTORE_STAFF;
      }

      // 步骤2: 检查邮箱是否已存在
      setCurrentStep(t('signup.progress.step2'));
      setProgress(25);
      await new Promise(resolve => setTimeout(resolve, 500));

      // 步骤3: 创建用户账户
      setCurrentStep(t('signup.progress.step3'));
      setProgress(40);
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const uid = await signUp(
        formData.email,
        formData.password,
        formData.firstName,
        formData.lastName,
        `${formData.firstName} ${formData.lastName}`,
        formData.invitationCode || undefined,
        formData.phoneNumber || undefined,
        userType,
        locale, // 传递当前 locale
        notificationsEnabled // 传递用户选择的通知权限
      );

      // 步骤4: 创建用户档案
      setCurrentStep(t('signup.progress.step4'));
      setProgress(65);
      await new Promise(resolve => setTimeout(resolve, 500));

      // 步骤5: 设置用户偏好
      setCurrentStep(t('signup.progress.step5'));
      setProgress(80);
      await new Promise(resolve => setTimeout(resolve, 300));

      console.log(`create user uid: ${uid}`);
      
      if(uid != null) {
        // 步骤6: 完成注册
        setCurrentStep(t('signup.progress.step6'));
        setProgress(100);
        await new Promise(resolve => setTimeout(resolve, 500));

        setMessage(t('signup.successMessage'));
        
        // 注册成功后跳转到verify-email page  
        setTimeout(() => {
          router.push(`/${locale}/auth/verify-email?uid=${uid}`);
        }, 1000);
      } else {
        setMessage(t('signup.errorGeneral'));
        setShowProgress(false);
      }

    } catch (error) {
      console.error('Signup error:', error);
      setShowProgress(false);
      
      if (error instanceof AuthError) {
        setMessage(error.message);
      } else {
        setMessage(t('signup.errorGeneral'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-[#FDECCE] via-[#F2D3A4] to-[#F2D3A4]">
      {/* 左侧内容区域 */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-lg w-full">
          {/* 主卡片 */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20">
            {/* 头部 */}
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-[#A126FF] to-[#C084FC] rounded-full mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {t('signup.createAccount')}
              </h1>
              <p className="text-[#C6C6C6] text-lg">
                {t('signup.startJourney')}
              </p>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
              {/* 姓名字段 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    {t('validation.firstName')} *
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={t('validation.firstNameRequired')}
                    required
                  />
                  {errors.firstName && (
                    <p className="text-sm text-red-500 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.firstName}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    {t('validation.lastName')} *
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className="w-full px-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={t('validation.lastNameRequired')}
                    required
                  />
                  {errors.lastName && (
                    <p className="text-sm text-red-500 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.lastName}
                    </p>
                  )}
                </div>
              </div>

              {/* 邮箱 */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  {t('validation.email')} *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={t('validation.email')}
                    required
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-500 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.email}
                  </p>
                )}
              </div>

              {/* 手机号 */}
              <PhoneNumberInput
                label={t('phoneNumber')}
                value={formData.phoneNumber}
                onChange={(value) => handleInputChange('phoneNumber', value)}
                placeholder={t('phoneNumberPlaceholder')}
                error={errors.phoneNumber}
                className="py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
              />

              {/* 密码字段 */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    {t('validation.password')} *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <input
                      type="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                      placeholder={t('validation.password')}
                      required
                    />
                  </div>
                  <p className="text-xs text-[#C6C6C6] mt-1">{t('validation.passwordLength')}</p>
                  {errors.password && (
                    <p className="text-sm text-red-500 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.password}
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    {t('validation.confirmPassword')} *
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <input
                      type="password"
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                      placeholder={t('validation.confirmPassword')}
                      required
                    />
                  </div>
                  {errors.confirmPassword && (
                    <p className="text-sm text-red-500 flex items-center">
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.confirmPassword}
                    </p>
                  )}
                </div>
              </div>

              {/* 邀请码 */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  {t('validation.invitationCode')}
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                    </svg>
                  </div>
                  <input
                    type="text"
                    required={true}
                    value={formData.invitationCode}
                    onChange={(e) => handleInputChange('invitationCode', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={t('validation.invitationCode')}
                  />
                </div>
                <p className="text-xs text-[#C6C6C6] mt-1">{t('validation.invitationCodeHint')}</p>
                {errors.invitationCode && (
                  <p className="text-sm text-red-500 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.invitationCode}
                  </p>
                )}
              </div>

              {/* 进度条 */}
              {showProgress && (
                <div className="space-y-4">
                  <div className="bg-gray-100 rounded-full h-3 overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-[#A126FF] to-[#C084FC] transition-all duration-500 ease-out"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-[#A126FF] font-medium">{currentStep}</span>
                    <span className="text-gray-500">{progress}%</span>
                  </div>
                </div>
              )}

              {/* 错误或成功消息 */}
              {message && (
                <div className={`p-4 rounded-xl flex items-center space-x-3 ${
                  message.includes(t('signup.success')) || message.includes('success') 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  {message.includes(t('signup.success')) || message.includes('success') ? (
                    <svg className="w-6 h-6 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  <span className={`text-sm font-medium ${
                    message.includes(t('signup.success')) || message.includes('success') 
                      ? 'text-green-700' 
                      : 'text-red-700'
                  }`}>
                    {message}
                  </span>
                </div>
              )}

              {/* 提交按钮 */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-4 px-6 rounded-xl bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] focus:scale-[0.98] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>{t('signup.creatingAccount')}</span>
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                    <span>{t('signup.createAccount')}</span>
                  </>
                )}
              </button>
            </form>

            {/* 登录链接 */}
            <div className="text-center mt-8 pt-6 border-t border-[#F2D3A4]">
              <p className="text-[#C6C6C6] text-sm">
                {t('signup.alreadyHaveAccount')} {' '}
                <button
                  onClick={() => router.push('/auth/login')}
                  className="font-semibold text-[#A126FF] hover:text-[#8A20D8] transition-colors duration-200"
                >
                  {t('signup.login')}
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧装饰区域 */}
      <div className="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center lg:p-12">
        <div className="text-center space-y-8">
          {/* 装饰图形 */}
          <div className="relative">
            <div className="w-64 h-64 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30">
              <div className="w-48 h-48 bg-gradient-to-br from-[#A126FF]/20 to-[#C084FC]/20 rounded-full flex items-center justify-center">
                <svg className="w-24 h-24 text-[#A126FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
            </div>
            {/* 浮动元素 */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-[#A126FF]/30 rounded-full animate-bounce"></div>
            <div className="absolute -bottom-6 -left-6 w-6 h-6 bg-[#C084FC]/40 rounded-full animate-pulse"></div>
          </div>
          
          {/* 文字内容 */}
          <div className="space-y-4">
            <h2 className="text-4xl font-bold text-white drop-shadow-lg">
              {t('signup.welcome')}
            </h2>
            <h3 className="text-2xl font-semibold text-white/90">
              {t('signup.platformName')}
            </h3>
            <p className="text-lg text-white/80 max-w-md mx-auto leading-relaxed">
              {t('signup.platformDescription')}
            </p>
          </div>

          {/* 特性列表 */}
          <div className="space-y-4 text-left max-w-sm">
            {[
              { icon: "📊", text: t('signup.feature1') },
              { icon: "🐕", text: t('signup.feature2') },
              { icon: "👥", text: t('signup.feature3') },
              { icon: "💰", text: t('signup.feature4') }
            ].map((feature, index) => (
              <div key={index} className="flex items-center space-x-3 text-white/90">
                <span className="text-2xl">{feature.icon}</span>
                <span className="font-medium">{feature.text}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 通知权限弹窗 */}
      <NotificationPermissionModal
        isOpen={showNotificationModal}
        onConfirm={handleNotificationPermissionConfirm}
        onClose={() => setShowNotificationModal(false)}
      />
    </div>
  );
} 