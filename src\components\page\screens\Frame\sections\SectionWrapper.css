.section-wrapper {
  background-color: #fdecce;
  height: 672px;
  left: 0;
  overflow: hidden;
  position: absolute;
  top: 87px;
  width: 1920px;
}

.section-wrapper .explore-the-pet-wrapper {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  left: 256px;
  position: absolute;
  top: 120px;
  width: 640px;
}

.section-wrapper .explore-the-pet {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 55px;
  font-weight: 700;
  letter-spacing: 0;
  line-height: 55px;
  margin-top: -1.00px;
  position: relative;
  width: fit-content;
}

.section-wrapper .with-onenata-s-map {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 84px;
  left: 256px;
  letter-spacing: 0.40px;
  line-height: 28px;
  position: absolute;
  top: 259px;
  width: 659px;
}

.section-wrapper .list {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 16px;
  left: 256px;
  position: absolute;
  top: 436px;
  width: 640px;
}

.section-wrapper .item {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 32px;
}

.section-wrapper .text-wrapper-2 {
  display: block;
  font-size: 24px;
  line-height: 1;
  margin: 0;
  padding: 0;
  color: #a025ff;
  font-family: "Material Icons", Helvetica;
  font-weight: 400;
  letter-spacing: 0;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.section-wrapper .p {
  display: block;
  font-size: 18px;
  line-height: 28px;
  margin: 0;
  padding: 0;
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-weight: 400;
  letter-spacing: 0;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.section-wrapper .ellipse {
  background: linear-gradient(
    141deg,
    rgba(161, 38, 255, 0.55) 0%,
    rgba(198, 198, 198, 0.55) 100%
  );
  border-radius: 336px;
  height: 672px;
  left: 1394px;
  position: absolute;
  top: -76px;
  width: 672px;
}
