'use client';

import { forwardRef, useState, useEffect } from 'react';
import { FiPhone } from 'react-icons/fi';
import { cn } from '../../../../../lib/utils';

export interface PhoneNumberInputProps {
  id?: string;
  label?: string;
  value: string;
  onChange: (value: string) => void;
  error?: string;
  helperText?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  showIcon?: boolean;
}

// 格式化加拿大电话号码为 +1 (xxx)-xxx-xxxx 格式
const formatCanadianPhoneNumber = (value: string): string => {
  // 移除所有非数字字符，但保留 +1
  const cleaned = value.replace(/[^\d+]/g, '');
  
  // 确保始终以 +1 开头
  let numbers = cleaned;
  if (!numbers.startsWith('+1')) {
    if (numbers.startsWith('1')) {
      numbers = '+' + numbers;
    } else {
      numbers = '+1' + numbers.replace(/^\+/, '');
    }
  }
  
  // 提取 +1 后面的数字
  const phoneDigits = numbers.substring(2);
  
  // 限制最多10位数字
  const limitedDigits = phoneDigits.substring(0, 10);
  
  // 格式化为 +1 (xxx)-xxx-xxxx
  if (limitedDigits.length === 0) {
    return '+1 ';
  } else if (limitedDigits.length <= 3) {
    return `+1 (${limitedDigits}`;
  } else if (limitedDigits.length <= 6) {
    return `+1 (${limitedDigits.substring(0, 3)})-${limitedDigits.substring(3)}`;
  } else {
    return `+1 (${limitedDigits.substring(0, 3)})-${limitedDigits.substring(3, 6)}-${limitedDigits.substring(6)}`;
  }
};

// 验证电话号码格式
export const validatePhoneNumber = (phoneNumber: string): boolean => {
  if (!phoneNumber || phoneNumber.trim() === '+1 ') {
    return true; // 空值认为是有效的（可选字段）
  }
  
  // 检查格式：+1 (xxx)-xxx-xxxx
  const phoneRegex = /^\+1 \(\d{3}\)-\d{3}-\d{4}$/;
  return phoneRegex.test(phoneNumber);
};

// 提取纯数字电话号码（去除格式）
export const getDigitsOnly = (phoneNumber: string): string => {
  if (!phoneNumber) return '';
  
  // 移除 +1 和所有非数字字符
  const digits = phoneNumber.replace(/[^\d]/g, '');
  return digits.startsWith('1') ? digits.substring(1) : digits;
};

const PhoneNumberInput = forwardRef<HTMLInputElement, PhoneNumberInputProps>(
  ({ 
    id,
    label, 
    value, 
    onChange, 
    error, 
    helperText, 
    placeholder = '+****************',
    disabled = false,
    required = false,
    className,
    showIcon = true,
    ...props 
  }, ref) => {
    const [internalValue, setInternalValue] = useState(value || '+1 ');

    // 同步外部值变化
    useEffect(() => {
      if (value !== internalValue) {
        setInternalValue(value || '+1 ');
      }
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value;
      
      // 如果用户删除了所有内容，重置为 +1 
      if (inputValue === '' || inputValue === '+') {
        const newValue = '+1 ';
        setInternalValue(newValue);
        onChange(newValue);
        return;
      }
      
      // 格式化电话号码
      const formattedValue = formatCanadianPhoneNumber(inputValue);
      setInternalValue(formattedValue);
      onChange(formattedValue);
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // 防止删除 +1 前缀
      if (e.key === 'Backspace' && internalValue.length <= 4) {
        e.preventDefault();
        const newValue = '+1 ';
        setInternalValue(newValue);
        onChange(newValue);
      }
    };

    return (
      <div className="space-y-1">
        {label && (
          <label htmlFor={id} className="block text-sm font-medium text-gray-700">
            {label}
            {required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
        <div className="relative">
          {showIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiPhone className="h-5 w-5 text-gray-400" />
            </div>
          )}
          <input
            ref={ref}
            id={id}
            type="tel"
            value={internalValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              'flex h-10 w-full rounded-md border border-gray-300 bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200',
              showIcon && 'pl-10',
              error && 'border-red-300 focus-visible:ring-red-500',
              className
            )}
            {...props}
          />
        </div>
        {error && (
          <p className="text-sm text-red-600 flex items-center">
            <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </p>
        )}
        {helperText && !error && (
          <p className="text-sm text-gray-500">{helperText}</p>
        )}
      </div>
    );
  }
);

PhoneNumberInput.displayName = 'PhoneNumberInput';

export { PhoneNumberInput }; 