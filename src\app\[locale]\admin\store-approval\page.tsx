/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../lib/firebase/context/AuthContext';
import { StoreListItem } from '../../../lib/services/store_services';
import { StoreVerifiedStatus } from '../../../lib/models/types';
import { 
  FiArrowLeft, 
  FiCheck, 
  FiX, 
  FiEye,
  FiClock,
  FiMapPin,
  FiPhone,
  FiMail,
  FiGlobe,
  FiSearch,
  FiFilter,
  FiRefreshCw
} from 'react-icons/fi';

const StoreApprovalPage = () => {
  const router = useRouter();
  const { user } = useAuth();
  
  const [stores, setStores] = useState<StoreListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [selectedStore, setSelectedStore] = useState<StoreListItem | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [action, setAction] = useState<'approve' | 'reject' | null>(null);
  const [reason, setReason] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    if (!user) {
      router.push('/auth/login');
      return;
    }

    // 检查管理员权限
    // TODO: 从用户数据中获取userType
    // if (user.userType !== UserType.ONENATA_ADMIN) {
    //   router.push('/dashboard');
    //   return;
    // }

    loadPendingStores();
  }, [user, router]);

  const loadPendingStores = async () => {
    try {
      setLoading(true);
      setError('');
      
      // 模拟获取待审核店铺数据
      // const result = await StoreService.getPendingStores(user?.uid || '');
      
      // 模拟数据
      const mockStores: StoreListItem[] = [
        {
          sid: 'store1',
          name: '宠物天堂',
          storeId: 'store1',
          currentAddress: {
            addressLine1: '123 Main Street',
            city: 'Toronto',
            province: 'ON',
            country: 'Canada',
            postCode: 'M5V 3A8'
          },
          phone: '+****************',
          email: '<EMAIL>',
          website: 'https://petparadise.com',
          businessType: 'PET_STORE' as any,
          description: '专业的宠物用品店，提供各种优质宠物食品、玩具和护理用品。',
          services: {
            grooming: true,
            boarding: true,
            veterinary: false,
            training: true,
            retail: true
          },
          avatarUrl: '',
          storePhotos: [],
          isValid: true,
          isSynced: true,
          createdBy: 'user1',
          updatedBy: 'user1',
          tags: [],
          accountInfo: {
            sid: 'store1',
            name: '宠物天堂',
            ownerId: 'user1',
            storeName: '宠物天堂',
            storeVerifiedStatus: StoreVerifiedStatus.PENDING,
            storeStatus: 'ACTIVE' as any,
            googlePlaceId: '',
            isValid: true,
            isSynced: true,
            createdBy: 'user1',
            updatedBy: 'user1',
            tags: []
          }
        },
        {
          sid: 'store2',
          name: '毛球诊所',
          storeId: 'store2',
          currentAddress: {
            addressLine1: '456 Pet Avenue',
            city: 'Vancouver',
            province: 'BC',
            country: 'Canada',
            postCode: 'V6B 1A1'
          },
          phone: '+****************',
          email: '<EMAIL>',
          businessType: 'VETERINARY_CLINIC' as any,
          description: '专业宠物医疗诊所，提供全面的宠物健康检查和治疗服务。',
          services: {
            grooming: false,
            boarding: false,
            veterinary: true,
            training: false,
            retail: false
          },
          avatarUrl: '',
          storePhotos: [],
          isValid: true,
          isSynced: true,
          createdBy: 'user2',
          updatedBy: 'user2',
          tags: [],
          accountInfo: {
            sid: 'store2',
            name: '毛球诊所',
            ownerId: 'user2',
            storeName: '毛球诊所',
            storeVerifiedStatus: StoreVerifiedStatus.PENDING,
            storeStatus: 'ACTIVE' as any,
            googlePlaceId: '',
            isValid: true,
            isSynced: true,
            createdBy: 'user2',
            updatedBy: 'user2',
            tags: []
          }
        }
      ];

      setStores(mockStores);
    } catch (err) {
      setError('加载待审核店铺失败');
      console.error('Error loading pending stores:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleApproval = async () => {
    if (!selectedStore || !action || !selectedStore.sid) return;

    try {
      setActionLoading(selectedStore.sid);
      
      // const decision = action === 'approve' ? StoreVerifiedStatus.APPROVED : StoreVerifiedStatus.REJECTED;
      
      // const result = await StoreService.approveStore(
      //   selectedStore.sid,
      //   user?.uid || '',
      //   decision,
      //   reason
      // );

      // 模拟成功
      const result = { success: true };

      if (result.success) {
        // 从列表中移除已处理的店铺
        setStores(prev => prev.filter(store => store.sid !== selectedStore.sid));
        setShowModal(false);
        setSelectedStore(null);
        setAction(null);
        setReason('');
      } else {
        setError('操作失败，请重试');
      }
    } catch (err) {
      setError('操作失败，请重试');
      console.error('Error approving store:', err);
    } finally {
      setActionLoading(null);
    }
  };

  const openApprovalModal = (store: StoreListItem, approvalAction: 'approve' | 'reject') => {
    setSelectedStore(store);
    setAction(approvalAction);
    setShowModal(true);
    setReason('');
  };

  const getBusinessTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      'ONLINE_STORE': '在线商店',
      'FAMILY_BASED_BUSINESS': '家庭企业',
      'RETAIL_BUSINESS': '零售企业',
      'COMMERCIAL_BUSINESS': '商业企业',
      'PET_STORE': '宠物店',
      'VETERINARY_CLINIC': '兽医诊所',
      'PET_GROOMING': '宠物美容',
      'PET_HOTEL': '宠物酒店',
      'PET_TRAINING': '宠物训练',
      'COMPREHENSIVE_SERVICE': '综合服务',
      'FRANCHISE': '连锁店',
      'MOBILE_SERVICE': '移动服务',
      'OTHER': '其他'
    };
    return types[type] || '其他';
  };

  const filteredStores = stores.filter(store =>
    store.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    store.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <FiArrowLeft className="w-5 h-5 mr-1" />
                返回
              </button>
              <h1 className="text-xl font-semibold text-gray-900">店铺审核</h1>
              <span className="ml-3 px-2 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">
                {filteredStores.length} 个待审核
              </span>
            </div>
            
            <button
              onClick={loadPendingStores}
              className="flex items-center px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              <FiRefreshCw className="w-4 h-4 mr-2" />
              刷新
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filter */}
        <div className="mb-6 flex items-center space-x-4">
          <div className="flex-1 relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="搜索店铺名称或邮箱..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <button className="flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
            <FiFilter className="w-4 h-4 mr-2" />
            筛选
          </button>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Stores List */}
        {filteredStores.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
            <FiClock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无待审核店铺</h3>
            <p className="text-gray-600">所有店铺都已处理完毕</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredStores.map((store) => (
              <div key={store.sid} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                {/* Store Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      {store.avatarUrl ? (
                        <img 
                          src={store.avatarUrl} 
                          alt={store.name}
                          className="w-12 h-12 rounded-full"
                        />
                      ) : (
                        <div className="w-12 h-12 rounded-full bg-purple-500 flex items-center justify-center">
                          <span className="text-white font-semibold">
                            {store.name?.charAt(0).toUpperCase() || 'S'}
                          </span>
                        </div>
                      )}
                      <div className="ml-3">
                        <h3 className="text-lg font-semibold text-gray-900">{store.name}</h3>
                        <p className="text-sm text-gray-600">{getBusinessTypeLabel(store.businessType)}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => router.push(`/store/${store.sid}`)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      >
                        <FiEye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  {store.description && (
                    <p className="mt-3 text-gray-700 text-sm line-clamp-2">{store.description}</p>
                  )}
                </div>

                {/* Store Details */}
                <div className="p-6 space-y-3">
                  <div className="flex items-center text-sm">
                    <FiMapPin className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-700">
                      {store.currentAddress.city}, {store.currentAddress.province}
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <FiPhone className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-700">{store.phone}</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <FiMail className="w-4 h-4 text-gray-400 mr-2" />
                    <span className="text-gray-700">{store.email}</span>
                  </div>
                  
                  {store.website && (
                    <div className="flex items-center text-sm">
                      <FiGlobe className="w-4 h-4 text-gray-400 mr-2" />
                      <a 
                        href={store.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-purple-600 hover:text-purple-700"
                      >
                        {store.website}
                      </a>
                    </div>
                  )}

                  {/* Services */}
                  <div className="pt-2">
                    <p className="text-sm font-medium text-gray-700 mb-2">提供服务:</p>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(store.services).map(([key, value]) => {
                        const serviceLabels: Record<string, string> = {
                          grooming: '美容',
                          boarding: '寄养',
                          veterinary: '兽医',
                          training: '训练',
                          retail: '零售'
                        };
                        
                        if (value) {
                          return (
                            <span
                              key={key}
                              className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"
                            >
                              {serviceLabels[key]}
                            </span>
                          );
                        }
                        return null;
                      })}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 flex justify-end space-x-3">
                  <button
                    onClick={() => openApprovalModal(store, 'reject')}
                    disabled={actionLoading === store.sid}
                    className="flex items-center px-4 py-2 text-red-700 bg-red-100 rounded-lg hover:bg-red-200 disabled:opacity-50 transition-colors"
                  >
                    <FiX className="w-4 h-4 mr-2" />
                    拒绝
                  </button>
                  <button
                    onClick={() => openApprovalModal(store, 'approve')}
                    disabled={actionLoading === store.sid}
                    className="flex items-center px-4 py-2 text-green-700 bg-green-100 rounded-lg hover:bg-green-200 disabled:opacity-50 transition-colors"
                  >
                    <FiCheck className="w-4 h-4 mr-2" />
                    通过
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Approval Modal */}
      {showModal && selectedStore && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={() => setShowModal(false)}></div>

            <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-xl">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  {action === 'approve' ? '通过审核' : '拒绝申请'}
                </h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="w-5 h-5" />
                </button>
              </div>

              <p className="text-gray-600 mb-4">
                确定要{action === 'approve' ? '通过' : '拒绝'}店铺 &quot;{selectedStore.name}&quot; 的申请吗？
              </p>

              {action === 'reject' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    拒绝原因 <span className="text-red-500">*</span>
                  </label>
                  <textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder="请输入拒绝原因..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  取消
                </button>
                <button
                  onClick={handleApproval}
                  disabled={actionLoading === selectedStore.sid || (action === 'reject' && !reason.trim())}
                  className={`flex items-center px-4 py-2 text-white rounded-lg disabled:opacity-50 ${
                    action === 'approve' 
                      ? 'bg-green-600 hover:bg-green-700' 
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {actionLoading === selectedStore.sid ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      处理中...
                    </>
                  ) : (
                    <>
                      {action === 'approve' ? <FiCheck className="w-4 h-4 mr-2" /> : <FiX className="w-4 h-4 mr-2" />}
                      确认{action === 'approve' ? '通过' : '拒绝'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StoreApprovalPage;
