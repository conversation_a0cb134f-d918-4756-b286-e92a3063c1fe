# OneNata Admin - 国际化 (i18n) 配置指南

## 概述

OneNata Admin 使用 `next-intl` 库实现国际化支持，目前支持英文 (en) 和中文简体 (zh-CN)。

## 配置文件

### 1. 路由配置 (`src/i18n/routing.ts`)

```typescript
import {defineRouting} from 'next-intl/routing';
 
export const routing = defineRouting({
  locales: ['en', 'zh-CN'],
  defaultLocale: 'en'
});
```

### 2. 请求配置 (`src/i18n/request.ts`)

```typescript
import {getRequestConfig} from 'next-intl/server';
import {hasLocale} from 'next-intl';
import {routing} from './routing';
 
export default getRequestConfig(async ({requestLocale}) => {
  const requested = await requestLocale;
  const locale = hasLocale(routing.locales, requested)
    ? requested
    : routing.defaultLocale;
 
  return {
    locale,
    messages: (await import(`../../messages/${locale}.json`)).default
  };
});
```

### 3. 导航配置 (`src/i18n/navigation.ts`)

```typescript
import {createNavigation} from 'next-intl/navigation';
import {routing} from './routing';
 
export const {Link, redirect, usePathname, useRouter, getPathname} =
  createNavigation(routing);
```

## 翻译文件

### 支持的语言

- **英文**: `messages/en.json`
- **中文简体**: `messages/zh-CN.json`

### 翻译文件结构

```json
{
  "HomePage": {
    "title": "标题",
    "description": "描述"
  },
  "auth-login": {
    "title": "登录",
    "email": "邮箱",
    "password": "密码"
  },
  "auth-signup": {
    "title": "注册",
    "firstName": "名字",
    "lastName": "姓氏"
  },
  "auth-verify-email": {
    "title": "验证邮箱",
    "message": "验证消息"
  },
  "auth-verify-phone": {
    "title": "验证手机",
    "code": "验证码"
  },
  "auth-profile-setup": {
    "title": "完善资料",
    "displayName": "显示名称",
    "bio": "个人简介"
  },
  "dashboard": {
    "welcome": "欢迎",
    "userInfo": "用户信息",
    "quickActions": "快速操作"
  },
  "validation": {
    "required": "必填",
    "emailInvalid": "邮箱格式错误"
  },
  "common": {
    "loading": "加载中",
    "save": "保存",
    "cancel": "取消"
  }
}
```

## 在组件中使用翻译

### 1. 导入 useTranslations Hook

```typescript
import { useTranslations } from 'next-intl';
```

### 2. 在组件中使用

```typescript
function MyComponent() {
  const t = useTranslations('auth-login');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}
```

### 3. 多个翻译命名空间

```typescript
function MyComponent() {
  const t = useTranslations('auth-login');
  const tCommon = useTranslations('common');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <button>{tCommon('save')}</button>
    </div>
  );
}
```

## 已完成的页面翻译

### ✅ 已完成
- **主页** (`/[locale]/page.tsx`) - 使用 `HomePage` 命名空间
- **登录页** (`/[locale]/auth/login/page.tsx`) - 使用 `auth-login` 命名空间
- **注册页** (`/[locale]/auth/signup/page.tsx`) - 使用 `auth-signup` 命名空间
- **邮箱验证页** (`/[locale]/auth/verify-email/page.tsx`) - 使用 `auth-verify-email` 命名空间
- **手机验证页** (`/[locale]/auth/verify-phone/page.tsx`) - 使用 `auth-verify-phone` 命名空间
- **个人资料设置页** (`/[locale]/auth/profile-setup/page.tsx`) - 使用 `auth-profile-setup` 命名空间
- **仪表板页** (`/[locale]/dashboard/page.tsx`) - 使用 `dashboard` 命名空间

### 🔧 组件翻译
- **通知权限模态框** (`NotificationPermissionModal.tsx`) - 使用 `auth-signup.notificationPermission` 命名空间
- **语言选择器** (`LanguageSelector.tsx`) - 使用 `common` 命名空间

## 路由结构

```
/[locale]/
├── page.tsx                    # 主页
├── auth/
│   ├── login/page.tsx         # 登录页
│   ├── signup/page.tsx        # 注册页
│   ├── verify-email/page.tsx  # 邮箱验证页
│   ├── verify-phone/page.tsx  # 手机验证页
│   └── profile-setup/page.tsx # 个人资料设置页
└── dashboard/page.tsx         # 仪表板页
```

## 用户认证流程

1. **注册** (`/[locale]/auth/signup`) → 
2. **邮箱验证** (`/[locale]/auth/verify-email`) → 
3. **个人资料设置** (`/[locale]/auth/profile-setup`) → 
4. **仪表板** (`/[locale]/dashboard`)

## 添加新翻译

### 1. 在翻译文件中添加新键

**messages/en.json**
```json
{
  "newSection": {
    "title": "New Title",
    "description": "New Description"
  }
}
```

**messages/zh-CN.json**
```json
{
  "newSection": {
    "title": "新标题",
    "description": "新描述"
  }
}
```

### 2. 在组件中使用

```typescript
const t = useTranslations('newSection');
return <h1>{t('title')}</h1>;
```

## 最佳实践

1. **命名空间组织**: 按页面或功能模块组织翻译命名空间
2. **一致性**: 保持翻译键名的一致性和语义化
3. **完整性**: 确保所有支持的语言都有对应的翻译
4. **验证**: 定期检查翻译文件的完整性和准确性

## 支持的语言代码

- `en`: 英文 (English)
- `zh-CN`: 中文简体 (Simplified Chinese)

## 扩展支持

要添加新语言支持:

1. 在 `src/i18n/routing.ts` 中添加新的语言代码
2. 在 `messages/` 目录下创建对应的 JSON 文件
3. 翻译所有必要的键值对
4. 测试新语言的显示效果

## 技术细节

- **框架**: Next.js 14+ with App Router
- **i18n 库**: next-intl
- **路由**: 基于 `[locale]` 动态路由
- **类型安全**: 支持 TypeScript 类型推断
- **SSR**: 服务器端渲染支持 