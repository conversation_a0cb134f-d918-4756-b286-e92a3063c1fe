'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from 'next/navigation';
import { resetPassword } from '../../../lib/firebase/services/auth';
import { AuthError } from '../../../lib/types/common';
import { useAuth } from '../../../lib/firebase/context/AuthContext';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface ForgotPasswordData {
  email: string;
}

export default function LoginPage() {
  const t = useTranslations('auth-login');
  const tForgot = useTranslations('auth-forgot-password');
  const tValidation = useTranslations('validation');
  const router = useRouter();
  const { signIn, setRememberMe, user, userData, loading } = useAuth();

  // 如果用户已登录，根据角色重定向到相应的仪表板
  useEffect(() => {
    if (!loading && user && userData) {
      // 跳转到通用仪表板路由，由路由器自动分发到相应的仪表板
      router.push('/dashboard');
    }
  }, [user, userData, loading, router]);
  
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    rememberMe: false,
  });
  
  const [forgotPasswordData, setForgotPasswordData] = useState<ForgotPasswordData>({
    email: '',
  });
  
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);
  const [forgotPasswordMessage, setForgotPasswordMessage] = useState('');

  const handleInputChange = (field: keyof LoginFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleForgotPasswordChange = (value: string) => {
    setForgotPasswordData({ email: value });
  };

  const validateLoginForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = tValidation('emailRequired');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = tValidation('emailInvalid');
    }
    if (!formData.password) {
      newErrors.password = tValidation('passwordRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateLoginForm()) return;

    setIsLoading(true);
    setMessage('');
    setErrors({});

    try {
      // 设置记住我选项
      setRememberMe(formData.rememberMe);
      
      await signIn(formData.email, formData.password);
      
      setMessage(t('successMessage'));
      
      // // 跳转到仪表板
      // setTimeout(() => {
      //   router.push('/dashboard');
      // }, 1000);

    } catch (error) {
      console.error('Login error:', error);
      
      if (error instanceof AuthError) {
        setMessage(error.message);
      } else {
        setMessage(t('errorGeneral'));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!forgotPasswordData.email.trim()) {
      setForgotPasswordMessage(tValidation('emailRequired'));
      return;
    }
    
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(forgotPasswordData.email)) {
      setForgotPasswordMessage(tValidation('emailInvalid'));
      return;
    }

    setForgotPasswordLoading(true);
    setForgotPasswordMessage('');

    try {
      await resetPassword(forgotPasswordData.email);
      setForgotPasswordMessage(tForgot('successMessage'));
      
      // 3秒后关闭模态框
      setTimeout(() => {
        setShowForgotPassword(false);
        setForgotPasswordMessage('');
        setForgotPasswordData({ email: '' });
      }, 3000);

    } catch (error) {
      console.error('Forgot password error:', error);
      
      if (error instanceof AuthError) {
        setForgotPasswordMessage(error.message);
      } else {
        setForgotPasswordMessage(tForgot('errorGeneral'));
      }
    } finally {
      setForgotPasswordLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-[#FDECCE] via-[#F2D3A4] to-[#F2D3A4]">
      {/* 左侧内容区域 */}
      <div className="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div className="max-w-lg w-full">
          {/* 主卡片 */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20">
            {/* 头部 */}
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-[#A126FF] to-[#C084FC] rounded-full mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                </svg>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {t('title')}
              </h1>
              <p className="text-[#C6C6C6] text-lg">
                {t('subtitle')}
              </p>
            </div>

            <form className="space-y-6" onSubmit={handleLogin}>
              {/* 邮箱 */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  {t('email')} *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={t('emailPlaceholder')}
                    required
                  />
                </div>
                {errors.email && (
                  <p className="text-sm text-red-500 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.email}
                  </p>
                )}
              </div>

              {/* 密码 */}
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  {t('password')} *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                  </div>
                  <input
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={t('passwordPlaceholder')}
                    required
                  />
                </div>
                {errors.password && (
                  <p className="text-sm text-red-500 flex items-center">
                    <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.password}
                  </p>
                )}
              </div>

              {/* 记住我和忘记密码 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    type="checkbox"
                    checked={formData.rememberMe}
                    onChange={(e) => handleInputChange('rememberMe', e.target.checked)}
                    className="h-4 w-4 text-[#A126FF] focus:ring-[#A126FF] border-[#C6C6C6] rounded"
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 font-medium">
                    {t('rememberMe')}
                  </label>
                </div>

                <button
                  type="button"
                  onClick={() => setShowForgotPassword(true)}
                  className="text-sm text-[#A126FF] hover:text-[#8A20D8] font-semibold transition-colors duration-200"
                >
                  {t('forgotPassword')}
                </button>
              </div>

              {/* 错误或成功消息 */}
              {message && (
                <div className={`p-4 rounded-xl flex items-center space-x-3 ${
                  message.includes('成功') || message.includes('success') 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  {message.includes('成功') || message.includes('success') ? (
                    <svg className="w-6 h-6 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  <span className={`text-sm font-medium ${
                    message.includes('成功') || message.includes('success') 
                      ? 'text-green-700' 
                      : 'text-red-700'
                  }`}>
                    {message}
                  </span>
                </div>
              )}

              {/* 登录按钮 */}
              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-4 px-6 rounded-xl bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-[1.02] focus:scale-[0.98] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>{t('loading')}</span>
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    <span>{t('submitButton')}</span>
                  </>
                )}
              </button>
            </form>

            {/* 注册链接 */}
            <div className="text-center mt-8 pt-6 border-t border-[#F2D3A4]">
              <p className="text-[#C6C6C6] text-sm">
                {t('noAccount')}{' '}
                <button
                  onClick={() => router.push('/auth/signup')}
                  className="font-semibold text-[#A126FF] hover:text-[#8A20D8] transition-colors duration-200"
                >
                  {t('signupLink')}
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 右侧装饰区域 */}
      <div className="hidden lg:flex lg:flex-1 lg:items-center lg:justify-center lg:p-12">
        <div className="text-center space-y-8">
          {/* 装饰图形 */}
          <div className="relative">
            <div className="w-64 h-64 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm border border-white/30">
              <div className="w-48 h-48 bg-gradient-to-br from-[#A126FF]/20 to-[#C084FC]/20 rounded-full flex items-center justify-center">
                <svg className="w-24 h-24 text-[#A126FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
            </div>
            {/* 浮动元素 */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-[#A126FF]/30 rounded-full animate-bounce"></div>
            <div className="absolute -bottom-6 -left-6 w-6 h-6 bg-[#C084FC]/40 rounded-full animate-pulse"></div>
          </div>
          
          {/* 文字内容 */}
          <div className="space-y-4">
            <h2 className="text-4xl font-bold text-white drop-shadow-lg">
              {t('welcomeTitle')}
            </h2>
            <p className="text-lg text-white/80 max-w-md mx-auto leading-relaxed">
              {t('welcomeDescription')}
            </p>
          </div>

          {/* 特性列表 */}
          <div className="space-y-4 text-left max-w-sm">
            {[
              { icon: "📊", text: t('features.analytics') },
              { icon: "🐕", text: t('features.pets') },
              { icon: "👥", text: t('features.customers') },
              { icon: "💰", text: t('features.finance') }
            ].map((feature, index) => (
              <div key={index} className="flex items-center space-x-3 text-white/90">
                <span className="text-2xl">{feature.icon}</span>
                <span className="font-medium">{feature.text}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 忘记密码模态框 */}
      {showForgotPassword && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-6 w-full max-w-md border border-white/20">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-[#A126FF] to-[#C084FC] rounded-full mb-3">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                {tForgot('title')}
              </h3>
              <p className="text-sm text-[#C6C6C6]">
                {tForgot('subtitle')}
              </p>
            </div>
            
            <form onSubmit={handleForgotPassword} className="space-y-4">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">
                  {tForgot('email')} *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-[#C6C6C6]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                    </svg>
                  </div>
                  <input
                    type="email"
                    value={forgotPasswordData.email}
                    onChange={(e) => handleForgotPasswordChange(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-[#F2D3A4] focus:border-[#A126FF] focus:ring-4 focus:ring-[#A126FF]/20 transition-all duration-200 bg-white/70 placeholder-[#C6C6C6]"
                    placeholder={tForgot('emailPlaceholder')}
                    required
                  />
                </div>
              </div>
              
              {forgotPasswordMessage && (
                <div className={`p-3 rounded-xl flex items-center space-x-2 ${
                  forgotPasswordMessage.includes('成功') || forgotPasswordMessage.includes('success')
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  {forgotPasswordMessage.includes('成功') || forgotPasswordMessage.includes('success') ? (
                    <svg className="w-5 h-5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  )}
                  <span className={`text-sm font-medium ${
                    forgotPasswordMessage.includes('成功') || forgotPasswordMessage.includes('success')
                      ? 'text-green-700' 
                      : 'text-red-700'
                  }`}>
                    {forgotPasswordMessage}
                  </span>
                </div>
              )}
              
              <div className="flex space-x-3 pt-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowForgotPassword(false);
                    setForgotPasswordMessage('');
                    setForgotPasswordData({ email: '' });
                  }}
                  className="flex-1 py-3 px-4 rounded-xl border-2 border-[#C6C6C6] text-[#C6C6C6] font-semibold hover:border-gray-400 hover:text-gray-600 transition-all duration-200"
                >
                  {tForgot('cancel')}
                </button>
                <button
                  type="submit"
                  disabled={forgotPasswordLoading}
                  className="flex-1 py-3 px-4 rounded-xl bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] focus:scale-[0.98] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-1"
                >
                  {forgotPasswordLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>{tForgot('loading')}</span>
                    </>
                  ) : (
                    <span>{tForgot('submit')}</span>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
} 