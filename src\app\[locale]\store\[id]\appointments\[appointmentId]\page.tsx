/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/app/[locale]/components/ui/Button';
import { AuthenticatedRoute } from '@/app/lib/firebase/context/AuthContext';
import appointmentService from '@/app/lib/services/appointment_service';
import { AppointmentStatus } from '@/app/lib/models/types';
import { useTranslations } from 'next-intl';
import { StoreHeader } from '@/app/[locale]/components/ui/StoreHeader';
import { 
  FiArrowLeft, 
  FiCalendar,
  FiClock,
  FiUser,
  FiHeart,
  // FiCheck,
  // FiX,
  FiAlertCircle,
  // FiEdit3
} from 'react-icons/fi';

export default function AppointmentDetailPage() {
  return (
    <AuthenticatedRoute>
      <AppointmentDetailContent />
    </AuthenticatedRoute>
  );
}

function AppointmentDetailContent() {
  const router = useRouter();
  const params = useParams();
  const t = useTranslations("AppointmentDetail");
  // const { userData } = useAuth();
  const storeId = params.id as string;
  const appointmentId = params.appointmentId as string;
  
  // State
  const [appointment, setAppointment] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Load appointment data
  const loadAppointmentData = async () => {
    try {
      setLoading(true);
      
      const result = await appointmentService.getAppointmentById(appointmentId);
      
      if (result.success && result.data) {
        setAppointment(result.data);
      } else {
        console.error('Appointment not found');
        router.push(`/store/${storeId}/appointments`);
      }
    } catch (error) {
      console.error('Error loading appointment data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get appointment status display
  const getAppointmentStatusDisplay = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.DRAFT:
        return { text: t('draft'), color: 'bg-gray-100 text-gray-800' };
      case AppointmentStatus.CONFIRMED:
        return { text: t('confirmed'), color: 'bg-blue-100 text-blue-800' };
      case AppointmentStatus.IN_PROGRESS:
        return { text: t('inProgress'), color: 'bg-yellow-100 text-yellow-800' };
      case AppointmentStatus.COMPLETED:
        return { text: t('completed'), color: 'bg-green-100 text-green-800' };
      case AppointmentStatus.CANCELLED:
        return { text: t('cancelled'), color: 'bg-red-100 text-red-800' };
      default:
        return { text: t('unknown'), color: 'bg-gray-100 text-gray-800' };
    }
  };

  // Load data on mount
  useEffect(() => {
    if (storeId && appointmentId) {
      loadAppointmentData();
    }
  }, [storeId, appointmentId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('appointmentDetail')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-slate-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-slate-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('appointmentDetail')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="text-slate-500 text-lg">{t('appointmentNotFound')}</div>
            <Button
              onClick={() => router.push(`/store/${storeId}/appointments`)}
              className="mt-4 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              {t('backToAppointments')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const statusDisplay = getAppointmentStatusDisplay(appointment.status);

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('appointmentDetail')} storeId={storeId} currentPage="appointments" />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push(`/store/${storeId}/appointments`)}
                className="bg-slate-100 hover:bg-slate-200 text-slate-700"
              >
                <FiArrowLeft className="w-4 h-4 mr-2" />
                {t('back')}
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                  <FiCalendar className="w-8 h-8 mr-3 text-violet-600" />
                  {t('appointmentDetail')}
                </h1>
                <p className="text-slate-600 font-medium mt-1">
                  {appointment.serviceInfo?.serviceName || t('unknownService')}
                </p>
              </div>
            </div>
            <div className="flex gap-3">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${statusDisplay.color}`}>
                {statusDisplay.text}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Appointment Information */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiCalendar className="w-5 h-5 mr-2 text-violet-600" />
              {t('appointmentInformation')}
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <FiCalendar className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-sm text-slate-500">{t('date')}</p>
                  <p className="text-slate-900">
                    {appointment.timeInfo?.date ? 
                      new Date(appointment.timeInfo.date).toLocaleDateString() : 
                      t('notSet')
                    }
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiClock className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-sm text-slate-500">{t('time')}</p>
                  <p className="text-slate-900">
                    {appointment.timeInfo?.startTime} - {appointment.timeInfo?.endTime}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiClock className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-sm text-slate-500">{t('duration')}</p>
                  <p className="text-slate-900">{appointment.timeInfo?.duration} {t('minutes')}</p>
                </div>
              </div>
              
              {appointment.notes && (
                <div className="flex items-start space-x-3">
                  <FiAlertCircle className="w-4 h-4 text-slate-400 mt-1" />
                  <div>
                    <p className="text-sm text-slate-500">{t('notes')}</p>
                    <p className="text-slate-900">{appointment.notes}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiHeart className="w-5 h-5 mr-2 text-violet-600" />
              {t('serviceInformation')}
            </h2>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-slate-500">{t('serviceName')}</p>
                <p className="text-slate-900 font-medium">{appointment.serviceInfo?.serviceName}</p>
              </div>
              
              <div>
                <p className="text-sm text-slate-500">{t('serviceCategory')}</p>
                <p className="text-slate-900">{appointment.serviceInfo?.serviceCategory}</p>
              </div>
              
              <div>
                <p className="text-sm text-slate-500">{t('price')}</p>
                <p className="text-slate-900 font-medium">
                  ${appointment.serviceInfo?.price} {appointment.serviceInfo?.currency}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          {/* Customer Information */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiUser className="w-5 h-5 mr-2 text-violet-600" />
              {t('customerInformation')}
            </h2>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-slate-500">{t('customerName')}</p>
                <p className="text-slate-900 font-medium">{appointment.customerInfo?.name}</p>
              </div>
              
              <div>
                <p className="text-sm text-slate-500">{t('email')}</p>
                <p className="text-slate-900">{appointment.customerInfo?.email || t('notSet')}</p>
              </div>
              
              <div>
                <p className="text-sm text-slate-500">{t('phoneNumber')}</p>
                <p className="text-slate-900">{appointment.customerInfo?.phoneNumber || t('notSet')}</p>
              </div>
            </div>
          </div>

          {/* Staff Information */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiUser className="w-5 h-5 mr-2 text-violet-600" />
              {t('staffInformation')}
            </h2>
            
            <div className="space-y-4">
              <div>
                <p className="text-sm text-slate-500">{t('staffName')}</p>
                <p className="text-slate-900 font-medium">{appointment.staffInfo?.staffName}</p>
              </div>
              
              <div>
                <p className="text-sm text-slate-500">{t('email')}</p>
                <p className="text-slate-900">{appointment.staffInfo?.staffEmail || t('notSet')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 