'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useAuth } from '../../../../lib/firebase/context/AuthContext';
import StoreService, { StoreListItem, CreateStoreData } from '../../../../lib/services/store_services';
import { BusinessType } from '../../../../lib/models/types';
import { AddressValidationResult, Address } from '../../../../lib/models/place';
import { 
  FiArrowLeft, 
  FiSave, 
  FiMapPin, 
  FiMail, 
  FiGlobe, 
  FiClock,
  FiAlertCircle,
  FiCheck,
  FiCheckCircle
} from 'react-icons/fi';
import StorePhotoUpload from '../../../components/ui/StorePhotoUpload';
import { PhoneNumberInput, validatePhoneNumber as validatePhoneFormat } from '../../../components/ui/PhoneNumberInput';
import { validateWebsite, normalizeWebsiteUrl } from '../../../../lib/utils/validations';
import SmartAddressSearch from '../../../components/ui/SmartAddressSearch';

const StoreEditPage = () => {
  const t = useTranslations('storeEdit');
  const tCommon = useTranslations('common');
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  
  const [store, setStore] = useState<StoreListItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasChanges, setHasChanges] = useState(false);

  // Address validation state - enhanced for smart address search
  const [addressValidationResult, setAddressValidationResult] = useState<AddressValidationResult | null>(null);
  const [isValidatingAddress, setIsValidatingAddress] = useState(false);
  const [addressValidated, setAddressValidated] = useState(false);
  const [originalAddress, setOriginalAddress] = useState<string>('');
  const [addressSource, setAddressSource] = useState<'google_place' | 'manual' | null>(null);
  const [useSmartAddressSearch, setUseSmartAddressSearch] = useState(false);

  const storeId = params.id as string;

  // 表单数据
  const [formData, setFormData] = useState<CreateStoreData>({
    storeName: '',
    businessType: BusinessType.OTHER,
    description: '',
    phone: '+1 ',
    email: '',
    website: '',
    currentAddress: {
      addressLine1: '',
      addressLine2: '',
      city: '',
      province: '',
      country: '',
      postCode: ''
    },
    services: {
      grooming: false,
      boarding: false,
      veterinary: false,
      training: false,
      retail: false
    },
    businessHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: false }
    },
    googlePlaceId: '',
    avatarUrl: '',
    storePhotos: [],
    // Store address validation result
    addressValidationResult: undefined
  });

  // 业务类型选项
  const businessTypes = [
    { value: BusinessType.ONLINE_STORE, label: t('businessTypes.online_store') },
    { value: BusinessType.FAMILY_BASED_BUSINESS, label: t('businessTypes.family_based_business') },
    { value: BusinessType.RETAIL_BUSINESS, label: t('businessTypes.retail_business') },
    { value: BusinessType.COMMERCIAL_BUSINESS, label: t('businessTypes.commercial_business') },
    { value: BusinessType.PET_STORE, label: t('businessTypes.pet_store') },
    { value: BusinessType.VETERINARY_CLINIC, label: t('businessTypes.veterinary_clinic') },
    { value: BusinessType.PET_GROOMING, label: t('businessTypes.pet_grooming') },
    { value: BusinessType.PET_HOTEL, label: t('businessTypes.pet_hotel') },
    { value: BusinessType.PET_TRAINING, label: t('businessTypes.pet_training') },
    { value: BusinessType.COMPREHENSIVE_SERVICE, label: t('businessTypes.comprehensive_service') },
    { value: BusinessType.FRANCHISE, label: t('businessTypes.franchise') },
    { value: BusinessType.MOBILE_SERVICE, label: t('businessTypes.mobile_service') },
    { value: BusinessType.OTHER, label: t('businessTypes.other') }
  ];

  // 服务选项
  const serviceOptions = [
    { key: 'grooming', label: t('services.grooming'), icon: '✂️' },
    { key: 'boarding', label: t('services.boarding'), icon: '🏠' },
    { key: 'veterinary', label: t('services.veterinary'), icon: '🩺' },
    { key: 'training', label: t('services.training'), icon: '🎓' },
    { key: 'retail', label: t('services.retail'), icon: '🛍️' }
  ];

  // 星期天数组
  const weekDays = [
    { key: 'monday', label: tCommon('weekDays.monday') },
    { key: 'tuesday', label: tCommon('weekDays.tuesday') },
    { key: 'wednesday', label: tCommon('weekDays.wednesday') },
    { key: 'thursday', label: tCommon('weekDays.thursday') },
    { key: 'friday', label: tCommon('weekDays.friday') },
    { key: 'saturday', label: tCommon('weekDays.saturday') },
    { key: 'sunday', label: tCommon('weekDays.sunday') }
  ];

  useEffect(() => {
    if (!user || !storeId) return;
    
    loadStoreDetails();
  }, [user, storeId]);

  const loadStoreDetails = async () => {
    try {
      setLoading(true);
      const result = await StoreService.getStoreDetails(storeId);
      
      if (result.success && result.data) {
        setStore(result.data);
        
        // 创建原始地址字符串用于比较
        const address = result.data.currentAddress;
        const addressString = [
          address?.addressLine1,
          address?.addressLine2,
          address?.city,
          address?.province,
          address?.country,
          address?.postCode
        ].filter(Boolean).join(', ');
        setOriginalAddress(addressString);
        
        // 如果有原始地址，标记为已验证（历史数据）
        if (addressString) {
          setAddressValidated(true);
          setAddressValidationResult({
            isValid: true,
            hasGooglePlace: !!result.data.accountInfo.googlePlaceId,
            googlePlaceId: result.data.accountInfo.googlePlaceId,
            message: 'Existing address (validated on creation)'
          });
        }
        
        // 填充表单数据
        setFormData({
          storeName: result.data.name || '',
          businessType: result.data.businessType,
          description: result.data.description || '',
          phone: result.data.phone || '',
          email: result.data.email || '',
          website: result.data.website || '',
          currentAddress: result.data.currentAddress,
          services: result.data.services,
          businessHours: result.data.businessHours || formData.businessHours,
          googlePlaceId: result.data.accountInfo.googlePlaceId || '',
          avatarUrl: result.data.avatarUrl || '',
          storePhotos: result.data.storePhotos || [],
          addressValidationResult: addressValidated && addressValidationResult ? addressValidationResult : undefined
        });
      } else {
        setError(result.error || t('error.loadFailed'));
      }
    } catch (err) {
      setError(t('error.loadErrorMessage'));
      console.error('Error loading store details:', err);
    } finally {
      setLoading(false);
    }
  };

  // Check if address has changed
  const hasAddressChanged = (): boolean => {
    const currentAddressString = [
      formData.currentAddress.addressLine1,
      formData.currentAddress.addressLine2,
      formData.currentAddress.city,
      formData.currentAddress.province,
      formData.currentAddress.country,
      formData.currentAddress.postCode
    ].filter(Boolean).join(', ');
    
    return currentAddressString !== originalAddress;
  };

  // Handle smart address search selection
  const handleSmartAddressSelection = (data: {
    address: Address;
    validationResult: AddressValidationResult;
    source: 'google_place' | 'manual';
  }) => {
    const { address, validationResult, source } = data;
    
    // Update form data with the new address
    setFormData(prev => ({
      ...prev,
      currentAddress: address,
      addressValidationResult: validationResult
    }));
    
    // Update address validation state
    setAddressValidationResult(validationResult);
    setAddressValidated(true);
    setAddressSource(source);
    setHasChanges(true);
    
    // Clear any previous address errors
    setErrors(prev => ({ ...prev, address: '' }));
  };

  // Handle smart address search errors
  const handleSmartAddressError = (error: string) => {
    setErrors(prev => ({ ...prev, address: error }));
  };

  // Toggle between smart search and manual entry
  const toggleAddressSearchMode = () => {
    setUseSmartAddressSearch(!useSmartAddressSearch);
    // Reset address validation when switching modes
    if (!useSmartAddressSearch) {
      setAddressValidated(false);
      setAddressValidationResult(null);
      setAddressSource(null);
    }
  };

  // Legacy validate address function for manual mode
  const validateAddress = async () => {
    const { addressLine1, city, province, country, postCode } = formData.currentAddress;
    
    // Check if required fields are filled
    if (!addressLine1 || !city || !province || !country || !postCode) {
      setErrors(prev => ({
        ...prev,
        address: t('validation.addressRequired')
      }));
      return;
    }

    setIsValidatingAddress(true);
    setErrors(prev => ({ ...prev, address: '' }));

    try {
      const result = await StoreService.validateStoreAddress(formData.currentAddress);
      
      setAddressValidationResult(result);
      setAddressValidated(true);
      setAddressSource('manual');
      
      // Store validation result in form data
      setFormData(prev => ({
        ...prev,
        addressValidationResult: result
      }));

      if (!result.isValid) {
        setErrors(prev => ({
          ...prev,
          address: result.error || t('validation.addressValidationFailed')
        }));
      }
    } catch (error) {
      console.error('Error validating address:', error);
      setErrors(prev => ({
        ...prev,
        address: t('validation.addressValidationError')
      }));
    } finally {
      setIsValidatingAddress(false);
    }
  };

  // 更新表单数据
  const updateFormData = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
    
    // 清除相关错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // 处理电话号码输入
  const handlePhoneChange = (value: string) => {
    // PhoneNumberInput 组件已经处理了格式化，直接使用
    updateFormData('phone', value);
    
    if (value && value.trim() !== '+1 ' && !validatePhoneFormat(value)) {
      setErrors(prev => ({
        ...prev,
        phone: tCommon('validation.phoneInvalid')
      }));
    } else {
      setErrors(prev => ({
        ...prev,
        phone: ''
      }));
    }
  };

  // 处理网站URL输入
  const handleWebsiteChange = (value: string) => {
    updateFormData('website', value);
    
    if (value && !validateWebsite(value)) {
      setErrors(prev => ({
        ...prev,
        website: tCommon('validation.websiteInvalid')
      }));
    } else {
      setErrors(prev => ({
        ...prev,
        website: ''
      }));
    }
  };

  // 处理网站URL失去焦点
  const handleWebsiteBlur = () => {
    if (formData.website) {
      const normalizedUrl = normalizeWebsiteUrl(formData.website);
      updateFormData('website', normalizedUrl);
    }
  };

  // 更新地址信息
  const updateAddress = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      currentAddress: {
        ...prev.currentAddress,
        [field]: value
      }
    }));
    setHasChanges(true);
    
    // Reset address validation when address changes
    if (hasAddressChanged()) {
      setAddressValidated(false);
      setAddressValidationResult(null);
      setFormData(prev => ({
        ...prev,
        addressValidationResult: undefined
      }));
    }
  };

  // 更新服务选择
  const updateServices = (service: keyof typeof formData.services, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      services: {
        ...prev.services,
        [service]: checked
      }
    }));
    setHasChanges(true);
  };

  // 更新营业时间
  const updateBusinessHours = (day: string, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      businessHours: {
        ...prev.businessHours,
        [day]: {
          ...prev.businessHours![day],
          [field]: value
        }
      }
    }));
    setHasChanges(true);
  };

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.storeName.trim()) {
      newErrors.storeName = t('validation.storeNameRequired');
    }
    if (!formData.businessType) {
      newErrors.businessType = t('validation.businessTypeRequired');
    }
    if (!formData.phone.trim() || formData.phone.trim() === '+1 ') {
      newErrors.phone = t('validation.phoneRequired');
    } else if (!validatePhoneFormat(formData.phone)) {
      newErrors.phone = tCommon('validation.phoneInvalid');
    }
    if (!formData.email.trim()) {
      newErrors.email = t('validation.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('validation.emailInvalid');
    }
    if (formData.website && !validateWebsite(formData.website)) {
      newErrors.website = tCommon('validation.websiteInvalid');
    }

    // Address validation check
    if (hasAddressChanged() && !addressValidated) {
      newErrors.address = t('validation.addressValidationError');
    } else if (addressValidationResult && !addressValidationResult.isValid) {
      newErrors.address = t('validation.addressValidationRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 保存更改
  const handleSave = async () => {
    if (!validateForm()) return;

    setSaving(true);
    try {
      const result = await StoreService.updateStore(storeId, formData);
      
      if (result.success) {
        setHasChanges(false);
        // 显示成功消息并返回详情页
        router.push(`/store/${storeId}`);
      } else {
        setError(result.error || t('error.saveFailed'));
      }
    } catch (err) {
      setError(t('error.saveErrorMessage'));
      console.error('Error updating store:', err);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('loading.loadingStore')}</p>
        </div>
      </div>
    );
  }

  if (error && !store) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiAlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">{t('error.loadFailed')}</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.back()}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
          >
            {tCommon('back')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-gray-900 mr-4"
              >
                <FiArrowLeft className="w-5 h-5 mr-1" />
                返回
              </button>
              <h1 className="text-xl font-semibold text-gray-900">{t('title')}</h1>
              {hasChanges && (
                <span className="ml-3 px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                  {t('status.unsavedChanges')}
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push(`/store/${storeId}`)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                {t('cancelButton')}
              </button>
              <button
                onClick={handleSave}
                disabled={saving || !hasChanges}
                className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <FiSave className="w-4 h-4 mr-2" />
                {saving ? t('saving') : t('saveButton')}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <FiAlertCircle className="w-5 h-5 text-red-400 mr-3 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-red-800">{t('error.saveFailed')}</h3>
                  <p className="text-sm text-red-700 mt-1">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Basic Information */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">{t('basicInfo')}</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('storeName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.storeName}
                  onChange={(e) => updateFormData('storeName', e.target.value)}
                  placeholder={t('storeNamePlaceholder')}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    errors.storeName ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.storeName && (
                  <p className="mt-1 text-sm text-red-600">{errors.storeName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('businessType')} <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.businessType}
                  onChange={(e) => updateFormData('businessType', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    errors.businessType ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  {businessTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {t(`businessTypes.${type.value}`)}
                    </option>
                  ))}
                </select>
                {errors.businessType && (
                  <p className="mt-1 text-sm text-red-600">{errors.businessType}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('description')}
                </label>
                <textarea
                  value={formData.description || ''}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder={t('descriptionPlaceholder')}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">{t('contactInfo')}</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <PhoneNumberInput
                label={`${t('phone')} *`}
                value={formData.phone}
                onChange={(value) => handlePhoneChange(value)}
                placeholder={t('phonePlaceholder')}
                error={errors.phone}
                required
                className="py-3 border-2 focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-200"
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiMail className="inline mr-1" />
                  {t('email')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  placeholder={t('emailPlaceholder')}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FiGlobe className="inline mr-1" />
                  {t('website')}
                </label>
                <input
                  type="url"
                  value={formData.website || ''}
                  onChange={(e) => handleWebsiteChange(e.target.value)}
                  onBlur={handleWebsiteBlur}
                  placeholder={t('websitePlaceholder')}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                    errors.website ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.website && (
                  <p className="mt-1 text-sm text-red-600">{errors.website}</p>
                )}
                {formData.website && validateWebsite(formData.website) && (
                  <p className="mt-1 text-sm text-green-600">
                    <FiCheck className="inline w-4 h-4 mr-1" />
                    {tCommon('validation.websiteValid')}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Smart Address Information with Search and Validation */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">
                <FiMapPin className="inline mr-2" />
                {t('addressInfo')}
              </h2>
              <button
                type="button"
                onClick={toggleAddressSearchMode}
                className="text-purple-600 hover:text-purple-700 font-medium"
              >
                {useSmartAddressSearch ? t('manualMode') : t('smartSearchMode')}
              </button>
            </div>

            {/* Smart Address Search Mode */}
            {useSmartAddressSearch ? (
              <SmartAddressSearch
                onAddressSelected={handleSmartAddressSelection}
                onError={handleSmartAddressError}
                currentAddress={formData.currentAddress}
                className="w-full"
              />
            ) : (
              /* Manual Address Entry Mode */
              <div className="space-y-4">
                {/* Address validation status */}
                {addressValidated && addressValidationResult && (
                  <div className={`mb-4 p-3 rounded-lg border ${
                    addressValidationResult.isValid 
                      ? 'bg-green-50 border-green-200 text-green-800' 
                      : 'bg-red-50 border-red-200 text-red-800'
                  }`}>
                    <div className="flex items-center">
                      {addressValidationResult.isValid ? (
                        <FiCheckCircle className="mr-2 h-5 w-5" />
                      ) : (
                        <FiAlertCircle className="mr-2 h-5 w-5" />
                      )}
                                        <div>
                    <p className="font-medium">
                      {addressValidationResult.isValid ? t('addressValidated') : t('addressValidationFailed')}
                    </p>
                    <p className="text-sm">
                      {addressValidationResult.message}
                    </p>
                    {addressValidationResult.isValid && (
                      <p className="text-sm">
                        {addressValidationResult.hasGooglePlace 
                          ? `✓ ${t('foundInGoogleMaps')}` 
                          : `○ ${t('customAddress')}`}
                        {addressSource && (
                          <span className="ml-2 text-xs">
                            ({t('status.addressSource', { source: addressSource === 'google_place' ? t('status.googleMaps') : t('status.manualEntry') })})
                          </span>
                        )}
                      </p>
                    )}
                  </div>
                    </div>
                  </div>
                )}
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('addressLine1')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.currentAddress.addressLine1}
                  onChange={(e) => updateAddress('addressLine1', e.target.value)}
                  placeholder={t('addressLine1Placeholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('addressLine2')}
                </label>
                <input
                  type="text"
                  value={formData.currentAddress.addressLine2 || ''}
                  onChange={(e) => updateAddress('addressLine2', e.target.value)}
                  placeholder={t('addressLine2Placeholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('city')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.currentAddress.city}
                  onChange={(e) => updateAddress('city', e.target.value)}
                  placeholder={t('cityPlaceholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('province')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.currentAddress.province}
                  onChange={(e) => updateAddress('province', e.target.value)}
                  placeholder={t('provincePlaceholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('country')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.currentAddress.country}
                  onChange={(e) => updateAddress('country', e.target.value)}
                  placeholder={t('countryPlaceholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {t('postCode')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.currentAddress.postCode}
                  onChange={(e) => updateAddress('postCode', e.target.value)}
                  placeholder={t('postCodePlaceholder')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
                </div>

                {/* Address validation button - only show if address has changed */}
                {hasAddressChanged() && (
                  <div className="mt-6">
                                    <button
                  type="button"
                  onClick={validateAddress}
                  disabled={isValidatingAddress || !formData.currentAddress.addressLine1 || !formData.currentAddress.city}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    isValidatingAddress || !formData.currentAddress.addressLine1 || !formData.currentAddress.city
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : addressValidated && addressValidationResult?.isValid
                        ? 'bg-green-600 text-white hover:bg-green-700'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  {isValidatingAddress ? (
                    <>
                      <div className="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      {t('validating')}
                    </>
                  ) : addressValidated && addressValidationResult?.isValid ? (
                    <>
                      <FiCheckCircle className="inline mr-2" />
                      {t('addressValidated')}
                    </>
                  ) : (
                    <>
                      <FiMapPin className="inline mr-2" />
                      {t('validateAddress')}
                    </>
                  )}
                </button>
                
                {errors.address && (
                  <p className="mt-2 text-sm text-red-600">{errors.address}</p>
                )}
                
                <p className="mt-2 text-xs text-gray-500">
                  {hasAddressChanged() ? 
                    t('addressChanged') :
                    t('addressValidationHint')
                  }
                </p>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Services */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">{t('servicesOffered')}</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {serviceOptions.map(service => (
                <div
                  key={service.key}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    formData.services[service.key as keyof typeof formData.services]
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => updateServices(
                    service.key as keyof typeof formData.services,
                    !formData.services[service.key as keyof typeof formData.services]
                  )}
                >
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.services[service.key as keyof typeof formData.services]}
                      onChange={(e) => updateServices(service.key as keyof typeof formData.services, e.target.checked)}
                      className="mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-2xl mr-2">{service.icon}</span>
                        <span className="font-medium text-gray-900">{t(`services.${service.key}`)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Business Hours */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">
              <FiClock className="inline mr-2" />
              {t('businessHours')}
            </h2>
            
            <div className="space-y-4">
              {weekDays.map(day => (
                <div key={day.key} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                  <div className="w-16 text-sm font-medium text-gray-700">
                    {t(`weekDays.${day.key}`)}
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={!formData.businessHours?.[day.key]?.closed}
                      onChange={(e) => updateBusinessHours(day.key, 'closed', !e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-600">{tCommon('open')}</span>
                  </div>
                  {!formData.businessHours?.[day.key]?.closed && (
                    <div className="flex items-center space-x-2">
                      <input
                        type="time"
                        value={formData.businessHours?.[day.key]?.open || '09:00'}
                        onChange={(e) => updateBusinessHours(day.key, 'open', e.target.value)}
                        className="px-2 py-1 border border-gray-300 rounded text-sm"
                      />
                      <span className="text-gray-500">-</span>
                      <input
                        type="time"
                        value={formData.businessHours?.[day.key]?.close || '18:00'}
                        onChange={(e) => updateBusinessHours(day.key, 'close', e.target.value)}
                        className="px-2 py-1 border border-gray-300 rounded text-sm"
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Photos */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">{t('storePhotos')}</h2>
            
            <StorePhotoUpload
              currentAvatarURL={formData.avatarUrl}
              currentPhotos={formData.storePhotos}
              onAvatarUploaded={(url) => updateFormData('avatarUrl', url)}
              onPhotosUploaded={(urls) => updateFormData('storePhotos', urls)}
              onError={(error) => setError(error)}
              maxPhotos={6}
              autoCompress={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StoreEditPage;
