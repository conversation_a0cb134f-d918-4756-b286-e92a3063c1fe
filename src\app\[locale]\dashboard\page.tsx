'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../lib/firebase/context/AuthContext';
import { UserType } from '../../lib/models/types';

export default function DashboardRouter() {
  const { user, userData, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user && userData) {
      // 根据用户角色重定向到相应的仪表板
      if (userData.userType === UserType.ONENATA_ADMIN) {
        router.replace('/dashboard-internal');
      } else if ([UserType.PETSTORE_OWNER, UserType.PETSTORE_ADMIN, UserType.PETSTORE_STAFF].includes(userData.userType)) {
        router.replace('/dashboard-business');
      } else {
        // 默认跳转到业务仪表板
        router.replace('/dashboard-business');
      }
    } else if (!loading && !user) {
      // 用户未登录，重定向到登录页面
      router.replace('/auth/login');
    }
    
  }, [user, userData, loading, router]);

  // 显示加载状态
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#A126FF] mx-auto"></div>
        <p className="mt-4 text-gray-600">loading...</p>
      </div>
    </div>
  );
} 