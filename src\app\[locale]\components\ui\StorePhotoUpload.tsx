'use client';

import React, { useState, useRef, useCallback } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { uploadStoreAvatar, uploadStorePhoto, validateImageFile, compressImage, UploadProgress } from "../../../lib/firebase/services";
import { FiUpload, FiX, FiCamera, FiPlus } from 'react-icons/fi';

interface StorePhotoUploadProps {
  placeId?: string; // 可选，创建时可能还没有storeId, 用uuid生成一个临时路径
  currentAvatarURL?: string;
  currentPhotos?: string[];
  onAvatarUploaded?: (photoURL: string) => void;
  onPhotosUploaded?: (photoURLs: string[]) => void;
  onError?: (error: string) => void;
  className?: string;
  maxPhotos?: number;
  autoCompress?: boolean;
}

export const StorePhotoUpload: React.FC<StorePhotoUploadProps> = ({
  placeId,
  currentAvatarURL,
  currentPhotos = [],
  onAvatarUploaded,
  onPhotosUploaded,
  onError,
  className = '',
  maxPhotos = 6,
  autoCompress = true,
}) => {
  const t = useTranslations('storeCreation');
  const avatarInputRef = useRef<HTMLInputElement>(null);
  const photosInputRef = useRef<HTMLInputElement>(null);
  
  const [avatarURL, setAvatarURL] = useState<string>(currentAvatarURL || '');
  const [photos, setPhotos] = useState<string[]>(currentPhotos);
  const [avatarProgress, setAvatarProgress] = useState<UploadProgress>({ progress: 0, isUploading: false });
  const [photosProgress, setPhotosProgress] = useState<UploadProgress>({ progress: 0, isUploading: false });
  const [isDragging, setIsDragging] = useState(false);

  // 同步外部传入的头像URL
  React.useEffect(() => {
    setAvatarURL(currentAvatarURL || '');
  }, [currentAvatarURL]);

  // 同步外部传入的照片数组
  React.useEffect(() => {
    setPhotos(currentPhotos);
  }, [currentPhotos]);

  // 处理头像上传
  const handleAvatarUpload = useCallback(async (file: File) => {
    // 验证文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      onError?.(validation.error || 'failed to validate file');
      return;
    }

    try {
      // 创建本地预览并立即显示
      const previewUrl = URL.createObjectURL(file);
      setAvatarURL(previewUrl);

      // 压缩图片（如果启用）
      let fileToUpload = file;
      if (autoCompress && file.size > 1024 * 1024) {
        fileToUpload = await compressImage(file, 400, 0.8);
      }

      // 如果有storeId，直接上传；否则上传到临时位置
      if (placeId) {
        const result = await uploadStoreAvatar(
          fileToUpload,
          placeId,
          (progress) => {
            setAvatarProgress(progress);
            if (progress.error) {
              onError?.(progress.error);
            }
          }
        );

        // 上传成功后，更新为服务器URL并清理本地预览
        setAvatarURL(result.url);
        onAvatarUploaded?.(result.url);
        URL.revokeObjectURL(previewUrl);
      } else {
        // 如果没有 storeId 和 userId，使用本地预览
        onAvatarUploaded?.(previewUrl);
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      onError?.(error instanceof Error ? error.message : '头像上传失败');
    }
  }, [placeId, autoCompress, onAvatarUploaded, onError]);

  // 处理多张照片上传
  const handlePhotosUpload = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    const remainingSlots = maxPhotos - photos.length;
    
    if (fileArray.length > remainingSlots) {
      onError?.(t('validation.tooManyPhotos', { max: maxPhotos }));
      return;
    }

    const newPhotos: string[] = [];
    
    try {
      setPhotosProgress({ progress: 0, isUploading: true });
      
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];
        
        // 验证文件
        const validation = validateImageFile(file);
        if (!validation.isValid) {
          onError?.(validation.error || 'failed to validate file');
          continue;
        }

        // 创建本地预览
        const previewUrl = URL.createObjectURL(file);
        
        // 压缩图片（如果启用）
        let fileToUpload = file;
        if (autoCompress && file.size > 1024 * 1024) {
          fileToUpload = await compressImage(file, 800, 0.8);
        }

        if (placeId) {
          const result = await uploadStorePhoto(
            fileToUpload,
            placeId,
            (progress) => {
              const overallProgress = ((i + progress.progress / 100) / fileArray.length) * 100;
              setPhotosProgress({ 
                progress: Math.round(overallProgress), 
                isUploading: true,
                error: progress.error 
              });
            }
          );
          
          newPhotos.push(result.url);
          URL.revokeObjectURL(previewUrl);
        } else {
          // 如果没有 storeId 和 userId，只存储预览URL
          newPhotos.push(previewUrl);
        }
      }

      const updatedPhotos = [...photos, ...newPhotos];
      setPhotos(updatedPhotos);
      onPhotosUploaded?.(updatedPhotos);
      setPhotosProgress({ progress: 100, isUploading: false });
      
    } catch (error) {
      console.error('Photos upload error:', error);
      onError?.(error instanceof Error ? error.message : '照片上传失败');
      setPhotosProgress({ progress: 0, isUploading: false });
    }
  }, [placeId, photos, maxPhotos, autoCompress, onPhotosUploaded, onError, t]);

  // 删除照片
  const removePhoto = (index: number) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    setPhotos(updatedPhotos);
    onPhotosUploaded?.(updatedPhotos);
  };

  // 处理文件输入变化
  const handleAvatarFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleAvatarUpload(file);
    }
  };

  const handlePhotosFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handlePhotosUpload(files);
    }
  };

  // 处理拖拽
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handlePhotosUpload(files);
    }
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 店铺头像上传 */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <FiCamera className="mr-2" />
          {t('avatar')}
        </h4>
        
        <div className="flex items-center space-x-6">
          {/* 头像预览 */}
          <div
            className="relative w-24 h-24 rounded-full overflow-hidden bg-gradient-to-br from-purple-500 to-purple-700 flex items-center justify-center text-white font-bold shadow-lg cursor-pointer transition-all duration-200 hover:scale-105"
            onClick={() => avatarInputRef.current?.click()}
            style={{ backgroundColor: '#A126FF' }}
          >
            {avatarURL ? (
              <Image
                src={avatarURL}
                alt="store avatar"
                fill
                className="object-cover"
                sizes="96px"
              />
            ) : (
              <FiCamera className="w-8 h-8" />
            )}
            
            {/* 上传覆盖层 */}
            <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 hover:opacity-100 transition-opacity duration-200">
                <FiUpload className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>

          {/* 上传按钮和进度 */}
          <div className="flex-1">
            <button
              type="button"
              onClick={() => avatarInputRef.current?.click()}
              disabled={avatarProgress.isUploading}
              className="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {avatarProgress.isUploading ? 'uploading...' : 'select avatar'}
            </button>
            
            {avatarProgress.isUploading && (
              <div className="mt-2">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-[#A126FF] to-[#8a20d8] h-2 rounded-full transition-all duration-300"
                    style={{ width: `${avatarProgress.progress}%` }}
                  />
                </div>
                <p className="text-sm text-gray-600 mt-1">{avatarProgress.progress}%</p>
              </div>
            )}
          </div>
        </div>

        <input
          ref={avatarInputRef}
          type="file"
          accept="image/*"
          onChange={handleAvatarFileChange}
          className="hidden"
        />
      </div>

      {/* 店铺照片上传 */}
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <FiUpload className="mr-2" />
          {t('storePhotos')} ({photos.length}/{maxPhotos})
        </h4>

        {/* 照片网格 */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
          {photos.map((photo, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <Image
                  src={photo}
                  alt={`store photo ${index + 1}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, 33vw"
                />
              </div>
              <button
                onClick={() => removePhoto(index)}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"
              >
                <FiX className="w-4 h-4" />
              </button>
            </div>
          ))}

          {/* 添加照片按钮 */}
          {photos.length < maxPhotos && (
            <div
              className={`aspect-square border-2 border-dashed rounded-lg flex items-center justify-center cursor-pointer transition-all duration-200 ${
                isDragging 
                  ? 'border-purple-500 bg-purple-50' 
                  : 'border-gray-300 hover:border-gray-400 bg-gray-50 hover:bg-gray-100'
              }`}
              onClick={() => photosInputRef.current?.click()}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
            >
              <div className="text-center">
                <FiPlus className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">add photo</p>
              </div>
            </div>
          )}
        </div>

        {/* 上传进度 */}
        {photosProgress.isUploading && (
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">uploading...</span>
              <span className="text-sm text-gray-600">{photosProgress.progress}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-[#A126FF] to-[#8a20d8] h-2 rounded-full transition-all duration-300"
                style={{ width: `${photosProgress.progress}%` }}
              />
            </div>
          </div>
        )}

        {/* 上传提示 */}
        <div className="text-center p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-600 mb-1">
            support JPEG, PNG, WebP, GIF format, single file size up to 5MB
          </p>
          <p className="text-xs text-gray-500">
            drag and drop files to the dashed box, or click to select files
          </p>
        </div>

        <input
          ref={photosInputRef}
          type="file"
          accept="image/*"
          multiple
          onChange={handlePhotosFileChange}
          className="hidden"
        />
      </div>
    </div>
  );
};

export default StorePhotoUpload; 