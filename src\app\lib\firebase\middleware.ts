import { NextRequest, NextResponse } from 'next/server';
import { adminAuth } from './admin';

export interface AuthenticatedRequest extends NextRequest {
  userId?: string;
  user?: {
    uid: string;
    email?: string;
    emailVerified: boolean;
  };
}

/**
 * Firebase 认证中间件
 */
export async function verifyFirebaseToken(request: NextRequest): Promise<{
  success: boolean;
  user?: {
    uid: string;
    email?: string;
    emailVerified: boolean;
  };
  error?: string;
}> {
  try {
    // 从 Authorization 头中获取令牌
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return {
        success: false,
        error: '未提供认证令牌'
      };
    }

    const token = authHeader.substring(7);
    
    // 验证 Firebase ID 令牌
    const decodedToken = await adminAuth.verifyIdToken(token);
    
    return {
      success: true,
      user: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        emailVerified: decodedToken.email_verified || false,
      }
    };
  } catch (error) {
    console.error('Firebase 令牌验证失败:', error);
    return {
      success: false,
      error: '无效的认证令牌'
    };
  }
}

/**
 * 创建需要认证的 API 路由包装器
 */
export function withAuth(handler: (request: AuthenticatedRequest) => Promise<NextResponse>) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const authResult = await verifyFirebaseToken(request);
    
    if (!authResult.success) {
      return NextResponse.json({
        success: false,
        error: authResult.error
      }, { status: 401 });
    }

    // 将用户信息附加到请求对象
    const authenticatedRequest = request as AuthenticatedRequest;
    authenticatedRequest.userId = authResult.user?.uid;
    authenticatedRequest.user = authResult.user;

    return handler(authenticatedRequest);
  };
}

/**
 * 创建可选认证的 API 路由包装器
 */
export function withOptionalAuth(handler: (request: AuthenticatedRequest) => Promise<NextResponse>) {
  return async (request: NextRequest): Promise<NextResponse> => {
    const authResult = await verifyFirebaseToken(request);
    
    // 将用户信息附加到请求对象（如果有的话）
    const authenticatedRequest = request as AuthenticatedRequest;
    if (authResult.success && authResult.user) {
      authenticatedRequest.userId = authResult.user.uid;
      authenticatedRequest.user = authResult.user;
    }

    return handler(authenticatedRequest);
  };
} 