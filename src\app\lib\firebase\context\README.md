# AuthContext 认证上下文

## 功能概述

AuthContext 提供了完整的用户认证管理功能，包括：

- 用户登录/登出
- 会话管理
- 自动登出机制
- 路由保护

## 自动登出机制

### 1. 关闭页面自动登出

在非"记住我"模式下，当用户关闭浏览器标签页或浏览器时，系统会自动清除用户的登录状态。

**实现原理：**
- 使用 `sessionStorage` 存储会话标记
- 监听 `beforeunload` 事件清除会话标记
- 页面重新加载时检查会话标记是否存在

### 2. 长时间不活跃自动登出

当用户在30分钟内没有任何操作时，系统会自动登出用户。

**监听的活动事件：**
- `mousedown` - 鼠标按下
- `mousemove` - 鼠标移动
- `keypress` - 键盘按键
- `scroll` - 页面滚动
- `touchstart` - 触摸开始
- `click` - 点击事件

**配置参数：**
```typescript
const INACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30分钟
const SESSION_CHECK_INTERVAL = 5 * 60 * 1000; // 5分钟检查间隔
```

### 3. 页面隐藏检测

当用户切换到其他标签页或最小化浏览器时，系统会记录隐藏时间。当页面重新可见时，如果隐藏时间超过30分钟，会自动登出用户。

## 使用方法

### 基本使用

```tsx
import { useAuth } from '@/app/lib/firebase/context/AuthContext';

function MyComponent() {
  const { user, userData, signIn, signOut, resetInactivityTimer } = useAuth();

  // 手动重置非活跃计时器（可选）
  const handleUserAction = () => {
    resetInactivityTimer();
  };

  return (
    <div>
      {user ? (
        <div>
          <p>欢迎，{userData?.name}</p>
          <button onClick={signOut}>登出</button>
        </div>
      ) : (
        <div>
          <p>请登录</p>
        </div>
      )}
    </div>
  );
}
```

### 路由保护

```tsx
import { ProtectedRoute, AdminRoute } from '@/app/lib/firebase/context/AuthContext';

// 需要完整认证的页面
function DashboardPage() {
  return (
    <ProtectedRoute>
      <div>Dashboard 内容</div>
    </ProtectedRoute>
  );
}

// 仅管理员可访问的页面
function AdminPage() {
  return (
    <AdminRoute>
      <div>管理员内容</div>
    </AdminRoute>
  );
}
```

### 记住我功能

```tsx
function LoginPage() {
  const { signIn, setRememberMe } = useAuth();
  const [rememberUser, setRememberUser] = useState(false);

  const handleLogin = async (email: string, password: string) => {
    setRememberMe(rememberUser);
    await signIn(email, password);
  };

  return (
    <form>
      {/* 登录表单 */}
      <label>
        <input
          type="checkbox"
          checked={rememberUser}
          onChange={(e) => setRememberUser(e.target.checked)}
        />
        记住我
      </label>
    </form>
  );
}
```

## 存储说明

### localStorage
- `remember_me`: 记住我状态
- `auth_user`: 用户基本信息（仅在记住我模式下）
- `last_activity`: 最后活动时间
- `page_hidden_time`: 页面隐藏时间

### sessionStorage
- `auth_session`: 会话标记（非记住我模式下）

## 注意事项

1. **记住我模式**：用户信息会持久化存储，不会自动登出
2. **非记住我模式**：会启用所有自动登出机制
3. **隐私保护**：所有敏感信息都会在登出时清除
4. **跨标签页同步**：使用 localStorage 实现跨标签页状态同步 