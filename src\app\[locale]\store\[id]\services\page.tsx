/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { StoreHeader } from '../../../components/ui/StoreHeader';
import { useAuth, AuthenticatedRoute } from '../../../../lib/firebase/context/AuthContext';
import { 
  ServiceCategory,
  ServiceBreed,
  StoreServiceStatus
} from '../../../../lib/models/types';
import { 
  StaffMember
} from '../../../../lib/services/staff_services';
import {
  StoreServiceData,
  CreateStoreServiceData
} from '../../../../lib/services/store_services';
import storeService from '../../../../lib/services/store_services';
import staffService from '../../../../lib/services/staff_services';
import { getUserStoreRole, UserStoreRole } from '../../../../lib/utils/permissions';
import { 
  FiPlus, 
  FiEdit3, 
  FiTrash2, 
  FiDollarSign,
  FiSettings,
  FiSearch,
  FiMoreVertical,
  FiEye,
  FiEyeOff,
  FiCalendar,
  FiUsers,
  FiCamera,
  FiCheck
} from 'react-icons/fi';
import { useRouter } from 'next/navigation';

interface ServiceWithProvider extends StoreServiceData {
  providerNames?: string[];
  providerEmails?: string[];
}

interface AddServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (serviceData: CreateStoreServiceData) => void;
  storeId: string;
  staff: StaffMember[];
}

function AddServiceModal({ isOpen, onClose, onSubmit, staff }: AddServiceModalProps) {
  const t = useTranslations('storeServices');
  // const { userData } = useAuth();
  const [formData, setFormData] = useState<CreateStoreServiceData>({
    serviceCategory: ServiceCategory.GROOMING,
    serviceBreed: ServiceBreed.DOG,
    description: '',
    staffIds: [],
    commission: 0.1, // 10% default commission
    isOnlineBookingEnabled: false,
    requiresApproval: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [availableStaff, setAvailableStaff] = useState<any[]>([]);
  const [isLoadingStaff, setIsLoadingStaff] = useState(false);

  // Load available staff for selected service category
  useEffect(() => {
    if (formData.serviceCategory) {
      loadAvailableStaff();
    } else {
      setAvailableStaff([]);
    }
  }, [formData.serviceCategory]);

  const loadAvailableStaff = async () => {
    setIsLoadingStaff(true);
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const staffWithServices: any[] = [];

      for (const staffMember of staff) {
        const servicesResult = await staffService.getStaffServices(staffMember);
        if (servicesResult.success && servicesResult.data) {
          // Check if staff has services in the selected category
          const hasCategoryService = servicesResult.data.some(
            (service: any) => service.serviceCategory === formData.serviceCategory
          );
          
          if (hasCategoryService) {
            staffWithServices.push({
              ...staffMember,
              services: servicesResult.data.filter(
                (service: any) => service.serviceCategory === formData.serviceCategory
              )
            });
          }
        }
      }

      setAvailableStaff(staffWithServices);
    } catch (error) {
      console.error('Error loading available staff:', error);
    } finally {
      setIsLoadingStaff(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (formData.staffIds.length === 0) {
      newErrors.staffIds = t('validation.providerRequired');
    }
    
    if (formData.commission < 0 || formData.commission > 1) {
      newErrors.commission = t('validation.commissionInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      await onSubmit(formData);
      onClose();
      setFormData({
        serviceCategory: ServiceCategory.GROOMING,
        serviceBreed: ServiceBreed.DOG,
        description: '',
        staffIds: [],
        commission: 0.1,
        isOnlineBookingEnabled: false,
        requiresApproval: false
      });
    } catch (error) {
      console.error('Add service error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-slate-900">{t('addService')}</h2>
            <button
              onClick={onClose}
              className="text-slate-400 hover:text-slate-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Service Category Selection */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('category')} *
                </label>
                <select
                  value={formData.serviceCategory}
                  onChange={(e) => setFormData({ ...formData, serviceCategory: e.target.value as ServiceCategory })}
                  className="w-full px-4 py-3 bg-white border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
                >
                  <option value={ServiceCategory.GROOMING}>{t('categories.grooming')}</option>
                  <option value={ServiceCategory.BOARDING}>{t('categories.boarding')}</option>
                  <option value={ServiceCategory.VETERINARY}>{t('categories.veterinary')}</option>
                  <option value={ServiceCategory.TRAINING}>{t('categories.training')}</option>
                  <option value={ServiceCategory.WASH}>{t('categories.wash')}</option>
                  <option value={ServiceCategory.OTHER}>{t('categories.other')}</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-semibold text-slate-800 mb-2">
                  {t('breed')}
                </label>
                <select
                  value={formData.serviceBreed}
                  onChange={(e) => setFormData({ ...formData, serviceBreed: e.target.value as ServiceBreed })}
                  className="w-full px-4 py-3 bg-white border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
                >
                  <option value={ServiceBreed.DOG}>{t('breeds.dog')}</option>
                  <option value={ServiceBreed.CAT}>{t('breeds.cat')}</option>
                  <option value={ServiceBreed.OTHER}>{t('breeds.other')}</option>
                </select>
              </div>
            </div>

            {/* Available Staff Selection */}
            <div>
              <label className="block text-sm font-semibold text-slate-800 mb-2">
                {t('availableStaff')} *
              </label>
              {isLoadingStaff ? (
                <div className="p-4 bg-slate-50 rounded-lg">
                  <p className="text-sm text-slate-600">{t('loadingAvailableStaff')}</p>
                </div>
              ) : availableStaff.length > 0 ? (
                <div className="space-y-2 max-h-32 overflow-y-auto border border-slate-200 rounded-lg p-3">
                  {availableStaff.map((staffMember) => (
                    <label key={staffMember.userData.uid} className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={formData.staffIds.includes(staffMember.userData.uid)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData({
                              ...formData,
                              staffIds: [...formData.staffIds, staffMember.userData.uid]
                            });
                          } else {
                            setFormData({
                              ...formData,
                              staffIds: formData.staffIds.filter(id => id !== staffMember.userData.uid)
                            });
                          }
                        }}
                        className="rounded border-slate-300 text-violet-600 focus:ring-violet-500"
                      />
                      <div className="flex-1">
                        <span className="text-sm text-slate-700 font-medium">
                          {staffMember.userData.displayName}
                        </span>
                        <div className="text-xs text-slate-500">
                          {staffMember.email && `${staffMember.email} • `}
                          {staffMember.services?.length || 0} {t('servicesInCategory')}
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              ) : (
                <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <p className="text-sm text-amber-700">
                    {t('noStaffForCategory')}
                  </p>
                </div>
              )}
              {errors.staffIds && <p className="mt-1 text-sm text-red-600">{errors.staffIds}</p>}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-semibold text-slate-800 mb-2">
                {t('description')}
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder={t('descriptionPlaceholder')}
                rows={3}
                className="w-full px-4 py-3 bg-white border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
              />
            </div>

            {/* Commission */}
            <div>
              <label className="block text-sm font-semibold text-slate-800 mb-2">
                {t('commission')} (%) *
              </label>
              <Input
                type="number"
                value={formData.commission * 100}
                onChange={(e) => setFormData({ ...formData, commission: (parseFloat(e.target.value) || 0) / 100 })}
                placeholder="10"
                min="0"
                max="100"
                step="0.1"
                error={errors.commission}
              />
              <p className="mt-1 text-xs text-slate-500">
                {t('commissionDescription')}
              </p>
            </div>

            {/* Online Booking */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.isOnlineBookingEnabled}
                onChange={(e) => setFormData({ ...formData, isOnlineBookingEnabled: e.target.checked })}
                className="rounded border-slate-300 text-violet-600 focus:ring-violet-500"
              />
              <label className="text-sm text-slate-700">
                {t('enableOnlineBooking')}
              </label>
            </div>

            {/* Requires Approval */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={formData.requiresApproval}
                onChange={(e) => setFormData({ ...formData, requiresApproval: e.target.checked })}
                className="rounded border-slate-300 text-violet-600 focus:ring-violet-500"
              />
              <label className="text-sm text-slate-700">
                {t('requiresApproval')}
              </label>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                type="button"
                onClick={onClose}
                variant="secondary"
                className="flex-1"
              >
                {t('cancel')}
              </Button>
              <Button
                type="submit"
                disabled={isLoading || availableStaff.length === 0}
                className="flex-1 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
              >
                {isLoading ? t('creating') : t('createService')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

interface ServiceCardProps {
  service: ServiceWithProvider;
  onEdit: (service: ServiceWithProvider) => void;
  onDelete: (serviceId: string) => void;
  onToggleActive: (serviceId: string, active: boolean) => void;
}

function ServiceCard({ service, onEdit, onDelete, onToggleActive }: ServiceCardProps) {
  const t = useTranslations('storeServices');
  const params = useParams();
  const router = useRouter();
  const [showActions, setShowActions] = useState(false);

  const getCategoryColor = (category: ServiceCategory) => {
    switch (category) {
      case ServiceCategory.GROOMING:
        return 'bg-blue-100 text-blue-800';
      case ServiceCategory.VETERINARY:
        return 'bg-red-100 text-red-800';
      case ServiceCategory.BOARDING:
        return 'bg-green-100 text-green-800';
      case ServiceCategory.TRAINING:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryName = (category: ServiceCategory) => {
    switch (category) {
      case ServiceCategory.GROOMING:
        return t('categories.grooming');
      case ServiceCategory.VETERINARY:
        return t('categories.veterinary');
      case ServiceCategory.BOARDING:
        return t('categories.boarding');
      case ServiceCategory.TRAINING:
        return t('categories.training');
      case ServiceCategory.WASH:
        return t('categories.wash');
      default:
        return t('categories.other');
    }
  };

  const formatCommission = (commission: number) => {
    return `${(commission * 100).toFixed(1)}%`;
  };

  const handleCardClick = () => {
    router.push(`/store/${params.id}/services/${service.sid}`);
  };

  return (
    <div 
      className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleCardClick}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-slate-900 mb-2">
            {getCategoryName(service.serviceCategory)}
          </h3>
          <div className="flex items-center space-x-2 mb-3">
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(service.serviceCategory)}`}>
              {getCategoryName(service.serviceCategory)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-100 text-slate-800">
              {service.serviceBreed}
            </span>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              service.status === StoreServiceStatus.ACTIVE 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {service.status === StoreServiceStatus.ACTIVE ? t('active') : t('inactive')}
            </span>
          </div>
        </div>

        <div className="relative">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowActions(!showActions);
            }}
            className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
          >
            <FiMoreVertical className="w-5 h-5 text-slate-500" />
          </button>

          {showActions && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200 z-10">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(service);
                  setShowActions(false);
                }}
                className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center"
              >
                <FiEdit3 className="w-4 h-4 mr-2" />
                {t('edit')}
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onToggleActive(service.sid || '', service.status !== StoreServiceStatus.ACTIVE);
                  setShowActions(false);
                }}
                className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center"
              >
                {service.status === StoreServiceStatus.ACTIVE ? (
                  <>
                    <FiEyeOff className="w-4 h-4 mr-2" />
                    {t('deactivate')}
                  </>
                ) : (
                  <>
                    <FiEye className="w-4 h-4 mr-2" />
                    {t('activate')}
                  </>
                )}
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(service.sid || '');
                  setShowActions(false);
                }}
                className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center"
              >
                <FiTrash2 className="w-4 h-4 mr-2" />
                {t('delete')}
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm text-slate-600">
            <FiDollarSign className="w-4 h-4 mr-2" />
            {t('commission')}: {formatCommission(service.commission)}
          </div>
          <div className="flex items-center text-sm text-slate-600">
            <FiCalendar className="w-4 h-4 mr-2" />
            {t('totalBookings')}: {service.totalBookings || 0}
          </div>
        </div>

        {service.providerNames && service.providerNames.length > 0 && (
          <div className="flex items-center text-sm text-slate-600">
            <FiUsers className="w-4 h-4 mr-2" />
            {t('providers')}: {service.providerNames.join(', ')}
          </div>
        )}

        <div className="flex items-center text-sm text-slate-600">
          <FiCheck className="w-4 h-4 mr-2" />
          {t('completedCount')}: {service.completedBookings || 0}
        </div>

        {service.isOnlineBookingEnabled && (
          <div className="flex items-center text-sm text-green-600">
            <FiCalendar className="w-4 h-4 mr-2" />
            {t('onlineBookingEnabled')}
          </div>
        )}

        {service.servicePhotos && service.servicePhotos.length > 0 && (
          <div className="flex items-center text-sm text-slate-600">
            <FiCamera className="w-4 h-4 mr-2" />
            {service.servicePhotos.length} {t('photos')}
          </div>
        )}

        {service.description && (
          <div className="text-sm text-slate-600">
            <p className="line-clamp-2">{service.description}</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default function StoreServicesPage() {
  return (
    <AuthenticatedRoute>
      <StoreServicesContent />
    </AuthenticatedRoute>
  );
}

function StoreServicesContent() {
  const params = useParams();
  const { userData } = useAuth();
  const t = useTranslations('storeServices');
  const storeId = params.id as string;
  
  // 店铺名称
  const storeName = t('title');

  const [services, setServices] = useState<ServiceWithProvider[]>([]);
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<ServiceCategory | 'all'>('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [userStoreRole, setUserStoreRole] = useState<UserStoreRole | null>(null);

  // Helper function to get category name
  const getCategoryName = (category: ServiceCategory) => {
    switch (category) {
      case ServiceCategory.GROOMING:
        return t('categories.grooming');
      case ServiceCategory.VETERINARY:
        return t('categories.veterinary');
      case ServiceCategory.BOARDING:
        return t('categories.boarding');
      case ServiceCategory.TRAINING:
        return t('categories.training');
      case ServiceCategory.WASH:
        return t('categories.wash');
      default:
        return t('categories.other');
    }
  };

  // 加载店铺员工和服务列表
  useEffect(() => {
    loadData();
  }, [storeId]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // 获取用户角色
      if (userData?.uid) {
        const role = await getUserStoreRole(userData.uid, storeId);
        setUserStoreRole(role);
      }
      
      await Promise.all([loadStaff(), loadServices()]);
    } catch (error) {
      console.error('Load data error:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStaff = async () => {
    try {
      const staffResult = await staffService.getStoreStaff(storeId);
      if (staffResult.success && staffResult.data) {
        // 如果是staff，只显示自己
        if (userStoreRole?.isStaff && userData?.uid) {
          const currentStaff = staffResult.data.find(s => s.userData.uid === userData.uid);
          setStaff(currentStaff ? [currentStaff] : []);
        } else {
          setStaff(staffResult.data);
        }
      } else {
        console.error('Load staff error:', staffResult.error);
      }
    } catch (error) {
      console.error('Load staff error:', error);
    }
  };

  const loadServices = async () => {
    try {
      const servicesResult = await storeService.getStoreServices(storeId);
      
      if (servicesResult.success && servicesResult.data) {
        let filteredServices = servicesResult.data;
        
        // 如果是staff，只显示自己参与的服务
        if (userStoreRole?.isStaff && userData?.uid) {
          filteredServices = servicesResult.data.filter(service => 
            service.staffIds.includes(userData.uid)
          );
        }
        
        // Add provider names to services
        const servicesWithProvider = filteredServices.map(service => {
          const providerNames: string[] = [];
          const providerEmails: string[] = [];
          
          // Get provider names from staffIds
          service.staffIds.forEach(staffId => {
            const staffMember = staff.find(s => s.userData.uid === staffId);
            if (staffMember) {
              providerNames.push(staffMember.userData.displayName || `${staffMember.userData.firstName} ${staffMember.userData.lastName}`);
              if (staffMember.email) {
                providerEmails.push(staffMember.email);
              }
            }
          });
          
          return {
            ...service,
            providerNames,
            providerEmails
          };
        });
        
        setServices(servicesWithProvider);
      }
    } catch (error) {
      console.error('Load services error:', error);
    }
  };

  const handleAddService = async (serviceData: CreateStoreServiceData) => {
    try {
      const result = await storeService.createStoreService(
        storeId,
        serviceData,
        userData?.uid || ''
      );

      if (result.success) {
        await loadServices(); // Reload services list
      } else {
        console.error('Add service error:', result.error);
      }
    } catch (error) {
      console.error('Add service error:', error);
    }
  };

  const handleEditService = (service: ServiceWithProvider) => {
    // TODO: Implement edit functionality
    console.log('Edit service:', service);
  };

  const handleDeleteService = async (serviceId: string) => {
    if (window.confirm(t('confirmDelete'))) {
      try {
        const result = await storeService.deleteStoreService(storeId, serviceId);

        if (result.success) {
          await loadServices(); // Reload services list
        } else {
          console.error('Delete service error:', result.error);
        }
      } catch (error) {
        console.error('Delete service error:', error);
      }
    }
  };

  const handleToggleActive = async (serviceId: string, active: boolean) => {
    try {
      const result = await storeService.toggleStoreServiceStatus(
        storeId,
        serviceId,
        active,
        userData?.uid || ''
      );

      if (result.success) {
        await loadServices(); // Reload services list
      } else {
        console.error('Toggle service active error:', result.error);
      }
    } catch (error) {
      console.error('Toggle service active error:', error);
    }
  };

  const filteredServices = services.filter(service => {
    const matchesSearch = 
      getCategoryName(service.serviceCategory).toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.providerNames?.some(name => name.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = categoryFilter === 'all' || service.serviceCategory === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const activeServicesCount = services.filter(s => s.status === StoreServiceStatus.ACTIVE).length;

  // 检查是否可以添加服务
  const canAddService = userStoreRole?.isOwner || userStoreRole?.isAdmin || false;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={storeName} storeId={storeId} currentPage="services" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-slate-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-slate-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={storeName} storeId={storeId} currentPage="services" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 头部 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                <FiSettings className="w-8 h-8 mr-3 text-violet-600" />
                {t('title')}
              </h1>
              <p className="text-slate-600 font-medium mt-1">
                {t('subtitle', { total: services.length, active: activeServicesCount })}
              </p>
            </div>
            <Button
              onClick={() => setShowAddModal(true)}
              className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 flex items-center"
              disabled={staff.length === 0 || !canAddService}
            >
              <FiPlus className="w-5 h-5 mr-2" />
              {t('addService')}
            </Button>
          </div>
          {staff.length === 0 && (
            <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <p className="text-amber-700 text-sm">
                {t('noStaffWarning')}
              </p>
            </div>
          )}
          {!canAddService && userStoreRole?.isStaff && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-blue-700 text-sm">
                {t('staffPermissionNote')}
              </p>
            </div>
          )}
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5" />
                <Input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={t('searchPlaceholder')}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="md:w-48">
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value as ServiceCategory | 'all')}
                className="w-full px-4 py-3 bg-white border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
              >
                <option value="all">{t('allCategories')}</option>
                <option value={ServiceCategory.GROOMING}>{t('categories.grooming')}</option>
                <option value={ServiceCategory.VETERINARY}>{t('categories.veterinary')}</option>
                <option value={ServiceCategory.BOARDING}>{t('categories.boarding')}</option>
                <option value={ServiceCategory.TRAINING}>{t('categories.training')}</option>
                <option value={ServiceCategory.WASH}>{t('categories.wash')}</option>
                <option value={ServiceCategory.OTHER}>{t('categories.other')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* 服务列表 */}
        {filteredServices.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredServices.map((service) => (
              <ServiceCard
                key={service.sid + service.serviceCategory}
                service={service}
                onEdit={handleEditService}
                onDelete={handleDeleteService}
                onToggleActive={handleToggleActive}
              />
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-2xl shadow-lg p-12 text-center">
            <FiSettings className="w-16 h-16 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              {searchTerm || categoryFilter !== 'all' ? t('noSearchResults') : t('noServices')}
            </h3>
            <p className="text-slate-600 mb-6">
              {searchTerm || categoryFilter !== 'all' 
                ? t('noSearchResultsDesc') 
                : t('noServicesDesc')
              }
            </p>
            {!searchTerm && categoryFilter === 'all' && staff.length > 0 && (
              <Button
                onClick={() => setShowAddModal(true)}
                className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
              >
                <FiPlus className="w-5 h-5 mr-2" />
                {t('addFirstService')}
              </Button>
            )}
          </div>
        )}

        {/* 添加服务模态框 */}
        <AddServiceModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSubmit={handleAddService}
          storeId={storeId}
          staff={staff}
        />
      </div>
    </div>
  );
} 
