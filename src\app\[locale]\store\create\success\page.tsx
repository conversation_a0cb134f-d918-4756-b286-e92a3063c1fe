'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { FiCheckCircle, FiArrowRight } from 'react-icons/fi';
import Link from 'next/link';

const StoreCreateSuccessPage = () => {
  const t = useTranslations('storeCreation');
  const router = useRouter();

  useEffect(() => {
    // 5秒后自动跳转到dashboard
    const timer = setTimeout(() => {
      router.push('/dashboard');
    }, 5000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FDECCE] via-[#F2D3A4] to-[#F2D3A4] flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20 text-center">
          {/* 成功图标 */}
          <div className="mb-6">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FiCheckCircle className="w-10 h-10 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {t('successPage.title')}
            </h1>
            <p className="text-gray-600">
              {t('successPage.subtitle')}
            </p>
          </div>

          {/* 详细信息 */}
          <div className="bg-gray-50 rounded-2xl p-6 mb-6">
            <div className="text-left space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{t('successPage.status')}</span>
                <span className="px-3 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                  {t('successPage.pendingApproval')}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{t('successPage.estimatedReviewTime')}</span>
                <span className="text-sm text-gray-600">{t('successPage.reviewTimeValue')}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">{t('successPage.notificationMethod')}</span>
                <span className="text-sm text-gray-600">{t('successPage.emailNotification')}</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Link
              href="/dashboard"
              className="w-full flex items-center justify-center px-6 py-3 bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white font-semibold rounded-xl hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200"
            >
              <span>{t('successPage.backToDashboard')}</span>
              <FiArrowRight className="ml-2 w-4 h-4" />
            </Link>
            
            <Link
              href="/store/create"
              className="w-full flex items-center justify-center px-6 py-3 bg-gray-100 text-gray-700 font-medium rounded-xl hover:bg-gray-200 transition-colors"
            >
              {t('successPage.createAnotherStore')}
            </Link>
          </div>

          {/* 自动跳转提示 */}
          <p className="text-xs text-gray-500 mt-4">
            {t('successPage.autoRedirect')}
          </p>
        </div>
      </div>
    </div>
  );
};

export default StoreCreateSuccessPage; 