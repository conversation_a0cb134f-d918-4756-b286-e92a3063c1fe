.frame {
  background-color: #ffffff;
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
}

.frame .div-2 {
  background-color: #ffffff;
  height: 5601px;
  overflow: hidden;
  position: relative;
  width: 1920px;
}

.frame .overlap-6 {
  border-radius: 0px 0px 200px 0px;
  height: 940px;
  left: 0;
  position: absolute;
  top: 0;
  width: 1920px;
}

.frame .toy {
  height: 93px;
  left: 1850px;
  position: absolute;
  top: 264px;
  width: 70px;
}

.frame .overlap-7 {
  height: 762px;
  left: 0;
  position: absolute;
  top: 1688px;
  width: 1920px;
}

.frame .black-2 {
  height: 762px;
  left: 1189px;
  position: absolute;
  top: 0;
  width: 589px;
}

.frame .overlap-8 {
  height: 762px;
  position: relative;
}

.frame .element-2 {
  background-image: url(https://c.animaapp.com/9exExmER/img/iphone-16.png);
  background-size: 100% 100%;
  height: 523px;
  left: 0;
  position: absolute;
  top: 0;
  width: 347px;
}

.frame .img-wrapper {
  background-image: url(https://c.animaapp.com/9exExmER/img/mask-1.svg);
  background-size: 100% 100%;
  height: 497px;
  left: 23px;
  position: relative;
  top: 10px;
  width: 316px;
}

.frame .mask-group-3 {
  height: 497px;
  left: 0;
  position: absolute;
  top: 0;
  width: 316px;
}

.frame .element-3 {
  background-image: url(https://c.animaapp.com/9exExmER/img/<EMAIL>);
  background-size: 100% 100%;
  height: 489px;
  left: 196px;
  position: absolute;
  top: 274px;
  width: 393px;
}

.frame .mockup-3 {
  background-image: url(https://c.animaapp.com/9exExmER/img/mask-2.svg);
  background-size: 100% 100%;
  height: 456px;
  left: 10px;
  position: relative;
  top: 8px;
  width: 363px;
}

.frame .mask-group-4 {
  height: 456px;
  left: 0;
  position: absolute;
  top: 0;
  width: 363px;
}

.frame .overlap-9 {
  height: 780px;
  left: -61px;
  position: absolute;
  top: 2500px;
  width: 1732px;
}

.frame .hand-and-iphone {
  height: 780px;
  left: 0;
  position: absolute;
  top: 0;
  width: 1039px;
}

.frame .artboard {
  height: 780px;
  position: relative;
}

.frame .background-4 {
  height: 780px;
  left: 716px;
  position: absolute;
  top: -2462px;
  width: 1039px;
}

.frame .overlap-group-3 {
  height: 704px;
  left: 252px;
  position: absolute;
  top: 76px;
  width: 525px;
}

.frame .hand {
  height: 617px;
  left: 8px;
  position: absolute;
  top: 87px;
  width: 448px;
}

.frame .iphone-pro-2 {
  height: 588px;
  left: 0;
  position: absolute;
  top: 0;
  width: 523px;
}

.frame .mockup-4 {
  background-image: url(https://c.animaapp.com/9exExmER/img/mask-group-5.png);
  background-size: 100% 100%;
  height: 570px;
  left: 9px;
  position: absolute;
  top: 9px;
  width: 505px;
}
