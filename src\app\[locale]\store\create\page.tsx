'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { useAuth } from '../../../lib/firebase/context/AuthContext';
import StoreService, { CreateStoreData } from '../../../lib/services/store_services';
import { Services, BusinessType } from '../../../lib/models/types';
import { AddressValidationResult } from '../../../lib/models/place';
import { FiArrowLeft, FiArrowRight, FiCheck, FiMapPin, FiMail, FiClock, FiGlobe, FiAlertCircle, FiCheckCircle } from 'react-icons/fi';
import StorePhotoUpload from '../../components/ui/StorePhotoUpload';
import { PhoneNumberInput, validatePhoneNumber as validatePhoneFormat } from '../../components/ui/PhoneNumberInput';
import { validateWebsite, normalizeWebsiteUrl } from '../../../lib/utils/validations';
import SmartAddressSearch from '../../components/ui/SmartAddressSearch';
import { Address, GeoPlace } from '../../../lib/models/place';
import GoogleMapsService from '../../../lib/services/google_map_services';
import GooglePhotosPreview from '../../components/ui/GooglePhotosPreview';

// Step enumeration - 重新调整步骤顺序
enum Step {
  LOCATION = 0,    // 第一步：地址搜索和选择
  BASIC_INFO = 1,  // 第二步：基本信息
  SERVICES = 2,    // 第三步：服务选择
  SETTINGS = 3     // 第四步：营业时间和预览
}

const CreateStorePage = () => {
  const t = useTranslations('storeCreation');
  const tCommon = useTranslations('common');
  const tAddress = useTranslations('Address');
  const router = useRouter();
  const { user, userData } = useAuth();
  
  const [currentStep, setCurrentStep] = useState(Step.LOCATION); // 从地址搜索开始
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Address validation state
  const [addressValidationResult, setAddressValidationResult] = useState<AddressValidationResult | null>(null);
  const [addressValidated, setAddressValidated] = useState(false);

  // 地址相关增强状态
  const [placeData, setPlaceData] = useState<GeoPlace | undefined>(undefined);
  const [useSmartAddressSearch, setUseSmartAddressSearch] = useState(true);
  const [googlePlaceSelected, setGooglePlaceSelected] = useState(false);
  const [googlePlaceDetails, setGooglePlaceDetails] = useState<{
    businessName?: string;
    formattedAddress?: string;
    phoneNumber?: string;
    website?: string;
    rating?: number;
    userRatingsTotal?: number;
    businessStatus?: string;
    types?: string[];
    openingHours?: {
      weekday_text?: string[];
    };
    photos?: Array<{
      photo_reference: string;
      height: number;
      width: number;
      html_attributions: string[];
    }>;
    placeId: string;
  } | null>(null);

  const [formData, setFormData] = useState<CreateStoreData>({
    storeName: '',
    businessType: BusinessType.OTHER,
    description: '',
    phone: '+1 ',
    email: '',
    website: '',
    currentAddress: {
      addressLine1: '',
      addressLine2: '',
      city: '',
      province: '',
      country: '',
      postCode: ''
    },
    services: {
      grooming: false,
      boarding: false,
      veterinary: false,
      training: false,
      retail: false
    },
    businessHours: {},
    avatarUrl: '',
    storePhotos: [],
    // Store address validation result
    addressValidationResult: undefined
  });

  // 步骤配置 - 调整顺序和标题
  const steps = [
    { key: Step.LOCATION, title: t('locationInfo'), icon: FiMapPin },
    { key: Step.BASIC_INFO, title: t('basicInfo'), icon: FiCheck },
    { key: Step.SERVICES, title: t('servicesOffered'), icon: FiCheck },
    { key: Step.SETTINGS, title: t('businessHours'), icon: FiClock }
  ];

  // 业务类型选项
  const businessTypes = [
    { value: BusinessType.ONLINE_STORE, label: t('businessTypes.online_store') },
    { value: BusinessType.FAMILY_BASED_BUSINESS, label: t('businessTypes.family_based_business') },
    { value: BusinessType.RETAIL_BUSINESS, label: t('businessTypes.retail_business') },
    { value: BusinessType.COMMERCIAL_BUSINESS, label: t('businessTypes.commercial_business') },
    { value: BusinessType.PET_STORE, label: t('businessTypes.pet_store') },
    { value: BusinessType.VETERINARY_CLINIC, label: t('businessTypes.veterinary_clinic') },
    { value: BusinessType.PET_GROOMING, label: t('businessTypes.pet_grooming') },
    { value: BusinessType.PET_HOTEL, label: t('businessTypes.pet_hotel') },
    { value: BusinessType.PET_TRAINING, label: t('businessTypes.pet_training') },
    { value: BusinessType.COMPREHENSIVE_SERVICE, label: t('businessTypes.comprehensive_service') },
    { value: BusinessType.FRANCHISE, label: t('businessTypes.franchise') },
    { value: BusinessType.MOBILE_SERVICE, label: t('businessTypes.mobile_service') },
    { value: BusinessType.OTHER, label: t('businessTypes.other') }
  ];

  // 服务选项
  const serviceOptions = [
    { key: 'grooming', label: t('services.grooming'), icon: '✂️' },
    { key: 'boarding', label: t('services.boarding'), icon: '🏠' },
    { key: 'veterinary', label: t('services.veterinary'), icon: '🩺' },
    { key: 'training', label: t('services.training'), icon: '🎓' },
    { key: 'retail', label: t('services.retail'), icon: '🛍️' },
    { key: 'other', label: t('services.other'), icon: '💬' }
  ];

  // 星期天数组
  const weekDays = [
    { key: 'monday', label: tCommon('weekDays.monday') },
    { key: 'tuesday', label: tCommon('weekDays.tuesday') },
    { key: 'wednesday', label: tCommon('weekDays.wednesday') },
    { key: 'thursday', label: tCommon('weekDays.thursday') },
    { key: 'friday', label: tCommon('weekDays.friday') },
    { key: 'saturday', label: tCommon('weekDays.saturday') },
    { key: 'sunday', label: tCommon('weekDays.sunday') }
  ];

  // 转换Google Maps营业时间到BusinessHours格式
  const convertOpeningHoursToBusinessHours = (openingHours: {
    weekday_text?: string[];
    open_now?: boolean;
  }): Record<string, { open: string; close: string; closed: boolean }> => {
    const businessHours: Record<string, { open: string; close: string; closed: boolean }> = {};
    
    if (!openingHours.weekday_text) {
      // Default business hours if no data available
      const defaultHours = {
        open: '09:00',
        close: '18:00',
        closed: false
      };
      
      ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
        businessHours[day] = defaultHours;
      });
      
      return businessHours;
    }

    // Map Google's weekday_text to our format
    const dayMapping: Record<string, string> = {
      'Monday': 'monday',
      'Tuesday': 'tuesday', 
      'Wednesday': 'wednesday',
      'Thursday': 'thursday',
      'Friday': 'friday',
      'Saturday': 'saturday',
      'Sunday': 'sunday'
    };

    openingHours.weekday_text.forEach(dayText => {
      // Parse format like "Monday: 9:00 AM – 6:00 PM" or "Monday: Closed"
      const [dayName, hoursText] = dayText.split(': ');
      
      const dayKey = dayMapping[dayName];
      if (!dayKey) return;

      if (hoursText.toLowerCase().includes('closed')) {
        businessHours[dayKey] = {
          open: '09:00',
          close: '18:00',
          closed: true
        };
      } else {
        // Parse time range like "9:00 AM – 6:00 PM"
        // 支持多种格式：9:00 AM – 6:00 PM, 9:00AM-6:00PM, 9:00 AM-6:00 PM
        const timeMatch = hoursText.match(/(\d{1,2}:\d{2}\s*(?:AM|PM))\s*[–-]\s*(\d{1,2}:\d{2}\s*(?:AM|PM))/i);
        if (timeMatch) {
          const openTime = convertTo24Hour(timeMatch[1].trim());
          const closeTime = convertTo24Hour(timeMatch[2].trim());
          
          businessHours[dayKey] = {
            open: openTime,
            close: closeTime,
            closed: false
          };
        } else {
          // Fallback to default hours
          businessHours[dayKey] = {
            open: '09:00',
            close: '18:00',
            closed: false
          };
        }
      }
    });

    // Fill in missing days with default hours
    ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].forEach(day => {
      if (!businessHours[day]) {
        businessHours[day] = {
          open: '09:00',
          close: '18:00',
          closed: false
        };
      }
    });

    return businessHours;
  };

  // 转换12小时制到24小时制
  const convertTo24Hour = (time12h: string): string => {
    // 清理输入，移除多余空格
    const cleanTime = time12h.trim();
    
    console.log('Converting time:', time12h, '-> cleaned:', cleanTime);
    
    // 匹配时间格式：9:00 AM, 9:00AM, 12:30 PM, 12:30PM
    const match = cleanTime.match(/^(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
    
    if (!match) {
      console.warn('Invalid time format:', time12h);
      return '09:00'; // 默认返回
    }
    
    let hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    const period = match[3].toUpperCase();
    
    console.log('Parsed time components:', { hours, minutes, period });
    
    // 处理12小时制转换
    if (period === 'PM' && hours !== 12) {
      hours += 12;
    } else if (period === 'AM' && hours === 12) {
      hours = 0;
    }
    
    // 确保小时在0-23范围内
    hours = hours % 24;
    
    const result = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    console.log('Converted result:', time12h, '->', result);
    
    return result;
  };

  // Check user permissions
  useEffect(() => {
    if (!user || !userData) {
      router.push('/auth/login');
    }
  }, [user, userData, router]);

  // Update form data
  const updateFormData = (field: string, value: unknown) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear related errors
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle phone number input with real-time formatting
  const handlePhoneChange = (value: string) => {
    // PhoneNumberInput component already handles formatting, use directly
    updateFormData('phone', value);
    
    // Real-time validation
    if (value && value.trim() !== '+1 ' && !validatePhoneFormat(value)) {
      setErrors(prev => ({
        ...prev,
        phone: tCommon('validation.phoneInvalid')
      }));
    } else {
      setErrors(prev => ({
        ...prev,
        phone: ''
      }));
    }
  };

  // Handle website URL input
  const handleWebsiteChange = (value: string) => {
    updateFormData('website', value);
    
    // Real-time website URL validation
    if (value && !validateWebsite(value)) {
      setErrors(prev => ({
        ...prev,
        website: tCommon('validation.websiteInvalid')
      }));
    } else {
      setErrors(prev => ({
        ...prev,
        website: ''
      }));
    }
  };

  // Handle website URL blur, auto-add protocol
  const handleWebsiteBlur = () => {
    if (formData.website) {
      const normalizedUrl = normalizeWebsiteUrl(formData.website);
      updateFormData('website', normalizedUrl);
    }
  };

  // Update services selection
  const updateServices = (service: keyof Services, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      services: {
        ...prev.services,
        [service]: checked
      }
    }));
  };

  // Update business hours
  const updateBusinessHours = (day: string, field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      businessHours: {
        ...prev.businessHours,
        [day]: {
          ...prev.businessHours![day],
          [field]: value
        }
      }
    }));
  };

  // Validation function for each step - 调整验证逻辑
  const validateStep = (step: Step): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case Step.LOCATION:
        if (!addressValidated) {
          newErrors.address = t('validation.addressValidationRequired');
        } else if (addressValidationResult && !addressValidationResult.isValid) {
          newErrors.address = t('validation.addressValidationError');
        }
        break;
        
      case Step.BASIC_INFO:
        if (!formData.storeName) newErrors.storeName = t('validation.storeNameRequired');
        if (!formData.businessType) newErrors.businessType = t('validation.businessTypeRequired');
        if (!formData.phone) newErrors.phone = t('validation.phoneRequired');
        if (!formData.email) newErrors.email = t('validation.emailRequired');
        break;
        
      case Step.SERVICES:
        const hasServices = Object.values(formData.services).some(Boolean);
        if (!hasServices) newErrors.services = t('validation.servicesRequired');
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Navigate to next step
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, Step.SETTINGS));
    }
  };

  // Navigate to previous step
  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, Step.LOCATION));
  };

  // 地址选择回调 - 增强以处理Google Places详细信息
  const handleSmartAddressSelection = async (data: {
    address: Address;
    validationResult: AddressValidationResult;
    source: 'google_place' | 'manual';
    placeData?: GeoPlace;
  }) => {
    setFormData(prev => ({
      ...prev,
      currentAddress: data.address,
      addressValidationResult: data.validationResult
    }));
    setAddressValidationResult(data.validationResult);
    setAddressValidated(true);
    setPlaceData(data.placeData);
    setErrors(prev => ({ ...prev, address: '' }));

    // 如果是Google Places，获取详细信息并预填表单
    if (data.source === 'google_place' && data.validationResult.googlePlaceId) {
      setGooglePlaceSelected(true);
      await fetchGooglePlaceDetails(data.validationResult.googlePlaceId);
    } else {
      setGooglePlaceSelected(false);
      setGooglePlaceDetails(null);
    }
  };

  // 获取Google Places详细信息的函数
  const fetchGooglePlaceDetails = async (placeId: string) => {
    try {
      const result = await GoogleMapsService.getComprehensivePlaceDetails(placeId);
      
      if (result.success && result.data) {
        const details = result.data;
        setGooglePlaceDetails(details);

        // 转换营业时间并添加调试信息
        let convertedBusinessHours = {};
        if (details.openingHours) {
          console.log('Original Google Maps opening hours:', details.openingHours);
          convertedBusinessHours = convertOpeningHoursToBusinessHours(details.openingHours);
          console.log('Converted business hours:', convertedBusinessHours);
        }

        // 自动填充表单数据
        setFormData(prev => ({
          ...prev,
          storeName: details.businessName || '',
          phone: details.phoneNumber || '+1 ',
          website: details.website || '',
          // 转换营业时间
          businessHours: details.openingHours ? 
            convertedBusinessHours : 
            prev.businessHours,
        }));

        console.log('Google Place详细信息已获取:', details);
      }
    } catch (error) {
      console.error('获取Google Places详细信息时出错:', error);
    }
  };

  const handleSmartAddressError = (error: string) => {
    setErrors(prev => ({ ...prev, address: error }));
  };

  const toggleAddressSearchMode = () => {
    setUseSmartAddressSearch(!useSmartAddressSearch);
    if (!useSmartAddressSearch) {
      setAddressValidated(false);
      setAddressValidationResult(null);
      setPlaceData(undefined);
      setGooglePlaceSelected(false);
      setGooglePlaceDetails(null);
    }
  };

  // Submit form 
  const handleSubmit = async () => {
    if (!validateStep(Step.SETTINGS)) return;
    setLoading(true);
    try {
      const result = await StoreService.createStore(userData!, {
        ...formData,
        placeData,
        // 如果选择了Google Places，传递详细信息
        ...(googlePlaceSelected && googlePlaceDetails && {
          googlePlaceId: addressValidationResult?.googlePlaceId,
          // 可以添加更多Google Places的信息
        })
      }, (step, total, message) => {
        console.log(`Step ${step}/${total}: ${message}`);
      });
      if (result.success) {
        router.push('/store/create/success');
      } else {
        setErrors({ submit: result.error || t('error') });
      }
    } catch (error) {
      console.error('Error creating store:', error);
      setErrors({ submit: t('error') });
    } finally {
      setLoading(false);
    }
  };

  // Render basic information step
  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('basicInfo')}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('storeName')} <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.storeName}
              onChange={(e) => updateFormData('storeName', e.target.value)}
              placeholder={t('storeNamePlaceholder')}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                errors.storeName ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.storeName && (
              <p className="mt-1 text-sm text-red-600">{errors.storeName}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('businessType')} <span className="text-red-500">*</span>
            </label>
            <select
              value={formData.businessType}
              onChange={(e) => updateFormData('businessType', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                errors.businessType ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">{t('selectBusinessType')}</option>
              {businessTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.businessType && (
              <p className="mt-1 text-sm text-red-600">{errors.businessType}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('description')}
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => updateFormData('description', e.target.value)}
              placeholder={t('descriptionPlaceholder')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          <PhoneNumberInput
            label={`${t('phone')} *`}
            value={formData.phone}
            onChange={(value) => handlePhoneChange(value)}
            placeholder={t('phonePlaceholder')}
            error={errors.phone}
            required
            className="py-3 border-2 focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 transition-all duration-200"
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FiMail className="inline mr-1" />
              {t('email')} <span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => updateFormData('email', e.target.value)}
              placeholder={t('emailPlaceholder')}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FiGlobe className="inline mr-1" />
              {t('website')}
            </label>
            <input
              type="url"
              value={formData.website || ''}
              onChange={(e) => handleWebsiteChange(e.target.value)}
              onBlur={handleWebsiteBlur}
              placeholder={t('websitePlaceholder')}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent ${
                errors.website ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.website && (
              <p className="mt-1 text-sm text-red-600">{errors.website}</p>
            )}
            {formData.website && validateWebsite(formData.website) && (
              <p className="mt-1 text-sm text-green-600">
                ✓ valid website address
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              like: example.com or https://www.example.com
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  // 第一步：地址搜索和选择
  const renderLocation = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          <FiMapPin className="inline mr-2" />
          {t('locationInfo')}
        </h3>
        <button
          type="button"
          onClick={toggleAddressSearchMode}
          className="text-purple-600 hover:text-purple-700 font-medium"
        >
          {useSmartAddressSearch ? t('manualMode') : t('smartSearchMode')}
        </button>
      </div>

      {/* 只在智能搜索模式下显示蓝色提示框 */}
      {useSmartAddressSearch && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h4 className="text-blue-800 font-medium mb-2">🔍 {t('smartAddressSearch.title')}</h4>
          <p className="text-blue-700 text-sm">
            {googlePlaceSelected 
              ? t('smartAddressSearch.selected')
              : t('smartAddressSearch.notSelected')
            }
          </p>
        </div>
      )}

      {useSmartAddressSearch ? (
        <SmartAddressSearch
          onAddressSelected={handleSmartAddressSelection}
          onError={handleSmartAddressError}
          currentAddress={formData.currentAddress}
          className="w-full"
        />
      ) : (
        <div className="space-y-4">
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-gray-800 font-medium mb-2">📝 {t('manualAddressEntry')}</h4>
            <p className="text-gray-600 text-sm">{t('manualAddressHint')}</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('addressLine1')} *
              </label>
              <input
                type="text"
                value={formData.currentAddress.addressLine1}
                onChange={(e) => updateFormData('currentAddress', {
                  ...formData.currentAddress,
                  addressLine1: e.target.value
                })}
                placeholder={tAddress('addressLine1Placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('addressLine2')}
              </label>
              <input
                type="text"
                value={formData.currentAddress.addressLine2}
                onChange={(e) => updateFormData('currentAddress', {
                  ...formData.currentAddress,
                  addressLine2: e.target.value
                })}
                placeholder={tAddress('addressLine2Placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('city')} *
              </label>
              <input
                type="text"
                value={formData.currentAddress.city}
                onChange={(e) => updateFormData('currentAddress', {
                  ...formData.currentAddress,
                  city: e.target.value
                })}
                placeholder={tAddress('cityPlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('province')} *
              </label>
              <input
                type="text"
                value={formData.currentAddress.province}
                onChange={(e) => updateFormData('currentAddress', {
                  ...formData.currentAddress,
                  province: e.target.value
                })}
                placeholder={tAddress('provincePlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('country')} *
              </label>
              <input
                type="text"
                value={formData.currentAddress.country}
                onChange={(e) => updateFormData('currentAddress', {
                  ...formData.currentAddress,
                  country: e.target.value
                })}
                placeholder={tAddress('countryPlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {tAddress('postCode')} *
              </label>
              <input
                type="text"
                value={formData.currentAddress.postCode}
                onChange={(e) => updateFormData('currentAddress', {
                  ...formData.currentAddress,
                  postCode: e.target.value
                })}
                placeholder={tAddress('postCodePlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>
          </div>

          <button
            onClick={async () => {
              // 验证手动输入的地址
              if (!formData.currentAddress.addressLine1 || !formData.currentAddress.city || 
                  !formData.currentAddress.province || !formData.currentAddress.country || 
                  !formData.currentAddress.postCode) {
                setErrors(prev => ({ ...prev, address: t('validation.addressRequired') }));
                return;
              }

              try {
                const result = await GoogleMapsService.validateAddress(formData.currentAddress);
                setAddressValidationResult(result);
                setAddressValidated(true);
                setErrors(prev => ({ ...prev, address: '' }));
              } catch {
                setErrors(prev => ({ ...prev, address: t('validation.addressValidationError') }));
              }
            }}
            className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center justify-center"
          >
            <FiCheck className="w-4 h-4 mr-2" />
            {tAddress('addressValidation.title')}
          </button>
        </div>
      )}

      {addressValidated && addressValidationResult && (
        <div className={`mb-4 p-3 rounded-lg border ${
          addressValidationResult.isValid
            ? 'bg-green-50 border-green-200 text-green-800'
            : 'bg-red-50 border-red-200 text-red-800'
        }`}>
          <div className="flex items-center">
            {addressValidationResult.isValid ? (
              <FiCheckCircle className="mr-2 h-5 w-5" />
            ) : (
              <FiAlertCircle className="mr-2 h-5 w-5" />
            )}
            <div>
              <p className="font-medium">
                {addressValidationResult.isValid ? t('addressValidated') : t('addressValidationFailed')}
              </p>
              <p className="text-sm">{addressValidationResult.message}</p>
              {addressValidationResult.isValid && (
                <p className="text-sm">
                  {addressValidationResult.hasGooglePlace
                    ? t('addressValidationSuccess')
                    : t('addressValidationCustom')}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {errors.address && (
        <p className="mt-2 text-sm text-red-600">{errors.address}</p>
      )}

      {/* 显示Google Places预览信息 */}
      {googlePlaceSelected && googlePlaceDetails && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="text-green-800 font-medium mb-3">📍 {tAddress('GoogleMaps.googleMapsBusinessPreview')}</h4>
          <div className="space-y-2 text-sm">
            <div><strong>{tAddress('GoogleMaps.businessName')}</strong> {googlePlaceDetails.businessName || tCommon('unknown')}</div>
            <div><strong>{tAddress('GoogleMaps.address')}</strong> {googlePlaceDetails.formattedAddress}</div>
            <div><strong>{tAddress('GoogleMaps.phone')}</strong> {googlePlaceDetails.phoneNumber || tCommon('notProvided')}</div>
            <div><strong>{tAddress('GoogleMaps.website')}</strong> {googlePlaceDetails.website || tCommon('notProvided')}</div>
            <div><strong>{tAddress('GoogleMaps.rating')}</strong> {googlePlaceDetails.rating ? `${googlePlaceDetails.rating}⭐ (${googlePlaceDetails.userRatingsTotal} ${tAddress('GoogleMaps.reviewNumber')})` : tCommon('noRating')}</div>
          </div>
          <p className="text-green-700 text-xs mt-2">✨ {tAddress('GoogleMaps.autoFillForm')}</p>
        </div>
      )}
    </div>
  );

  // Render services selection step
  const renderServices = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('servicesOffered')}</h3>
        <p className="text-gray-600 mb-6">{t('selectServices')}</p>
        
        {errors.services && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{errors.services}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {serviceOptions.map(service => (
            <div
              key={service.key}
              className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                formData.services[service.key as keyof Services]
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => updateServices(
                service.key as keyof Services,
                !formData.services[service.key as keyof Services]
              )}
            >
              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.services[service.key as keyof Services]}
                  onChange={(e) => updateServices(service.key as keyof Services, e.target.checked)}
                  className="mr-3 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">{service.icon}</span>
                    <span className="font-medium text-gray-900">{service.label}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // 第四步：营业时间和预览信息 - 显示Google Maps获取的信息
  const renderSettings = () => (
    <div className="space-y-6">
      {/* Google Places信息展示 */}
      {googlePlaceSelected && googlePlaceDetails && (
        <div className="bg-gradient-to-r from-blue-50 to-green-50 border border-blue-200 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            <FiMapPin className="inline mr-2" />
            📍 {tAddress('GoogleMaps.googleMapsBusinessCompleteView')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.businessName')}:</strong>
                <p className="text-gray-900">{googlePlaceDetails.businessName || tCommon('unknown')}</p>
              </div>
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.address')}:</strong>
                <p className="text-gray-900">{googlePlaceDetails.formattedAddress}</p>
              </div>
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.phone')}:</strong>
                <p className="text-gray-900">{googlePlaceDetails.phoneNumber || tCommon('notProvided')}</p>
              </div>
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.website')}:</strong>
                <p className="text-gray-900">{googlePlaceDetails.website || tCommon('notProvided')}</p>
              </div>
            </div>
            
            <div className="space-y-3">
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.rating')}:</strong>
                <p className="text-gray-900">
                  {googlePlaceDetails.rating ? `${googlePlaceDetails.rating}⭐ (${googlePlaceDetails.userRatingsTotal} ${tAddress('GoogleMaps.reviewNumber')})` : tCommon('noRating')}
                </p>
              </div>
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.businessStatus')}:</strong>
                <p className="text-gray-900">{googlePlaceDetails.businessStatus || tCommon('open')}</p>
              </div>
              <div>
                <strong className="text-gray-700">{tAddress('GoogleMaps.businessType')}:</strong>
                <p className="text-gray-900">{googlePlaceDetails.types?.slice(0, 3).join(', ') || tCommon('petStore')}</p>
              </div>
            </div>
          </div>

          {/* 营业时间显示 */}
          {googlePlaceDetails.openingHours && (
            <div className="mt-6 p-4 bg-white rounded-lg border">
              <h4 className="font-medium text-gray-800 mb-3">📅 {tAddress('GoogleMaps.googleMapsBusinessHours')}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                {googlePlaceDetails.openingHours.weekday_text?.map((day: string, index: number) => (
                  <div key={index} className="flex justify-between">
                    <span className="font-medium">{day.split(': ')[0]}:</span>
                    <span>{day.split(': ')[1] || tCommon('closed')}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Google照片预览和上传 */}
      {googlePlaceSelected && googlePlaceDetails?.photos && googlePlaceDetails.photos.length > 0 && (
        <GooglePhotosPreview
          photos={googlePlaceDetails.photos}
          placeId={googlePlaceSelected ? googlePlaceDetails!.placeId : undefined}
          onPhotosUploaded={(urls) => {
            // setGooglePhotosUploaded(urls); // This line is removed
            // 合并到现有的storePhotos中
            setFormData(prev => ({
              ...prev,
              storePhotos: [...(prev.storePhotos || []), ...urls]
            }));
          }}
          onError={(error) => setErrors({ ...errors, googlePhotos: error })}
        />
      )}

      {/* 营业时间设置 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          <FiClock className="inline mr-2" />
          {t('businessHours')}
          {googlePlaceSelected && <span className="text-sm text-green-600 ml-2">{tAddress('GoogleMaps.adjustBasedOnGoogleMaps')}</span>}
        </h3>
        <div className="space-y-4">
          {weekDays.map(day => (
            <div key={day.key} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
              <div className="w-16 text-sm font-medium text-gray-700">
                {day.label}
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={!formData.businessHours?.[day.key]?.closed}
                  onChange={(e) => updateBusinessHours(day.key, 'closed', !e.target.checked)}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">{tCommon('open')}</span>
              </div>
              {!formData.businessHours?.[day.key]?.closed && (
                <div className="flex items-center space-x-2">
                  <input
                    type="time"
                    value={formData.businessHours?.[day.key]?.open || '09:00'}
                    onChange={(e) => updateBusinessHours(day.key, 'open', e.target.value)}
                    className="px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                  <span className="text-gray-500">-</span>
                  <input
                    type="time"
                    value={formData.businessHours?.[day.key]?.close || '18:00'}
                    onChange={(e) => updateBusinessHours(day.key, 'close', e.target.value)}
                    className="px-2 py-1 border border-gray-300 rounded text-sm"
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 店铺照片上传 */}
      <div>
        <StorePhotoUpload
          placeId={googlePlaceSelected ? googlePlaceDetails!.placeId : undefined}
          currentAvatarURL={formData.avatarUrl}
          currentPhotos={formData.storePhotos}
          onAvatarUploaded={(url) => updateFormData('avatarUrl', url)}
          onPhotosUploaded={(urls) => updateFormData('storePhotos', urls)}
          onError={(error) => setErrors({ ...errors, photos: error })}
          maxPhotos={6}
          autoCompress={true}
        />
        {errors.photos && (
          <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{errors.photos}</p>
          </div>
        )}
      </div>
    </div>
  );

  // Render step content - 调整步骤渲染
  const renderStepContent = () => {
    switch (currentStep) {
      case Step.LOCATION:
        return renderLocation();
      case Step.BASIC_INFO:
        return renderBasicInfo();
      case Step.SERVICES:
        return renderServices();
      case Step.SETTINGS:
        return renderSettings();
      default:
        return null;
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{tCommon('loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <FiArrowLeft className="mr-2" />
            {tCommon('back')}
          </button>
          <h1 className="text-3xl font-bold text-gray-900">{t('title')}</h1>
          <p className="text-gray-600 mt-2">{t('subtitle')}</p>
        </div>

        {/* Step indicators */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={step.key} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.key
                    ? 'bg-purple-600 border-purple-600 text-white'
                    : 'border-gray-300 text-gray-400'
                }`}>
                  {currentStep > step.key ? (
                    <FiCheck className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    currentStep >= step.key ? 'text-purple-600' : 'text-gray-400'
                  }`}>
                    {step.title}
                  </p>
                </div>
                {index < steps.length - 1 && (
                  <div className={`hidden md:block w-16 h-0.5 ml-8 ${
                    currentStep > step.key ? 'bg-purple-600' : 'bg-gray-300'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form content */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          {renderStepContent()}
        </div>

        {/* Error messages */}
        {errors.submit && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600">{errors.submit}</p>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center px-6 py-2 rounded-lg font-medium ${
              currentStep === 0
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            <FiArrowLeft className="mr-2" />
            {tCommon('previous')}
          </button>

          {currentStep < steps.length - 1 ? (
            <button
              onClick={nextStep}
              className="flex items-center px-6 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700"
            >
              {tCommon('next')}
              <FiArrowRight className="ml-2" />
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="flex items-center px-6 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 disabled:opacity-50"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {t('submitting')}
                </>
              ) : (
                <>
                  <FiCheck className="mr-2" />
                  {t('submitButton')}
                </>
              )}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateStorePage; 