.container-wrapper {
  align-items: center;
  background: linear-gradient(
    90deg,
    rgba(242, 211, 164, 1) 0%,
    rgba(217, 217, 217, 1) 100%
  );
  display: flex;
  flex-direction: column;
  left: 0;
  padding: 112px 0px;
  position: absolute;
  top: 4849px;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
}

.container-wrapper .container-2 {
  height: 236px;
  max-width: 1536px;
  position: relative;
  width: 1536px;
}

.container-wrapper .heading-ready-to {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 55px;
  font-weight: 700;
  letter-spacing: 0;
  line-height: 1.1;
  text-align: center;
  margin: 0 auto 24px auto;
}

.container-wrapper .join-the-onenata-wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  left: 432px;
  max-width: 672px;
  position: absolute;
  top: 80px;
  width: 672px;
}

.container-wrapper .join-the-onenata {
  color: #262626;
  font-family: "<PERSON><PERSON><PERSON>", Helvetica;
  font-size: 20px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 28px;
  margin-top: -1.00px;
  position: relative;
  text-align: center;
  width: fit-content;
}

.container-wrapper .component-3 {
  background: linear-gradient(
    147deg,
    rgba(161, 38, 255, 1) 0%,
    rgba(96, 23, 153, 1) 100%
  ) !important;
  background-color: unset !important;
  left: 525px !important;
  position: absolute !important;
  top: 184px !important;
}

.container-wrapper .component-5 {
  background: linear-gradient(
    147deg,
    rgba(161, 38, 255, 1) 0%,
    rgba(96, 23, 153, 1) 100%
  ) !important;
  background-color: unset !important;
  left: 770px !important;
  position: absolute !important;
  top: 184px !important;
}

.container-wrapper .component-6 {
  color: #ffffff !important;
}
