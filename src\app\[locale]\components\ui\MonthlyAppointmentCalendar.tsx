'use client';

import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { AppointmentStatus } from '@/app/lib/models/types';
import { 
  FiChevronLeft,
  FiChevronRight,
  FiCalendar,
  FiClock,
  FiUser,
  FiEdit,
  FiTrash2,
  FiCheck
} from 'react-icons/fi';
import { Button } from './Button';

interface AppointmentData {
  appointmentId: string;
  storeId: string;
  status: AppointmentStatus;
  timeInfo: {
    date: string;
    startTime: string;
    endTime: string;
    duration: number;
  };
  customerInfo: {
    name: string;
    email?: string;
    phoneNumber?: string;
  };
  serviceInfo: {
    serviceName: string;
    serviceCategory: string;
    serviceBreed: string;
  };
  staffInfo: {
    staffName: string;
    staffEmail?: string;
  };
}

interface MonthlyAppointmentCalendarProps {
  appointments: AppointmentData[];
  storeId: string;
  canCreateAppointment: boolean;
  onUpdateStatus?: (appointmentId: string, status: AppointmentStatus) => void;
  onDeleteAppointment?: (appointmentId: string) => void;
  selectedDate?: string;
  selectedStatus?: AppointmentStatus | '';
}

export default function MonthlyAppointmentCalendar({
  appointments,
  storeId,
  canCreateAppointment,
  onUpdateStatus,
  onDeleteAppointment,
  selectedDate,
  selectedStatus
}: MonthlyAppointmentCalendarProps) {
  const router = useRouter();
  const t = useTranslations('appointments');
  
  const [currentDate, setCurrentDate] = useState(() => {
    if (selectedDate) {
      return new Date(selectedDate);
    }
    return new Date();
  });

  // Filter appointments by selected filters
  const filteredAppointments = useMemo(() => {
    let filtered = appointments;
    
    if (selectedStatus) {
      filtered = filtered.filter(apt => apt.status === selectedStatus);
    }
    
    if (selectedDate) {
      filtered = filtered.filter(apt => apt.timeInfo.date === selectedDate);
    }
    
    return filtered;
  }, [appointments, selectedStatus, selectedDate]);

  // Group appointments by date
  const appointmentsByDate = useMemo(() => {
    const grouped: Record<string, AppointmentData[]> = {};
    
    filteredAppointments.forEach(appointment => {
      const date = appointment.timeInfo.date;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(appointment);
    });
    
    // Sort appointments by time within each date
    Object.keys(grouped).forEach(date => {
      grouped[date].sort((a, b) => {
        return a.timeInfo.startTime.localeCompare(b.timeInfo.startTime);
      });
    });
    
    return grouped;
  }, [filteredAppointments]);

  // Generate calendar days for current month
  const calendarDays = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    
    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Start from the first Sunday before or on the first day of month
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());
    
    // End on the last Saturday after or on the last day of month
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));
    
    const days = [];
    const current = new Date(startDate);
    
    while (current <= endDate) {
      const dateString = current.toISOString().split('T')[0];
      const isCurrentMonth = current.getMonth() === month;
      const isToday = dateString === new Date().toISOString().split('T')[0];
      const dayAppointments = appointmentsByDate[dateString] || [];
      
      days.push({
        date: new Date(current),
        dateString,
        day: current.getDate(),
        isCurrentMonth,
        isToday,
        appointments: dayAppointments
      });
      
      current.setDate(current.getDate() + 1);
    }
    
    return days;
  }, [currentDate, appointmentsByDate]);

  const getStatusColor = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.DRAFT:
        return 'bg-gray-100 text-gray-700 border-gray-300';
      case AppointmentStatus.CONFIRMED:
        return 'bg-blue-100 text-blue-700 border-blue-300';
      case AppointmentStatus.IN_PROGRESS:
        return 'bg-yellow-100 text-yellow-700 border-yellow-300';
      case AppointmentStatus.COMPLETED:
        return 'bg-green-100 text-green-700 border-green-300';
      case AppointmentStatus.CANCELLED:
        return 'bg-red-100 text-red-700 border-red-300';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-300';
    }
  };

  const getStatusIcon = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.CONFIRMED:
        return <FiCheck className="w-3 h-3" />;
      case AppointmentStatus.IN_PROGRESS:
        return <FiClock className="w-3 h-3" />;
      case AppointmentStatus.COMPLETED:
        return <FiCheck className="w-3 h-3" />;
      default:
        return <FiClock className="w-3 h-3" />;
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const formatTime = (time: string) => {
    return time.substring(0, 5); // HH:MM format
  };

  const handleAppointmentClick = (appointment: AppointmentData) => {
    router.push(`/store/${storeId}/appointments/${appointment.appointmentId}`);
  };

  const handleAppointmentAction = (e: React.MouseEvent, appointmentId: string, action: 'edit' | 'delete' | 'status', status?: AppointmentStatus) => {
    e.stopPropagation();
    
    switch (action) {
      case 'edit':
        router.push(`/store/${storeId}/appointments/${appointmentId}/edit`);
        break;
      case 'delete':
        if (onDeleteAppointment) {
          onDeleteAppointment(appointmentId);
        }
        break;
      case 'status':
        if (onUpdateStatus && status) {
          onUpdateStatus(appointmentId, status);
        }
        break;
    }
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Calendar Header */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <FiCalendar className="w-6 h-6 mr-2 text-blue-600" />
              {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
            </h2>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              onClick={goToToday}
              className="bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 px-3 py-2 text-sm rounded-lg"
            >
              {t('calendar.today')}
            </Button>
            <Button
              onClick={() => navigateMonth('prev')}
              className="bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 p-2 rounded-lg"
            >
              <FiChevronLeft className="w-4 h-4" />
            </Button>
            <Button
              onClick={() => navigateMonth('next')}
              className="bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 p-2 rounded-lg"
            >
              <FiChevronRight className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-6">
        {/* Day headers */}
        <div className="grid grid-cols-7 gap-px mb-2">
          {dayNames.map((day) => (
            <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar cells */}
        <div className="grid grid-cols-7 gap-px bg-gray-200 rounded-lg overflow-hidden">
          {calendarDays.map((dayData, index) => (
            <div
              key={index}
              className={`min-h-[120px] bg-white p-2 relative ${
                !dayData.isCurrentMonth ? 'bg-gray-50' : ''
              } ${dayData.isToday ? 'bg-blue-50' : ''}`}
            >
              {/* Date number */}
              <div className={`text-sm font-medium mb-2 ${
                !dayData.isCurrentMonth 
                  ? 'text-gray-400' 
                  : dayData.isToday 
                  ? 'text-blue-600 font-bold' 
                  : 'text-gray-900'
              }`}>
                {dayData.day}
              </div>

              {/* Appointments */}
              <div className="space-y-1">
                {dayData.appointments.slice(0, 3).map((appointment) => (
                  <div
                    key={appointment.appointmentId}
                    onClick={() => handleAppointmentClick(appointment)}
                    className={`
                      text-xs p-1 rounded border cursor-pointer
                      hover:shadow-sm transition-all duration-200
                      group relative
                      ${getStatusColor(appointment.status)}
                    `}
                    title={`${appointment.customerInfo.name} - ${appointment.serviceInfo.serviceName} (${formatTime(appointment.timeInfo.startTime)})`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-1 min-w-0 flex-1">
                        {getStatusIcon(appointment.status)}
                        <span className="truncate font-medium">
                          {formatTime(appointment.timeInfo.startTime)}
                        </span>
                      </div>
                      
                      {/* Quick Actions (visible on hover) */}
                      {canCreateAppointment && (
                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center space-x-1">
                          {(appointment.status === AppointmentStatus.DRAFT || appointment.status === AppointmentStatus.CONFIRMED) && (
                            <>
                              <button
                                onClick={(e) => handleAppointmentAction(e, appointment.appointmentId, 'edit')}
                                className="hover:bg-white hover:bg-opacity-50 p-0.5 rounded"
                                title={t('actions.edit')}
                              >
                                <FiEdit className="w-3 h-3" />
                              </button>
                              <button
                                onClick={(e) => handleAppointmentAction(e, appointment.appointmentId, 'delete')}
                                className="hover:bg-white hover:bg-opacity-50 p-0.5 rounded"
                                title={t('actions.delete')}
                              >
                                <FiTrash2 className="w-3 h-3" />
                              </button>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div className="truncate text-xs opacity-75">
                      <FiUser className="w-3 h-3 inline mr-1" />
                      {appointment.customerInfo.name}
                    </div>
                    
                    <div className="truncate text-xs opacity-75">
                      {appointment.serviceInfo.serviceCategory}
                    </div>
                  </div>
                ))}
                
                {/* Show "X more" if there are more than 3 appointments */}
                {dayData.appointments.length > 3 && (
                  <div className="text-xs text-gray-500 text-center py-1">
                    +{dayData.appointments.length - 3} {t('calendar.more')}
                  </div>
                )}
              </div>

              {/* Add appointment button (visible on hover for empty days) */}
              {dayData.appointments.length === 0 && dayData.isCurrentMonth && canCreateAppointment && (
                <button
                  onClick={() => router.push(`/store/${storeId}/appointments/create?date=${dayData.dateString}`)}
                  className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 hover:bg-blue-50 hover:bg-opacity-50 transition-all duration-200 rounded"
                  title={t('actions.createAppointment')}
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 text-lg font-light">+</span>
                  </div>
                </button>
              )}
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="mt-6 flex flex-wrap gap-4 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-100 border border-gray-300 rounded"></div>
            <span className="text-gray-600">{t('status.draft')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-100 border border-blue-300 rounded"></div>
            <span className="text-gray-600">{t('status.confirmed')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-yellow-100 border border-yellow-300 rounded"></div>
            <span className="text-gray-600">{t('status.inProgress')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
            <span className="text-gray-600">{t('status.completed')}</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
            <span className="text-gray-600">{t('status.cancelled')}</span>
          </div>
        </div>
      </div>
    </div>
  );
} 