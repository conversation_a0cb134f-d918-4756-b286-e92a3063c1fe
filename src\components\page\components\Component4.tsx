/*
We're constantly improving the code you see. 
Please share your feedback here: https://form.asana.com/?k=uvp-HPgd3_hyoXRBw1IcNg&d=1152665201300829
*/

import React from "react";
import { Variant1 } from "../icons/Variant1";
import "./Component4.css";

interface Props {
  text: string;
  variant: "one";
  className: any;
  variant1Color: string;
  textClassName: any;
}

export const Component4 = ({
  text = "Get on Google Play",
  variant,
  className,
  variant1Color = "#333333",
  textClassName,
}: Props): JSX.Element => {
  return (
    <div className={`component-4 ${className}`}>
      <Variant1 className="variant-1" color={variant1Color} />
      <div className={`text-2 ${textClassName}`}>{text}</div>
    </div>
  );
};
