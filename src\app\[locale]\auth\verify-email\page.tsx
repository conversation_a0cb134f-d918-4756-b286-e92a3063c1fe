'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '../../components/ui/Button';
import { useAuth, AuthenticatedRoute } from '../../../lib/firebase/context/AuthContext';
import { 
  sendEmailVerificationToUser, 
  updateUserAccountEmailVerificationStatus 
} from '../../../lib/firebase/services/auth';
import { auth } from '../../../lib/firebase/config';

export default function VerifyEmailPage() {
  return (
    <AuthenticatedRoute>
      <VerifyEmailContent />
    </AuthenticatedRoute>
  );
}

function VerifyEmailContent() {
  const t = useTranslations('auth-verify-email');
  const params = useParams();
  const router = useRouter();
  const { user, loading, refreshUser, signOut } = useAuth();
  const locale = params.locale as string;

  const [isResending, setIsResending] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'verified' | 'failed'>('pending');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [resendCooldown, setResendCooldown] = useState(0);
  // 监听用户邮箱验证状态变化
  useEffect(() => {
    if (!user) return;

    // 监听 Firebase Auth 状态变化
    const unsubscribe = auth.onAuthStateChanged(async (currentUser) => {
      if (currentUser && currentUser.emailVerified && verificationStatus !== 'verified') {
        setVerificationStatus('verified');
        
        try {
          // 更新 PortalUserAccount 中的邮箱验证状态
          await updateUserAccountEmailVerificationStatus(currentUser.uid, true);
          
          // 刷新用户数据
          await refreshUser();
          
          // 延迟跳转到个人资料设置页面
          setTimeout(() => {
            router.push(`/${locale}/auth/profile-setup`);
          }, 2000);
        } catch (error) {
          console.error('Error updating email verification status:', error);
        }
      }
    });

    return () => unsubscribe();
  }, [user, verificationStatus, locale, router, refreshUser, t]);

  // 页面加载时检查验证状态
  useEffect(() => {
    if (user && !loading) {
      if (user.emailVerified) {
        setVerificationStatus('verified');
        // 如果已经验证，直接跳转
        setTimeout(() => {
          router.push(`/${locale}/auth/profile-setup`);
        }, 1000);
      } else {
        setVerificationStatus('pending');
      }
    }
  }, [user, loading, locale, router]);

  // 倒计时逻辑
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(prev => prev - 1);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  // 重新发送验证邮件
  const handleResendEmail = async () => {
    if (!user || resendCooldown > 0) return;

    setIsResending(true);
    setErrors({});

    try {
      await sendEmailVerificationToUser(user);
      // 发送成功后开始60秒倒计时
      setResendCooldown(60);
    } catch (error) {
      console.error('Error resending verification email:', error);
      setErrors({ general: t('errorGeneral') });
    } finally {
      setIsResending(false);
    }
  };

  // 手动刷新状态
  const handleManualRefresh = async () => {
    setErrors({});
    setIsChecking(true);
    
    try {
      // 刷新用户状态
      await refreshUser();
      
      // 重新获取当前用户对象
      const currentUser = auth.currentUser;
      if (currentUser) {
        await currentUser.reload();
        if (currentUser.emailVerified) {
          setVerificationStatus('verified');
          
          // 更新 PortalUserAccount 中的邮箱验证状态
          await updateUserAccountEmailVerificationStatus(currentUser.uid, true);
          
          // 延迟跳转到个人资料设置页面
          setTimeout(() => {
            router.push(`/${locale}/auth/profile-setup`);
          }, 2000);
        }
      }
    } catch (error) {
      console.error('Error checking email verification status:', error);
      setErrors({ general: t('verificationFailed') });
    } finally {
      setIsChecking(false);
    }
  };

  // 加载状态
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#FDECCE] to-[#F2D3A4]">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF]"></div>
      </div>
    );
  }

  // 验证成功状态
  if (verificationStatus === 'verified') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#FDECCE] to-[#F2D3A4]">
        <div className="w-full max-w-md mx-4">
          <div className="backdrop-blur-md bg-white/80 rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
            <div className="p-8 text-center">
              <Link href={`/${locale}`} className="inline-block">
                <div className="w-16 h-16 bg-gradient-to-br from-[#A126FF] to-[#8a20d8] rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 mx-auto shadow-lg">
                  ON
                </div>
              </Link>
              
              {/* 成功图标 */}
              <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('emailVerified')}</h1>
              <p className="text-gray-600 mb-6">{t('redirecting')}</p>
              
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#A126FF] mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#FDECCE] to-[#F2D3A4]">
      <div className="w-full max-w-md mx-4">
        {/* 主验证卡片 */}
        <div className="backdrop-blur-md bg-white/80 rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
          <div className="p-8">
            {/* Logo和标题 */}
            <div className="text-center mb-8">
              <Link href={`/${locale}`} className="inline-block">
                <div className="w-16 h-16 bg-gradient-to-br from-[#A126FF] to-[#8a20d8] rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-6 mx-auto shadow-lg">
                  ON
                </div>
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{t('title')}</h1>
              <p className="text-gray-600 text-sm leading-relaxed">
                {t('subtitle')}
              </p>
              {user?.email && (
                <p className="text-[#A126FF] font-medium mt-2">
                  {user.email}
                </p>
              )}
            </div>
            
            {/* 邮箱图标 */}
            <div className="text-center mb-6">
              <div className="w-24 h-24 bg-[#A126FF]/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-12 h-12 text-[#A126FF]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            
            {/* 说明文字 */}
            <div className="text-center mb-6">
              <p className="text-gray-600 text-sm mb-4">
                {t('message')}
              </p>
              <p className="text-gray-500 text-xs">
                {t('instructions')}
              </p>
            </div>
            
            {/* 状态检查 */}
            {isChecking && (
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 text-blue-700 rounded-xl text-sm text-center">
                <div className="flex items-center justify-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  {t('checkingStatus')}
                </div>
              </div>
            )}
            
            {/* 错误消息 */}
            {errors.general && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-xl text-sm">
                {errors.general}
              </div>
            )}
            

            
            {/* 操作按钮 */}
            <div className="space-y-4">
              <Button
                onClick={handleManualRefresh}
                className="w-full bg-gradient-to-r from-[#A126FF] to-[#8a20d8] hover:from-[#8a20d8] hover:to-[#6d1bb3] text-white font-semibold py-4 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg"
                disabled={isChecking}
              >
                {isChecking ? t('checkingStatus') : t('manualRefresh')}
              </Button>
              
              <Button
                onClick={handleResendEmail}
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 rounded-2xl py-4 transition-all duration-300"
                loading={isResending}
                disabled={isResending || resendCooldown > 0}
              >
                {isResending 
                  ? t('resending') 
                  : resendCooldown > 0 
                    ? `${t('resend')} (${resendCooldown}s)`
                    : t('resend')
                }
              </Button>
            </div>
            
            {/* 返回登录链接 */}
            <div className="text-center mt-8 pt-6 border-t border-gray-200">
              <button 
                onClick={async () => {
                  await signOut();
                  router.push(`/${locale}/auth/login`);
                }}
                className="text-sm text-gray-600 hover:text-gray-900 transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {t('backToLogin')}
              </button>
            </div>
          </div>
        </div>
        
        {/* 页脚 */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-600">
            © {new Date().getFullYear()} OneNata Pet Store. All rights reserved.
          </p>
        </div>
      </div>
    </div>
  );
}
