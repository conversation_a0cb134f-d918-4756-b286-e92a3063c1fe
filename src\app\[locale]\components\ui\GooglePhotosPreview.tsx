'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { FiDownload, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ye } from 'react-icons/fi';
// import GoogleMapsService from '../../../lib/services/google_map_services';
import { downloadAndUploadGooglePhotos, UploadProgress } from "../../../lib/firebase/services";

interface GooglePhotosPreviewProps {
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
    html_attributions: string[];
  }>;
  placeId?: string;
  onPhotosUploaded?: (urls: string[]) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface PhotoWithUrl {
  photo_reference: string;
  photoUrl: string;
  width: number;
  height: number;
  selected: boolean;
  uploaded: boolean;
  uploadedUrl?: string;
  imageLoaded?: boolean;
  imageError?: boolean;
}

const GooglePhotosPreview: React.FC<GooglePhotosPreviewProps> = ({
  photos = [],
  placeId,
  onPhotosUploaded,
  onError,
  className = ''
}) => {
  const t = useTranslations('googlePhotos');
  
  const [photosWithUrls, setPhotosWithUrls] = useState<PhotoWithUrl[]>([]);
  const [isLoadingUrls, setIsLoadingUrls] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{
    completed: number;
    total: number;
    currentProgress: UploadProgress;
  }>({ completed: 0, total: 0, currentProgress: { progress: 0, isUploading: false } });

  // 获取照片URLs
  useEffect(() => {
    const loadPhotoUrls = async () => {
      if (photos.length === 0) return;

      setIsLoadingUrls(true);
      try {
        const photosToProcess = photos.slice(0, 5);
        const photoUrls: Array<{
          photoUrl: string;
          photo_reference: string;
          width: number;
          height: number;
        }> = [];

        for (const photo of photosToProcess) {
          try {
            console.log('Fetching photo URL for reference:', photo.photo_reference);
            
            const response = await fetch('/api/google-maps', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                action: 'get_photo_url',
                params: {
                  photo_reference: photo.photo_reference,
                  maxwidth: '800',
                  maxheight: '600'
                }
              })
            });

            const result = await response.json();
            console.log('Photo API response:', result);
            
            if (result.success && result.data) {
              console.log('Photo URL received:', result.data.photoUrl.substring(0, 100) + '...');
              photoUrls.push({
                photoUrl: result.data.photoUrl,
                photo_reference: photo.photo_reference,
                width: photo.width,
                height: photo.height
              });
            } else {
              console.error('Failed to get photo URL:', result.error);
            }
          } catch (error) {
            console.error(`Error loading photo ${photo.photo_reference}:`, error);
          }
        }
        
        const photosWithUrls = photoUrls.map(photo => ({
          photo_reference: photo.photo_reference,
          photoUrl: photo.photoUrl,
          width: photo.width,
          height: photo.height,
          selected: true, // 默认选中所有照片
          uploaded: false,
          imageLoaded: false,
          imageError: false
        }));
        setPhotosWithUrls(photosWithUrls);
      } catch (error) {
        console.error('Error loading photo URLs:', error);
        onError?.(error instanceof Error ? error.message : 'Failed to load photos');
      } finally {
        setIsLoadingUrls(false);
      }
    };

    loadPhotoUrls();
  }, [photos, onError]);

  // 切换照片选择
  const togglePhotoSelection = (photoReference: string) => {
    setPhotosWithUrls(prev => 
      prev.map(photo => 
        photo.photo_reference === photoReference
          ? { ...photo, selected: !photo.selected }
          : photo
      )
    );
  };

  // 处理图片加载状态
  const handleImageLoad = (photoReference: string) => {
    setPhotosWithUrls(prev => 
      prev.map(photo => 
        photo.photo_reference === photoReference
          ? { ...photo, imageLoaded: true, imageError: false }
          : photo
      )
    );
  };

  const handleImageError = (photoReference: string) => {
    setPhotosWithUrls(prev => 
      prev.map(photo => 
        photo.photo_reference === photoReference
          ? { ...photo, imageLoaded: false, imageError: true }
          : photo
      )
    );
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    const allSelected = photosWithUrls.every(photo => photo.selected);
    setPhotosWithUrls(prev => 
      prev.map(photo => ({ ...photo, selected: !allSelected }))
    );
  };

  // 下载并上传选中的照片
  const handleUploadSelectedPhotos = async () => {
    if (!placeId) {
      onError?.('Store ID is required for uploading photos');
      return;
    }

    const selectedPhotos = photosWithUrls.filter(photo => photo.selected && !photo.uploaded);
    if (selectedPhotos.length === 0) {
      onError?.('Please select at least one photo to upload');
      return;
    }

    setIsUploading(true);
    setUploadProgress({ completed: 0, total: selectedPhotos.length, currentProgress: { progress: 0, isUploading: true } });

    try {
      const photosToUpload = selectedPhotos.map(photo => ({
        photoUrl: photo.photoUrl,
        photo_reference: photo.photo_reference
      }));

      const results = await downloadAndUploadGooglePhotos(
        photosToUpload,
        placeId,
        (completed, total, currentProgress) => {
          setUploadProgress({ completed, total, currentProgress });
        }
      );

      // 更新照片状态为已上传
      setPhotosWithUrls(prev => 
        prev.map(photo => {
          const result = results.find(r => r.fileName.includes(photo.photo_reference.substring(0, 10)));
          if (result && photo.selected) {
            return { ...photo, uploaded: true, uploadedUrl: result.url };
          }
          return photo;
        })
      );

      // 通知父组件上传的照片URLs
      const uploadedUrls = results.map(result => result.url);
      onPhotosUploaded?.(uploadedUrls);

    } catch (error) {
      console.error('Error uploading photos:', error);
      onError?.(error instanceof Error ? error.message : 'Failed to upload photos');
    } finally {
      setIsUploading(false);
      setUploadProgress({ completed: 0, total: 0, currentProgress: { progress: 0, isUploading: false } });
    }
  };

  if (photos.length === 0) {
    return null;
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-medium text-gray-900">
          📸 {t('title')}
        </h4>
        <div className="flex items-center space-x-2">
          <button
            onClick={toggleSelectAll}
            className="text-sm text-purple-600 hover:text-purple-700"
            disabled={isUploading}
          >
            {photosWithUrls.every(photo => photo.selected) ? t('deselectAll') : t('selectAll')}
          </button>
          <button
            onClick={handleUploadSelectedPhotos}
            disabled={isUploading || photosWithUrls.filter(p => p.selected && !p.uploaded).length === 0 || !placeId}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center text-sm"
          >
            {isUploading ? (
              <>
                <FiLoader className="w-4 h-4 animate-spin mr-2" />
                {t('uploading')}
              </>
            ) : (
              <>
                <FiDownload className="w-4 h-4 mr-2" />
                {t('uploadSelected')} ({photosWithUrls.filter(p => p.selected && !p.uploaded).length})
              </>
            )}
          </button>
        </div>
      </div>

      <p className="text-sm text-gray-600">{t('description')}</p>

      {/* 上传进度 */}
      {isUploading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-blue-800">{t('uploadProgress')}</span>
            <span className="text-sm text-blue-600">
              {uploadProgress.completed}/{uploadProgress.total} - {uploadProgress.currentProgress.progress}%
            </span>
          </div>
          <div className="w-full bg-blue-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${uploadProgress.total > 0 ? 
                  ((uploadProgress.completed + uploadProgress.currentProgress.progress / 100) / uploadProgress.total) * 100 
                  : 0}%` 
              }}
            />
          </div>
        </div>
      )}

      {/* 照片网格 */}
      {isLoadingUrls ? (
        <div className="flex items-center justify-center py-8">
          <FiLoader className="w-6 h-6 animate-spin text-purple-600" />
          <span className="ml-2 text-gray-600">{t('loadingPhotos')}</span>
        </div>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {photosWithUrls.map((photo) => (
            <div
              key={photo.photo_reference}
              className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                photo.selected 
                  ? 'border-purple-500 ring-2 ring-purple-200' 
                  : 'border-gray-200 hover:border-purple-300'
              }`}
              onClick={() => !isUploading && togglePhotoSelection(photo.photo_reference)}
            >
              <div className="aspect-square relative">
                <Image
                  src={photo.photoUrl}
                  alt={`Google Place Photo ${photo.photo_reference}`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 20vw"
                  onError={() => handleImageError(photo.photo_reference)}
                  onLoad={() => handleImageLoad(photo.photo_reference)}
                />
                
                {/* 错误占位符 */}
                {!photo.imageLoaded && !photo.imageError && (
                  <div className="absolute inset-0 bg-gray-200 flex items-center justify-center text-gray-500 text-sm">
                    <FiLoader className="w-4 h-4 animate-spin" />
                  </div>
                )}
                
                {photo.imageError && (
                  <div className="absolute inset-0 bg-gray-200 flex items-center justify-center text-gray-500 text-sm">
                    <span>Photo unavailable</span>
                  </div>
                )}

                {/* 选择覆盖层 */}
                <div className={`absolute inset-0 flex items-center justify-center transition-all duration-200 ${
                  photo.selected ? 'bg-purple-500 bg-opacity-30' : 'bg-black bg-opacity-0 group-hover:bg-opacity-20'
                }`}>
                  {photo.uploaded ? (
                    <div className="bg-green-500 rounded-full p-2">
                      <FiCheck className="w-4 h-4 text-white" />
                    </div>
                  ) : photo.selected ? (
                    <div className="bg-purple-500 rounded-full p-2">
                      <FiCheck className="w-4 h-4 text-white" />
                    </div>
                  ) : (
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <FiEye className="w-6 h-6 text-white" />
                    </div>
                  )}
                </div>

                {/* 状态标识 */}
                <div className="absolute top-2 right-2">
                  {photo.uploaded && (
                    <div className="bg-green-500 text-white text-xs px-2 py-1 rounded">
                      {t('uploaded')}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 已上传照片统计 */}
      {photosWithUrls.some(p => p.uploaded) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <p className="text-sm text-green-700">
            ✅ {t('uploadedCount', { count: photosWithUrls.filter(p => p.uploaded).length })}
          </p>
        </div>
      )}
    </div>
  );
};

export default GooglePhotosPreview; 