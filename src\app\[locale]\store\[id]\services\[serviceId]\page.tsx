/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { StoreHeader } from '../../../../components/ui/StoreHeader';
import { Button } from '../../../../components/ui/Button';
// import { useAuth } from '../../../../../lib/firebase/context/AuthContext';      
import { 
  ServiceCategory,
  StoreServiceStatus
} from '../../../../../lib/models/types';
import { 
  StaffMember
} from '../../../../../lib/services/staff_services';
import {
  StoreServiceData
} from '../../../../../lib/services/store_services';
import storeService from '../../../../../lib/services/store_services';
import staffService from '../../../../../lib/services/staff_services';
// import appointmentService from '../../../../../lib/services/appointment_service';
import { 
  FiArrowLeft,
  FiUsers,
  FiCalendar,
  FiDollarSign,
  FiTrendingUp,
  FiEdit3,
  FiSettings,
  FiCheck,
  FiAlertCircle,
  FiStar,
  FiUser,
  FiMail,
  FiPhone,
  FiBarChart
} from 'react-icons/fi';

interface ServiceDetailData extends StoreServiceData {
  providerNames?: string[];
  providerEmails?: string[];
  staffDetails?: StaffMember[];
  monthlyStats?: {
    totalBookings: number;
    completedBookings: number;
    revenue: number;
    averageRating: number;
  };
}

export default function ServiceDetailPage() {
  const params = useParams();
  const router = useRouter();
  // const { user } = useAuth();
  const t = useTranslations('serviceDetail');
  const storeId = params.id as string;
  const serviceId = params.serviceId as string;
  
  const [service, setService] = useState<ServiceDetailData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'staff' | 'bookings' | 'settings'>('overview');
  // const [showHolidayModal, setShowHolidayModal] = useState(false);
  // const [showEditModal, setShowEditModal] = useState(false);

  useEffect(() => {
    loadServiceDetail();
  }, [storeId, serviceId]);

  const loadServiceDetail = async () => {
    try {
      setLoading(true);
      
      // Load service data
      const serviceResult = await storeService.getStoreServiceById(storeId, serviceId);
      if (!serviceResult.success || !serviceResult.data) {
        console.error('Failed to load service:', serviceResult.error);
        return;
      }

      // Load staff details
      const staffResult = await staffService.getStoreStaff(storeId);
      const staffDetails = staffResult.success && staffResult.data 
        ? staffResult.data.filter(staff => serviceResult.data!.staffIds.includes(staff.userData.uid))
        : [];

      // Load monthly stats (mock data for now)
      const monthlyStats = {
        totalBookings: 45,
        completedBookings: 38,
        revenue: 2850.50,
        averageRating: 4.6
      };

      // Load recent bookings (commented out for now)
      // const bookingsResult = await appointmentService.getStoreAppointments(storeId, {
      //   limit: 10
      // });

      setService({
        ...serviceResult.data,
        staffDetails,
        monthlyStats,
        providerNames: staffDetails.map(staff => 
          staff.userData.displayName || `${staff.userData.firstName} ${staff.userData.lastName}`
        ),
        providerEmails: staffDetails.map(staff => staff.email).filter((email): email is string => Boolean(email))
      });
    } catch (error) {
      console.error('Load service detail error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategoryName = (category: ServiceCategory) => {
    switch (category) {
      case ServiceCategory.GROOMING:
        return t('categories.grooming');
      case ServiceCategory.VETERINARY:
        return t('categories.veterinary');
      case ServiceCategory.BOARDING:
        return t('categories.boarding');
      case ServiceCategory.TRAINING:
        return t('categories.training');
      case ServiceCategory.WASH:
        return t('categories.wash');
      default:
        return t('categories.other');
    }
  };

  const getCategoryColor = (category: ServiceCategory) => {
    switch (category) {
      case ServiceCategory.GROOMING:
        return 'bg-blue-100 text-blue-800';
      case ServiceCategory.VETERINARY:
        return 'bg-red-100 text-red-800';
      case ServiceCategory.BOARDING:
        return 'bg-green-100 text-green-800';
      case ServiceCategory.TRAINING:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCommission = (commission: number) => {
    return `${(commission * 100).toFixed(1)}%`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="services" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-slate-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-64 bg-slate-200 rounded-xl"></div>
                <div className="h-96 bg-slate-200 rounded-xl"></div>
              </div>
              <div className="space-y-6">
                <div className="h-48 bg-slate-200 rounded-xl"></div>
                <div className="h-64 bg-slate-200 rounded-xl"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="services" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <FiAlertCircle className="w-16 h-16 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              {t('serviceNotFound')}
            </h3>
            <p className="text-slate-600 mb-6">
              {t('serviceNotFoundDesc')}
            </p>
            <Button
              onClick={() => router.back()}
              className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              {t('goBack')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('title')} storeId={storeId} currentPage="services" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.back()}
                variant="secondary"
                className="p-2"
              >
                <FiArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                  <FiSettings className="w-8 h-8 mr-3 text-violet-600" />
                  {getCategoryName(service.serviceCategory)}
                </h1>
                <p className="text-slate-600 mt-1">
                  {t('serviceId')}: {service.serviceId}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(service.serviceCategory)}`}>
                {getCategoryName(service.serviceCategory)}
              </span>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                service.status === StoreServiceStatus.ACTIVE 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {service.status === StoreServiceStatus.ACTIVE ? t('active') : t('inactive')}
              </span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-2xl shadow-lg mb-6">
          <div className="border-b border-slate-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: t('tabs.overview'), icon: FiBarChart },
                { id: 'staff', label: t('tabs.staff'), icon: FiUsers },
                { id: 'bookings', label: t('tabs.bookings'), icon: FiCalendar },
                { id: 'settings', label: t('tabs.settings'), icon: FiSettings }
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-violet-500 text-violet-600'
                        : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-100 text-sm font-medium">{t('stats.totalBookings')}</p>
                        <p className="text-2xl font-bold">{service.totalBookings}</p>
                      </div>
                      <FiCalendar className="w-8 h-8 text-blue-200" />
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-100 text-sm font-medium">{t('stats.completedBookings')}</p>
                        <p className="text-2xl font-bold">{service.completedBookings}</p>
                      </div>
                      <FiCheck className="w-8 h-8 text-green-200" />
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-purple-100 text-sm font-medium">{t('stats.commission')}</p>
                        <p className="text-2xl font-bold">{formatCommission(service.commission)}</p>
                      </div>
                      <FiDollarSign className="w-8 h-8 text-purple-200" />
                    </div>
                  </div>
                  
                  <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-6 text-white">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-orange-100 text-sm font-medium">{t('stats.monthlyRevenue')}</p>
                        <p className="text-2xl font-bold">{formatCurrency(service.monthlyStats?.revenue || 0)}</p>
                      </div>
                      <FiTrendingUp className="w-8 h-8 text-orange-200" />
                    </div>
                  </div>
                </div>

                {/* Service Info */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-slate-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-slate-900 mb-4">{t('serviceInfo')}</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('category')}:</span>
                        <span className="font-medium">{getCategoryName(service.serviceCategory)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('breed')}:</span>
                        <span className="font-medium">{service.serviceBreed}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('onlineBooking')}:</span>
                        <span className={`font-medium ${service.isOnlineBookingEnabled ? 'text-green-600' : 'text-red-600'}`}>
                          {service.isOnlineBookingEnabled ? t('enabled') : t('disabled')}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('requiresApproval')}:</span>
                        <span className={`font-medium ${service.requiresApproval ? 'text-orange-600' : 'text-green-600'}`}>
                          {service.requiresApproval ? t('yes') : t('no')}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-slate-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-slate-900 mb-4">{t('monthlyPerformance')}</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('totalBookings')}:</span>
                        <span className="font-medium">{service.monthlyStats?.totalBookings || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('completedBookings')}:</span>
                        <span className="font-medium">{service.monthlyStats?.completedBookings || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('revenue')}:</span>
                        <span className="font-medium">{formatCurrency(service.monthlyStats?.revenue || 0)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-slate-600">{t('averageRating')}:</span>
                        <span className="font-medium flex items-center">
                          {service.monthlyStats?.averageRating || 0}
                          <FiStar className="w-4 h-4 text-yellow-400 ml-1" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {service.description && (
                  <div className="bg-slate-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-slate-900 mb-4">{t('description')}</h3>
                    <p className="text-slate-700">{service.description}</p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'staff' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-slate-900">{t('staffMembers')}</h3>
                  <span className="text-sm text-slate-500">
                    {service.staffDetails?.length || 0} {t('staffMembers')}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {service.staffDetails?.map((staff) => (
                    <div key={staff.userData.uid} className="bg-white border border-slate-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-purple-600 rounded-full flex items-center justify-center">
                          <FiUser className="w-6 h-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-slate-900">
                            {staff.userData.displayName || `${staff.userData.firstName} ${staff.userData.lastName}`}
                          </h4>
                          <p className="text-sm text-slate-500">{staff.email}</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center text-sm text-slate-600">
                          <FiPhone className="w-4 h-4 mr-2" />
                          {staff.userData.phoneNumber || t('noPhone')}
                        </div>
                        <div className="flex items-center text-sm text-slate-600">
                          <FiMail className="w-4 h-4 mr-2" />
                          {staff.email}
                        </div>
                        <div className="flex items-center text-sm text-slate-600">
                          <FiCalendar className="w-4 h-4 mr-2" />
                          {t('joined')}: {staff.userData.create_date ? new Date(staff.userData.create_date).toLocaleDateString() : t('unknown')}
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-slate-200">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-600">{t('status')}:</span>
                          <span className={`font-medium ${staff.isActive ? 'text-green-600' : 'text-red-600'}`}>
                            {staff.isActive ? t('active') : t('inactive')}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'bookings' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-slate-900">{t('recentBookings')}</h3>
                  <Button
                    onClick={() => router.push(`/store/${storeId}/appointments`)}
                    className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
                  >
                    <FiCalendar className="w-4 h-4 mr-2" />
                    {t('viewAllBookings')}
                  </Button>
                </div>

                <div className="bg-slate-50 rounded-xl p-6">
                  <p className="text-slate-600 text-center">
                    {t('bookingFeatureComingSoon')}
                  </p>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-slate-900">{t('serviceSettings')}</h3>
                  <Button
                    onClick={() => router.push(`/store/${storeId}/services/${serviceId}/edit`)}
                    className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
                  >
                    <FiEdit3 className="w-4 h-4 mr-2" />
                    {t('editService')}
                  </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-slate-50 rounded-xl p-6">
                    <h4 className="font-semibold text-slate-900 mb-4">{t('holidayManagement')}</h4>
                    <p className="text-slate-600 mb-4">{t('holidayManagementDesc')}</p>
                    <Button
                    //   onClick={() => setShowHolidayModal(true)}
                      variant="secondary"
                    >
                      <FiCalendar className="w-4 h-4 mr-2" />
                      {t('manageHolidays')}
                    </Button>
                  </div>

                  <div className="bg-slate-50 rounded-xl p-6">
                    <h4 className="font-semibold text-slate-900 mb-4">{t('serviceStatus')}</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-slate-600">{t('currentStatus')}:</span>
                        <span className={`font-medium ${service.status === StoreServiceStatus.ACTIVE ? 'text-green-600' : 'text-red-600'}`}>
                          {service.status === StoreServiceStatus.ACTIVE ? t('active') : t('inactive')}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-600">{t('onlineBooking')}:</span>
                        <span className={`font-medium ${service.isOnlineBookingEnabled ? 'text-green-600' : 'text-red-600'}`}>
                          {service.isOnlineBookingEnabled ? t('enabled') : t('disabled')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 