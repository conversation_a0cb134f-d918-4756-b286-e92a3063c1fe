/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import { Button } from '@/app/[locale]/components/ui/Button';
import { Input } from '@/app/[locale]/components/ui/Input';
import { PhoneNumberInput, validatePhoneNumber } from '@/app/[locale]/components/ui/PhoneNumberInput';
import { useAuth, AuthenticatedRoute } from '../../../../lib/firebase/context/AuthContext';
import staffService, { 
  StaffMember, 
  CreateStaffData, 
  UpdateStaffData, 
  UpdateScheduleData,
  CreateStaffServiceData
} from '../../../../lib/services/staff_services';
import { ProgressCallback } from '../../../../lib/types/common';
import {
  StoreRole,
  WorkTime,
  ServiceCategory,
  ServiceBreed,
  Currency
} from '../../../../lib/models/types';
import { EmployeeService } from '../../../../lib/models/portal-user';
import { storeService } from '@/app/lib/services/store_services';
import { formatDateTime } from '../../../../lib/utils/validations';
import { StoreHeader } from '../../../components/ui/StoreHeader';
import { 
  FiPlus, 
  FiEdit3,
  FiTrash2,
  FiPhone,
  FiCalendar,
  FiUsers,
  FiSearch,
  FiMoreVertical,
  FiUserCheck,
  FiUserX,
  FiClock,
  FiSettings,
  FiX,
  FiEye,
  FiUser,
  FiTool
} from 'react-icons/fi';

// 员工创建模态框
interface CreateStaffModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (staffData: CreateStaffData, onProgress?: ProgressCallback) => Promise<void>;
  isLoading: boolean;
}

function CreateStaffModal({ isOpen, onClose, onSubmit, isLoading }: CreateStaffModalProps) {
  const t = useTranslations('storeStaff');
  const [formData, setFormData] = useState<CreateStaffData>({
    email: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
    role: StoreRole.STORE_STAFF,
    note: '',
    password: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [progress, setProgress] = useState({ step: 0, total: 0, message: '' });
  const [showProgress, setShowProgress] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.email.trim()) {
      newErrors.email = t('form.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('form.emailInvalid');
    }
    
    if (!formData.firstName.trim()) {
      newErrors.firstName = t('form.firstNameRequired');
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = t('form.lastNameRequired');
    }

    if (formData.phoneNumber && formData.phoneNumber.trim() !== '+1 ' && !validatePhoneNumber(formData.phoneNumber)) {
      newErrors.phoneNumber = t('form.phoneInvalid');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setShowProgress(true);
    setProgress({ step: 0, total: 8, message: 'Creating staff account...' });

    const progressCallback: ProgressCallback = (step, total, message) => {
      setProgress({ step, total, message });
    };

    try {
      await onSubmit(formData, progressCallback);
      
      // 显示完成状态
      setProgress({ step: 8, total: 8, message: 'Staff created successfully!' });
      
      // 2秒后关闭
      setTimeout(() => {
        setShowProgress(false);
        setProgress({ step: 0, total: 0, message: '' });
        
        // 重置表单
        setFormData({
          email: '',
          firstName: '',
          lastName: '',
          phoneNumber: '',
          role: StoreRole.STORE_STAFF,
          note: '',
          password: ''
        });
        setErrors({});
      }, 2000);
      
    } catch (err) {
      console.error('Error creating staff:', err);
      setShowProgress(false);
      setProgress({ step: 0, total: 0, message: '' });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">{t('createStaffAccount')}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isLoading || showProgress}
          >
            <FiX size={24} />
          </button>
        </div>

        {/* 进度条显示 */}
        {showProgress && (
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-900">Create Progress</span>
              <span className="text-sm text-blue-700">{progress.step}/{progress.total}</span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2 mb-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(progress.step / progress.total) * 100}%` }}
              ></div>
            </div>
            <p className="text-sm text-blue-800">{progress.message}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('form.email')} *
            </label>
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              error={errors.email}
              placeholder={t('form.emailPlaceholder')}
              disabled={isLoading || showProgress}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('form.firstName')} *
              </label>
              <Input
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                error={errors.firstName}
                placeholder={t('form.firstNamePlaceholder')}
                disabled={isLoading || showProgress}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('form.lastName')} *
              </label>
              <Input
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                error={errors.lastName}
                placeholder={t('form.lastNamePlaceholder')}
                disabled={isLoading || showProgress}
              />
            </div>
          </div>

          <PhoneNumberInput
            label={t('form.phoneNumber')}
            value={formData.phoneNumber || ''}
            onChange={(value) => setFormData({ ...formData, phoneNumber: value })}
            error={errors.phoneNumber}
            placeholder={t('form.phoneNumberPlaceholder')}
            disabled={isLoading || showProgress}
          />

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('form.role')}
            </label>
            <select
              value={formData.role}
              onChange={(e) => setFormData({ ...formData, role: e.target.value as StoreRole })}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={isLoading || showProgress}
            >
              <option value={StoreRole.STORE_STAFF}>{t('roles.staff')}</option>
              <option value={StoreRole.STORE_ADMIN}>{t('roles.admin')}</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('form.tempPassword')}
            </label>
            <Input
              type="password"
              value={formData.password || ''}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              placeholder={t('form.tempPasswordPlaceholder')}
              disabled={isLoading || showProgress}
            />
            <p className="text-sm text-gray-500 mt-1">
              {t('form.tempPasswordNote')}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('form.note')}
            </label>
            <textarea
              value={formData.note || ''}
              onChange={(e) => setFormData({ ...formData, note: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              rows={3}
              placeholder={t('form.notePlaceholder')}
              disabled={isLoading || showProgress}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading || showProgress}
            >
              {t('form.cancel')}
            </Button>
            <Button
              type="submit"
              loading={isLoading || showProgress}
              disabled={showProgress}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {showProgress ? '创建中...' : t('form.create')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 服务创建/编辑模态框组件
interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (serviceData: CreateStaffServiceData) => Promise<void>;
  service?: EmployeeService;
  isLoading: boolean;
}

function ServiceModal({ isOpen, onClose, onSubmit, service, isLoading }: ServiceModalProps) {
  const t = useTranslations('storeStaff');
  const [formData, setFormData] = useState<CreateStaffServiceData>({
    serviceName: '',
    serviceCategory: 'grooming' as ServiceCategory,
    serviceBreed: 'dog' as ServiceBreed,
    serviceDuration: 60,
    serviceAmount: 0,
    serviceAmountCurrency: 'CAD' as Currency,
    servicePhotos: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 当模态框打开或服务数据变化时，重置表单
  useEffect(() => {
    if (isOpen) {
      if (service) {
        // 编辑模式：填充现有数据
        setFormData({
          serviceName: service.serviceName,
          serviceCategory: service.serviceCategory,
          serviceBreed: service.serviceBreed,
          serviceDuration: service.serviceDuration,
          serviceAmount: service.serviceAmount,
          serviceAmountCurrency: service.serviceAmountCurrency,
          servicePhotos: service.servicePhotos
        });
      } else {
        // 创建模式：重置为默认值
        setFormData({
          serviceName: '',
          serviceCategory: 'grooming' as ServiceCategory,
          serviceBreed: 'dog' as ServiceBreed,
          serviceDuration: 60,
          serviceAmount: 0,
          serviceAmountCurrency: 'CAD' as Currency,
          servicePhotos: []
        });
      }
      setErrors({});
    }
  }, [isOpen, service]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.serviceName.trim()) {
      newErrors.serviceName = t('serviceForm.serviceNameRequired');
    }
    
    if (!formData.serviceDuration || formData.serviceDuration < 1) {
      newErrors.serviceDuration = t('serviceForm.durationMinimum');
    }
    
    if (!formData.serviceAmount || formData.serviceAmount <= 0) {
      newErrors.serviceAmount = t('serviceForm.amountMinimum');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting service:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[60] p-4">
      <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/30 w-full max-w-2xl max-h-[90vh] overflow-hidden">
        <div className="h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-400"></div>
        
        <div className="flex justify-between items-center p-6 border-b border-gray-200/50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg flex items-center justify-center">
              <FiTool className="text-white" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {service ? t('editService') : t('createService')}
              </h2>
              <p className="text-sm text-gray-600">
                {service ? t('editService') : t('createService')}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100/80 transition-colors text-gray-400 hover:text-gray-600"
            disabled={isLoading}
          >
            <FiX size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-160px)]">
          {/* 服务名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('serviceForm.serviceName')} *
            </label>
            <Input
              type="text"
              value={formData.serviceName}
              onChange={(e) => setFormData({ ...formData, serviceName: e.target.value })}
              error={errors.serviceName}
              placeholder={t('serviceForm.serviceNamePlaceholder')}
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* 服务类别 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('serviceForm.serviceCategory')} *
              </label>
              <select
                value={formData.serviceCategory}
                onChange={(e) => setFormData({ ...formData, serviceCategory: e.target.value as ServiceCategory })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/80"
                disabled={isLoading}
              >
                <option value="grooming">{t('serviceCategories.grooming')}</option>
                <option value="boarding">{t('serviceCategories.boarding')}</option>
                <option value="veterinary">{t('serviceCategories.veterinary')}</option>
                <option value="training">{t('serviceCategories.training')}</option>
                <option value="wash">{t('serviceCategories.wash')}</option>
                <option value="other">{t('serviceCategories.other')}</option>
              </select>
            </div>

            {/* 宠物类型 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('serviceForm.serviceBreed')} *
              </label>
              <select
                value={formData.serviceBreed}
                onChange={(e) => setFormData({ ...formData, serviceBreed: e.target.value as ServiceBreed })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/80"
                disabled={isLoading}
              >
                <option value="dog">{t('serviceBreeds.dog')}</option>
                <option value="cat">{t('serviceBreeds.cat')}</option>
                <option value="other">{t('serviceBreeds.other')}</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            {/* 服务时长 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('serviceForm.serviceDuration')} *
              </label>
              <Input
                type="number"
                min="1"
                value={formData.serviceDuration}
                onChange={(e) => setFormData({ ...formData, serviceDuration: parseInt(e.target.value) || 0 })}
                error={errors.serviceDuration}
                disabled={isLoading}
              />
            </div>

            {/* 服务价格 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('serviceForm.serviceAmount')} *
              </label>
              <Input
                type="number"
                min="0"
                step="0.01"
                value={formData.serviceAmount}
                onChange={(e) => setFormData({ ...formData, serviceAmount: parseFloat(e.target.value) || 0 })}
                error={errors.serviceAmount}
                disabled={isLoading}
              />
            </div>

            {/* 货币类型 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('serviceForm.serviceAmountCurrency')} *
              </label>
              <select
                value={formData.serviceAmountCurrency}
                onChange={(e) => setFormData({ ...formData, serviceAmountCurrency: e.target.value as Currency })}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/80"
                disabled={isLoading}
              >
                <option value="CAD">{t('serviceCurrency.CAD')}</option>
                <option value="USD">{t('serviceCurrency.USD')}</option>
              </select>
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200/50">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              className="border-gray-300 hover:bg-gray-50/80"
            >
              {t('serviceForm.cancel')}
            </Button>
            <Button
              type="submit"
              loading={isLoading}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all"
            >
              {service ? t('serviceForm.update') : t('serviceForm.create')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

// 员工卡片组件
interface StaffCardProps {
  staff: StaffMember;
  onEdit: (staff: StaffMember) => void;
  onDelete: (staffId: string) => void;
  onToggleActive: (staffId: string, active: boolean) => void;
  onViewDetails: (staff: StaffMember) => void;
}

function StaffCard({ staff, onEdit, onDelete, onToggleActive, onViewDetails }: StaffCardProps) {
  const t = useTranslations('storeStaff');
  const [showMenu, setShowMenu] = useState(false);

  const getRoleColor = (role: StoreRole) => {
    switch (role) {
      case StoreRole.STORE_OWNER:
        return 'bg-purple-100 text-purple-800';
      case StoreRole.STORE_ADMIN:
        return 'bg-blue-100 text-blue-800';
      case StoreRole.STORE_STAFF:
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleName = (role: StoreRole) => {
    switch (role) {
      case StoreRole.STORE_OWNER:
        return t('roles.owner');
      case StoreRole.STORE_ADMIN:
        return t('roles.admin');
      case StoreRole.STORE_STAFF:
        return t('roles.staff');
      default:
        return t('roles.staff');
    }
  };

  const formatDate = (date: Date | string | number | null | undefined) => {
    if (!date) return '-';
    return formatDateTime(date, 'date');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
            <FiUser className="text-purple-600" size={20} />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">
              {staff.userData.displayName}
            </h3>
            <p className="text-sm text-gray-500">{staff.email}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getRoleColor(staff.staffInfo.role)}`}>
                {getRoleName(staff.staffInfo.role)}
              </span>
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                staff.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {staff.isActive ? t('active') : t('inactive')}
              </span>
            </div>
          </div>
        </div>

        <div className="relative">
          <button
            onClick={() => setShowMenu(!showMenu)}
            className="text-gray-400 hover:text-gray-600 p-1"
          >
            <FiMoreVertical size={16} />
          </button>

          {showMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-10">
              <div className="py-1">
                <button
                  onClick={() => {
                    onViewDetails(staff);
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <FiEye className="mr-2" size={14} />
                  {t('viewDetails')}
                </button>
                <button
                  onClick={() => {
                    onEdit(staff);
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  <FiEdit3 className="mr-2" size={14} />
                  {t('editInfo')}
                </button>
                <button
                  onClick={() => {
                    onToggleActive(staff.userData.uid, !staff.isActive);
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                >
                  {staff.isActive ? (
                    <>
                      <FiUserX className="mr-2" size={14} />
                      {t('deactivate')}
                    </>
                  ) : (
                    <>
                      <FiUserCheck className="mr-2" size={14} />
                      {t('activate')}
                    </>
                  )}
                </button>
                <button
                  onClick={() => {
                    onDelete(staff.userData.uid);
                    setShowMenu(false);
                  }}
                  className="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                >
                  <FiTrash2 className="mr-2" size={14} />
                  {t('deleteStaff')}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
        <div className="flex items-center text-gray-600">
          <FiPhone className="mr-2" size={14} />
          <span>{staff.userData.phoneNumber || t('stats.notSet')}</span>
        </div>
        <div className="flex items-center text-gray-600">
          <FiCalendar className="mr-2" size={14} />
          <span>{t('stats.joinDate')}: {formatDate(staff.staffInfo.startTime)}</span>
        </div>
        <div className="flex items-center text-gray-600">
          <FiClock className="mr-2" size={14} />
          <span>{t('stats.scheduleStatus')}: {staff.schedule?.active ? t('schedule.set') : t('schedule.notSet')}</span>
        </div>
        <div className="flex items-center text-gray-600">
          <FiTool className="mr-2" size={14} />
          <span>{t('stats.serviceCount')}: {staff.services?.length || 0}</span>
        </div>
      </div>

      {staff.staffInfo.note && (
        <div className="mt-3 p-2 bg-gray-50 rounded text-sm text-gray-600">
          <strong>{t('form.note')}:</strong> {staff.staffInfo.note}
        </div>
      )}
    </div>
  );
}

// 员工详情模态框
interface StaffDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  staff: StaffMember | null;
  storeId: string;
}

function StaffDetailsModal({ isOpen, onClose, staff, storeId }: StaffDetailsModalProps) {
  const t = useTranslations('storeStaff');
  const { user, userData } = useAuth();
  const [activeTab, setActiveTab] = useState<'info' | 'schedule' | 'services'>('info');
  const [isEditing, setIsEditing] = useState(false);
  const [scheduleData, setScheduleData] = useState<WorkTime[]>([]);
  const [services, setServices] = useState<EmployeeService[]>([]);
  const [loading, setLoading] = useState(false);
  const [storeDetails, setStoreDetails] = useState<any>(null);
  const [timeValidationErrors, setTimeValidationErrors] = useState<Record<string, string>>({});
  
  // 服务管理状态
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [editingService, setEditingService] = useState<EmployeeService | undefined>(undefined);
  const [serviceLoading, setServiceLoading] = useState(false);
  const [servicesLoading, setServicesLoading] = useState(false);

  useEffect(() => {
    if (staff && isOpen) {
      setScheduleData(staff.schedule?.workTimes || []);
      loadStoreDetails();
      loadStaffServices();
    }
  }, [staff, isOpen]);

  // 在店铺详情加载完成后，检查并自动取消无效排班（只执行一次）
  useEffect(() => {
    if (storeDetails?.businessHours && scheduleData.length > 0) {
      checkAndRemoveInvalidSchedules();
    }
  }, [storeDetails]); // 只监听 storeDetails 的变化

  // 获取店铺详情
  const loadStoreDetails = async () => {
    try {
      const result = await storeService.getStoreDetails(storeId);
      if (result.success && result.data) {
        setStoreDetails(result.data);
        console.log('Store business hours:', result.data.businessHours);
      }
    } catch (error) {
      console.error('Error loading store details:', error);
    }
  };

  // 加载员工服务列表
  const loadStaffServices = async () => {
    if (!staff) return;
    
    setServicesLoading(true);
    try {
      const result = await staffService.getStaffServices(staff);
      if (result.success && result.data) {
        setServices(result.data);
        console.log('staff services:', result.data);
      }
    } catch (error) {
      console.error('Error loading staff services:', error);
    } finally {
      setServicesLoading(false);
    }
  };

  // 创建服务
  const handleCreateService = async (serviceData: CreateStaffServiceData) => {
    if (!staff || !userData?.uid) return;
    
    setServiceLoading(true);
    try {
      const result = await staffService.addStaffService(staff, serviceData, userData.uid!);
      if (result.success) {
        alert(t('serviceCreated'));
        setShowServiceModal(false);
        setEditingService(undefined);
        await loadStaffServices(); // 重新加载服务列表
      } else {
        alert(t('serviceCreateError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error creating service:', error);
      alert(t('serviceCreateError'));
    } finally {
      setServiceLoading(false);
    }
  };

  // 更新服务
  const handleUpdateService = async (serviceData: CreateStaffServiceData) => {
    if (!staff || !user?.uid || !editingService) return;
    
    setServiceLoading(true);
    try {
      const result = await staffService.updateStaffService(
        editingService.sid!,
        staff,
        serviceData,
        user.uid!
      );
      if (result.success) {
        alert(t('serviceUpdated'));
        setShowServiceModal(false);
        setEditingService(undefined);
        await loadStaffServices(); // 重新加载服务列表
      } else {
        alert(t('serviceUpdateError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error updating service:', error);
      alert(t('serviceUpdateError'));
    } finally {
      setServiceLoading(false);
    }
  };

  // 删除服务
  const handleDeleteService = async (service: EmployeeService) => {
    if (!staff || !user?.uid || !service.sid) return;
    
    if (!confirm(t('confirmDeleteService'))) return;
    
    try {
      const result = await staffService.removeStaffService(service.sid, staff, user.uid);
      if (result.success) {
        alert(t('serviceDeleted'));
        await loadStaffServices(); // 重新加载服务列表
      } else {
        alert(t('serviceDeleteError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting service:', error);
      alert(t('serviceDeleteError'));
    }
  };

  // 打开编辑服务模态框
  const handleEditService = (service: EmployeeService) => {
    setEditingService(service);
    setShowServiceModal(true);
  };

  // 服务提交处理
  const handleServiceSubmit = async (serviceData: CreateStaffServiceData) => {
    if (editingService) {
      await handleUpdateService(serviceData);
    } else {
      await handleCreateService(serviceData);
    }
  };

  // 检查并自动取消无效排班（防止循环调用）
  const checkAndRemoveInvalidSchedules = () => {
    if (!storeDetails?.businessHours || scheduleData.length === 0) return;

    let hasChanges = false;
    const updatedScheduleData = scheduleData.filter(workTime => {
      const businessKey = weekdayToBusinessKey(workTime.weekday);
      const businessHour = storeDetails.businessHours[businessKey];
      
      // 如果店铺在这一天不营业，但员工有排班，则移除该排班
      if (!businessHour || businessHour.closed) {
        if (workTime.open) {
          console.log(`🗑️ cancel ${workTime.weekday} ${workTime.startTime} ${workTime.endTime} because store is closed`);
          hasChanges = true;
          return false; // 移除这个排班
        }
      }
      return true; // 保留这个排班
    });

    if (hasChanges) {
      setScheduleData(updatedScheduleData);
      setTimeValidationErrors({}); // 清除错误状态
      // 显示提示信息
      setTimeout(() => {
        alert(t('schedule.autoCancel'));
      }, 500);
    }
  };



  // 时间转换工具函数
  const timeToMinutes = (timeStr: string): number => {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  };



  // 星期映射
  const weekdayToBusinessKey = (weekday: string): string => {
    const mapping: Record<string, string> = {
      'Monday': 'monday',
      'Tuesday': 'tuesday', 
      'Wednesday': 'wednesday',
      'Thursday': 'thursday',
      'Friday': 'friday',
      'Saturday': 'saturday',
      'Sunday': 'sunday'
    };
    return mapping[weekday] || weekday.toLowerCase();
  };

  // 验证员工工作时间是否在营业时间内
  const validateWorkTime = (weekday: string, startTime: string, endTime: string): string | null => {
    if (!storeDetails?.businessHours) {
      return null; // 如果没有营业时间限制，则允许
    }

    const businessKey = weekdayToBusinessKey(weekday);
    const businessHour = storeDetails.businessHours[businessKey];
    
    if (!businessHour || businessHour.closed) {
      return t('schedule.validation.storeClosed', { weekday: getWeekdayName(weekday) });
    }

    const staffStart = timeToMinutes(startTime);
    const staffEnd = timeToMinutes(endTime);
    const businessStart = timeToMinutes(businessHour.open);
    const businessEnd = timeToMinutes(businessHour.close);

    if (staffStart < businessStart) {
      return t('schedule.validation.startTimeBeforeOpen', { 
        startTime: startTime,
        openTime: businessHour.open
      });
    }

    if (staffEnd > businessEnd) {
      return t('schedule.validation.endTimeAfterClose', {
        endTime: endTime,
        closeTime: businessHour.close
      });
    }

    if (staffStart >= staffEnd) {
      return t('schedule.validation.startTimeAfterEnd');
    }

    return null; // 验证通过
  };

  // 获取营业时间限制信息
  const getBusinessHourLimits = (weekday: string) => {
    if (!storeDetails?.businessHours) {
      return null;
    }

    const businessKey = weekdayToBusinessKey(weekday);
    const businessHour = storeDetails.businessHours[businessKey];
    
    if (!businessHour || businessHour.closed) {
      return null;
    }

    return {
      open: businessHour.open,
      close: businessHour.close,
      minTime: businessHour.open,
      maxTime: businessHour.close
    };
  };

  const handleSaveSchedule = async () => {
    if (!staff) return;

    // 验证所有排班时间是否在营业时间内
    const errors: Record<string, string> = {};
    
    for (const workTime of scheduleData) {
      if (workTime.open) {
        const error = validateWorkTime(workTime.weekday, workTime.startTime, workTime.endTime);
        if (error) {
          errors[workTime.weekday] = error;
        }
      }
    }

    if (Object.keys(errors).length > 0) {
      setTimeValidationErrors(errors);
      alert('排班时间设置有误，请检查红色提示信息');
      return;
    }

    setTimeValidationErrors({});
    setLoading(true);
    
    try {
      const updateData: UpdateScheduleData = {
        active: scheduleData.length > 0,
        workTimes: scheduleData
      };
      
      console.log('updateData:', updateData, 'staff: ', staff);

      const result = await staffService.updateStaffSchedule(
        staff,
        updateData,
        userData?.uid
      );

      if (result.success) {
        alert(t('updateSuccess'));
        setIsEditing(false);
      } else {
        alert(t('updateError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error updating schedule:', error);
      alert(t('updateError'));
    } finally {
      setLoading(false);
    }
  };

  const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  const getWeekdayName = (day: string) => {
    return t(`schedule.${day.toLowerCase()}`);
  };

  if (!isOpen || !staff) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/30 w-full max-w-5xl max-h-[90vh] overflow-hidden">
        {/* 头部渐变装饰 */}
        <div className="h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-400"></div>
        
        <div className="flex justify-between items-center p-6 border-b border-gray-200/50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg flex items-center justify-center">
              <FiUser className="text-white" size={20} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {staff.userData.displayName}
              </h2>
              <p className="text-sm text-gray-600">{t('staffDetails')}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100/80 transition-colors text-gray-400 hover:text-gray-600"
          >
            <FiX size={20} />
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="flex bg-gray-50/50 border-b border-gray-200/50">
          <button
            onClick={() => setActiveTab('info')}
            className={`flex-1 px-6 py-4 font-medium text-sm transition-all relative ${
              activeTab === 'info'
                ? 'text-purple-600 bg-white/80 shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-white/40'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <FiUser size={16} />
              <span>{t('basicInfo')}</span>
            </div>
            {activeTab === 'info' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600"></div>
            )}
          </button>
          <button
            onClick={() => setActiveTab('schedule')}
            className={`flex-1 px-6 py-4 font-medium text-sm transition-all relative ${
              activeTab === 'schedule'
                ? 'text-purple-600 bg-white/80 shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-white/40'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <FiClock size={16} />
              <span>{t('scheduleManagement')}</span>
            </div>
            {activeTab === 'schedule' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600"></div>
            )}
          </button>
          <button
            onClick={() => setActiveTab('services')}
            className={`flex-1 px-6 py-4 font-medium text-sm transition-all relative ${
              activeTab === 'services'
                ? 'text-purple-600 bg-white/80 shadow-sm'
                : 'text-gray-600 hover:text-gray-800 hover:bg-white/40'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <FiTool size={16} />
              <span>{t('serviceManagement')}</span>
            </div>
            {activeTab === 'services' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500 to-purple-600"></div>
            )}
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)] bg-gradient-to-br from-gray-50/30 to-white/30">
          {activeTab === 'info' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/40">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                      <FiUser className="text-white" size={16} />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('basicInfo')}</h3>
                  </div>
                  <div className="space-y-4">
                    <div className="p-3 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-lg">
                      <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t('form.firstName')} {t('form.lastName')}</label>
                      <p className="text-gray-900 font-medium mt-1">{staff.userData.displayName}</p>
                    </div>
                    <div className="p-3 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-lg">
                      <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t('form.email')}</label>
                      <p className="text-gray-900 font-medium mt-1">{staff.email}</p>
                    </div>
                    <div className="p-3 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-lg">
                      <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t('form.phoneNumber')}</label>
                      <p className="text-gray-900 font-medium mt-1">{staff.userData.phoneNumber || t('stats.notSet')}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/40">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <FiSettings className="text-white" size={16} />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('workInfo')}</h3>
                  </div>
                  <div className="space-y-4">
                    <div className="p-3 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-lg">
                      <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t('form.role')}</label>
                      <p className="text-gray-900 font-medium mt-1">{staff.staffInfo.role}</p>
                    </div>
                    <div className="p-3 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-lg">
                      <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t('status')}</label>
                      <div className="flex items-center mt-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          staff.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {staff.isActive ? t('active') : t('inactive')}
                        </span>
                      </div>
                    </div>
                    <div className="p-3 bg-gradient-to-r from-gray-50/80 to-white/80 rounded-lg">
                      <label className="text-xs font-medium text-gray-600 uppercase tracking-wide">{t('stats.joinDate')}</label>
                      <p className="text-gray-900 font-medium mt-1">
                        {staff.staffInfo.startTime ? new Date(staff.staffInfo.startTime).toLocaleDateString() : t('stats.notSet')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {staff.staffInfo.note && (
                <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/40">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                      <FiEdit3 className="text-white" size={16} />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('form.note')}</h3>
                  </div>
                  <div className="p-4 bg-gradient-to-r from-orange-50/80 to-yellow-50/80 rounded-lg border border-orange-200/30">
                    <p className="text-gray-700 leading-relaxed">{staff.staffInfo.note}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'schedule' && (
            <div className="space-y-6">
              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/40">
                <div className="flex justify-between items-center mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                      <FiCalendar className="text-white" size={16} />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('scheduleManagement')}</h3>
                  </div>
                  <div className="space-x-2">
                    {isEditing ? (
                      <>
                        <Button
                          onClick={() => setIsEditing(false)}
                          variant="outline"
                          size="sm"
                          className="border-gray-300 hover:bg-gray-50/80"
                        >
                          {t('form.cancel')}
                        </Button>
                        <Button
                          onClick={handleSaveSchedule}
                          loading={loading}
                          size="sm"
                          className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all"
                        >
                          {t('form.save')}
                        </Button>
                      </>
                    ) : (
                      <Button
                        onClick={() => setIsEditing(true)}
                        size="sm"
                        className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all"
                      >
                        <FiEdit3 className="mr-2" size={14} />
                        {t('editSchedule')}
                      </Button>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  {weekdays.map((day) => {
                    const workTime = scheduleData.find(w => w.weekday === day);
                    const isWeekend = ['Saturday', 'Sunday'].includes(day);
                    const businessLimits = getBusinessHourLimits(day);
                    const hasError = timeValidationErrors[day];
                    
                    return (
                      <div key={day} className={`p-4 rounded-xl border transition-all ${
                        hasError 
                          ? 'bg-gradient-to-r from-red-50/80 to-red-100/80 border-red-200' 
                          : workTime?.open 
                            ? 'bg-gradient-to-r from-green-50/80 to-emerald-50/80 border-green-200/50' 
                            : 'bg-gradient-to-r from-gray-50/80 to-white/80 border-gray-200/50'
                      }`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                              isWeekend 
                                ? 'bg-gradient-to-r from-orange-400 to-orange-500' 
                                : 'bg-gradient-to-r from-blue-400 to-blue-500'
                            }`}>
                              <span className="text-white text-xs font-bold">
                                {getWeekdayName(day).substring(0, 2)}
                              </span>
                            </div>
                            <div className="flex flex-col">
                              <span className="text-sm font-medium text-gray-900 min-w-[60px]">
                                {getWeekdayName(day)}
                              </span>
                              {businessLimits && (
                                <span className="text-xs text-gray-500">
                                  {t('schedule.businessHours')}: {businessLimits.open} - {businessLimits.close}
                                </span>
                              )}
                              {!businessLimits && storeDetails && (
                                <span className="text-xs text-red-500">
                                  {t('schedule.storeClosed')}
                                </span>
                              )}
                            </div>
                            {isEditing ? (
                              <div className="flex flex-col space-y-2">
                                <div className="flex items-center space-x-3">
                                  <input
                                    type="checkbox"
                                    checked={workTime?.open || false}
                                    disabled={!businessLimits}
                                    onChange={(e) => {
                                      const newSchedule = [...scheduleData];
                                      const existingIndex = newSchedule.findIndex(w => w.weekday === day);
                                      
                                      if (e.target.checked) {
                                        const defaultStart = businessLimits?.open || '09:00';
                                        const defaultEnd = businessLimits?.close || '17:00';
                                        
                                        const newWorkTime: WorkTime = {
                                          weekday: day as any,
                                          startTime: workTime?.startTime || defaultStart,
                                          endTime: workTime?.endTime || defaultEnd,
                                          open: true
                                        };
                                        
                                        if (existingIndex >= 0) {
                                          newSchedule[existingIndex] = newWorkTime;
                                        } else {
                                          newSchedule.push(newWorkTime);
                                        }
                                      } else {
                                        if (existingIndex >= 0) {
                                          newSchedule.splice(existingIndex, 1);
                                        }
                                      }
                                      
                                      setScheduleData(newSchedule);
                                      // 清除该天的错误
                                      if (timeValidationErrors[day]) {
                                        const newErrors = { ...timeValidationErrors };
                                        delete newErrors[day];
                                        setTimeValidationErrors(newErrors);
                                      }
                                    }}
                                    className="w-4 h-4 text-purple-600 bg-white border-gray-300 rounded focus:ring-purple-500"
                                  />
                                  {workTime?.open && (
                                    <div className="flex items-center space-x-2">
                                      <input
                                        type="time"
                                        value={workTime.startTime}
                                        min={businessLimits?.minTime}
                                        max={businessLimits?.maxTime}
                                        onChange={(e) => {
                                          const newSchedule = [...scheduleData];
                                          const index = newSchedule.findIndex(w => w.weekday === day);
                                          if (index >= 0) {
                                            newSchedule[index].startTime = e.target.value;
                                            setScheduleData(newSchedule);
                                            
                                            // 实时验证
                                            const error = validateWorkTime(day, e.target.value, newSchedule[index].endTime);
                                            const newErrors = { ...timeValidationErrors };
                                            if (error) {
                                              newErrors[day] = error;
                                            } else {
                                              delete newErrors[day];
                                            }
                                            setTimeValidationErrors(newErrors);
                                          }
                                        }}
                                        className={`px-3 py-1.5 bg-white/80 border rounded-lg text-sm focus:ring-2 focus:border-transparent ${
                                          hasError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-purple-500'
                                        }`}
                                      />
                                      <span className="text-gray-400">-</span>
                                      <input
                                        type="time"
                                        value={workTime.endTime}
                                        min={businessLimits?.minTime}
                                        max={businessLimits?.maxTime}
                                        onChange={(e) => {
                                          const newSchedule = [...scheduleData];
                                          const index = newSchedule.findIndex(w => w.weekday === day);
                                          if (index >= 0) {
                                            newSchedule[index].endTime = e.target.value;
                                            setScheduleData(newSchedule);
                                            
                                            // 实时验证
                                            const error = validateWorkTime(day, newSchedule[index].startTime, e.target.value);
                                            const newErrors = { ...timeValidationErrors };
                                            if (error) {
                                              newErrors[day] = error;
                                            } else {
                                              delete newErrors[day];
                                            }
                                            setTimeValidationErrors(newErrors);
                                          }
                                        }}
                                        className={`px-3 py-1.5 bg-white/80 border rounded-lg text-sm focus:ring-2 focus:border-transparent ${
                                          hasError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-purple-500'
                                        }`}
                                      />
                                    </div>
                                  )}
                                </div>
                                {hasError && (
                                  <div className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                                    {hasError}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div className="text-sm">
                                {workTime?.open ? (
                                  <div className="flex items-center space-x-2">
                                    <FiClock className="text-green-600" size={14} />
                                    <span className="text-green-700 font-medium">
                                      {workTime.startTime} - {workTime.endTime}
                                    </span>
                                  </div>
                                ) : (
                                  <div className="flex items-center space-x-2">
                                    <FiX className="text-gray-400" size={14} />
                                    <span className="text-gray-500">{t('schedule.rest')}</span>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {isEditing && (
                  <div className="flex flex-wrap gap-3 mt-6 pt-4 border-t border-gray-200/50">
                    <Button
                      onClick={() => {
                        const weekdayTimes: WorkTime[] = weekdays.slice(0, 5)
                          .map(day => {
                            const limits = getBusinessHourLimits(day);
                            if (!limits) return null; // 跳过不营业的日子
                            
                            return {
                              weekday: day as any,
                              startTime: limits.open,
                              endTime: limits.close,
                              open: true
                            };
                          })
                          .filter((item): item is WorkTime => item !== null);
                        
                        setScheduleData([...scheduleData.filter(w => !weekdays.slice(0, 5).includes(w.weekday)), ...weekdayTimes]);
                        setTimeValidationErrors({}); // 清除错误
                      }}
                      variant="outline"
                      size="sm"
                      className="bg-gradient-to-r from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 border-blue-200 text-blue-700"
                    >
                      <FiCalendar className="mr-2" size={14} />
                      {t('applyToWeekdays')}
                    </Button>
                    <Button
                      onClick={() => {
                        const weekendTimes: WorkTime[] = weekdays.slice(5)
                          .map(day => {
                            const limits = getBusinessHourLimits(day);
                            if (!limits) return null; // 跳过不营业的日子
                            
                            return {
                              weekday: day as any,
                              startTime: limits.open,
                              endTime: limits.close,
                              open: true
                            };
                          })
                          .filter((item): item is WorkTime => item !== null);
                        
                        setScheduleData([...scheduleData.filter(w => !['Saturday', 'Sunday'].includes(w.weekday)), ...weekendTimes]);
                        setTimeValidationErrors({}); // 清除错误
                      }}
                      variant="outline"
                      size="sm"
                      className="bg-gradient-to-r from-orange-50 to-orange-100 hover:from-orange-100 hover:to-orange-200 border-orange-200 text-orange-700"
                    >
                      <FiCalendar className="mr-2" size={14} />
                      {t('applyToWeekends')}
                    </Button>
                    {storeDetails?.businessHours && (
                      <div className="text-xs text-gray-500 flex items-center">
                        <FiClock className="mr-1" size={12} />
                        {t('schedule.quickApplyNote')}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === 'services' && (
            <div className="space-y-6">
              <div className="bg-white/70 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-white/40">
                <div className="flex justify-between items-center mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                      <FiTool className="text-white" size={16} />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900">{t('serviceManagement')}</h3>
                  </div>
                  <Button
                    onClick={() => {
                      setEditingService(undefined); // 切换到创建模式
                      setShowServiceModal(true);
                    }}
                    size="sm"
                    className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all"
                  >
                    <FiPlus className="mr-2" size={16} />
                    {t('addService')}
                  </Button>
                </div>

                {servicesLoading ? (
                  <div className="p-8 text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                    <p className="mt-2 text-gray-600">{t('loadingServices')}</p>
                  </div>
                ) : services.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full flex items-center justify-center mx-auto mb-4">
                      <FiTool className="text-gray-500" size={24} />
                    </div>
                    <p className="text-gray-500 text-lg font-medium mb-2">{t('noServices')}</p>
                    <p className="text-gray-400 text-sm">{t('noServicesNote')}</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {services.map((service) => (
                      <div key={service.sid} className="bg-gradient-to-br from-white/80 to-gray-50/80 backdrop-blur-sm border border-white/50 rounded-xl p-5 shadow-md hover:shadow-lg transition-all">
                        <div className="flex justify-between items-start mb-4">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                              <FiTool className="text-white" size={16} />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900">{service.serviceName}</h4>
                              <p className="text-xs text-gray-500 uppercase tracking-wide">{service.serviceCategory}</p>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            <button 
                              onClick={() => handleEditService(service)}
                              className="w-8 h-8 flex items-center justify-center rounded-lg bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 transition-colors"
                            >
                              <FiEdit3 size={14} />
                            </button>
                            <button 
                              onClick={() => handleDeleteService(service)}
                              className="w-8 h-8 flex items-center justify-center rounded-lg bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-colors"
                            >
                              <FiTrash2 size={14} />
                            </button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="p-2 bg-gradient-to-r from-blue-50/80 to-blue-100/80 rounded-lg">
                            <p className="text-xs text-blue-600 font-medium uppercase tracking-wide">{t('serviceInfo.petType')}</p>
                            <p className="text-blue-900 font-medium">{service.serviceBreed}</p>
                          </div>
                          <div className="p-2 bg-gradient-to-r from-green-50/80 to-green-100/80 rounded-lg">
                            <p className="text-xs text-green-600 font-medium uppercase tracking-wide">{t('serviceInfo.duration')}</p>
                            <p className="text-green-900 font-medium">{service.serviceDuration} {t('serviceInfo.minutes')}</p>
                          </div>
                          <div className="p-2 bg-gradient-to-r from-purple-50/80 to-purple-100/80 rounded-lg">
                            <p className="text-xs text-purple-600 font-medium uppercase tracking-wide">{t('serviceInfo.price')}</p>
                            <p className="text-purple-900 font-medium">{service.serviceAmountCurrency} {service.serviceAmount}</p>
                          </div>
                          <div className="p-2 bg-gradient-to-r from-orange-50/80 to-orange-100/80 rounded-lg">
                            <p className="text-xs text-orange-600 font-medium uppercase tracking-wide">{t('serviceInfo.count')}</p>
                            <p className="text-orange-900 font-medium">{service.serviceCount}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 服务管理模态框 */}
      <ServiceModal
        isOpen={showServiceModal}
        onClose={() => setShowServiceModal(false)}
        onSubmit={handleServiceSubmit}
        service={editingService}
        isLoading={serviceLoading}
      />
    </div>
  );
}

// 主要组件
export default function StoreStaffPage() {
  const { user } = useAuth();
  const t = useTranslations('storeStaff');
  
  if (!user) {
    return <div>{t('pleaseLogin')}</div>;
  }

  return (
    <AuthenticatedRoute>
      <StoreStaffContent />
    </AuthenticatedRoute>
  );
}

function StoreStaffContent() {
  const params = useParams();
  const storeId = params.id as string;
  const { user, userData } = useAuth();
  const t = useTranslations('storeStaff');
  const locale = useLocale();
  
  // 店铺名称
  const storeName = '员工管理';
  
  const [staffList, setStaffList] = useState<StaffMember[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<StoreRole | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  
  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [createLoading, setCreateLoading] = useState(false);

  // 统计信息
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    byRole: {
      [StoreRole.STORE_OWNER]: 0,
      [StoreRole.STORE_ADMIN]: 0,
      [StoreRole.STORE_STAFF]: 0
    }
  });

  // 加载员工列表
  const loadStaff = async () => {
    setLoading(true);
    try {
      const result = await staffService.getStoreStaff(storeId);
      if (result.success && result.data) {
        setStaffList(result.data);
        setFilteredStaff(result.data);
        
        // 获取统计信息
        const statsResult = await staffService.getStaffStats(storeId);
        if (statsResult.success && statsResult.data) {
          setStats(statsResult.data);
        }
      } else {
        console.error('Failed to load staff:', result.error);
      }
    } catch (error) {
      console.error('Error loading staff:', error);
    } finally {
      setLoading(false);
    }
  };

  // 过滤员工
  const filterStaff = () => {
    let filtered = [...staffList];

    // 搜索过滤
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(staff => 
        staff.userData.displayName.toLowerCase().includes(searchLower) ||
        staff.userData.firstName.toLowerCase().includes(searchLower) ||
        staff.userData.lastName.toLowerCase().includes(searchLower) ||
        staff.email?.toLowerCase().includes(searchLower)
      );
    }

    // 角色过滤
    if (roleFilter !== 'all') {
      filtered = filtered.filter(staff => staff.staffInfo.role === roleFilter);
    }

    // 状态过滤
    if (statusFilter !== 'all') {
      filtered = filtered.filter(staff => 
        statusFilter === 'active' ? staff.isActive : !staff.isActive
      );
    }

    setFilteredStaff(filtered);
  };

  // 创建员工
  const handleCreateStaff = async (staffData: CreateStaffData, onProgress?: ProgressCallback) => {
    setCreateLoading(true);
    try {
      const result = await staffService.createStaff(storeId, staffData, onProgress, userData?.uid, locale);
      if (result.success) {
        // 进度回调会处理成功状态显示，这里只需要重新加载列表
        setTimeout(() => {
          setShowCreateModal(false);
          loadStaff(); // 重新加载列表
        }, 2000);
      } else {
        alert(t('createError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error creating staff:', error);
      alert(t('createError'));
    } finally {
      setCreateLoading(false);
    }
  };

  // 编辑员工
  const handleEditStaff = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setShowDetailsModal(true);
  };

  // 删除员工
  const handleDeleteStaff = async (staffUid: string) => {
    if (!confirm(t('confirmDelete'))) return;

    try {
      const result = await staffService.removeStaff(staffUid, storeId, user?.uid || '');
      if (result.success) {
        alert(t('deleteSuccess'));
        loadStaff();
      } else {
        alert(t('deleteError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting staff:', error);
      alert(t('deleteError'));
    }
  };

  // 切换员工状态
  const handleToggleActive = async (staffUid: string, active: boolean) => {
    try {
      const updateData: UpdateStaffData = { active };
      const result = await staffService.updateStaffInfo(staffUid, storeId, updateData, userData?.uid || '');
      if (result.success) {
        alert(active ? t('activate') + '成功' : t('deactivate') + '成功');
        loadStaff();
      } else {
        alert(t('updateError') + ': ' + result.error);
      }
    } catch (error) {
      console.error('Error toggling staff status:', error);
      alert(t('updateError'));
    }
  };

  // 查看员工详情
  const handleViewDetails = (staff: StaffMember) => {
    setSelectedStaff(staff);
    setShowDetailsModal(true);
  };

  useEffect(() => {
    loadStaff();
  }, [storeId]);

  useEffect(() => {
    filterStaff();
  }, [searchTerm, roleFilter, statusFilter, staffList]);

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={storeName} storeId={storeId} currentPage="staff" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all">
          <div className="flex items-center">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-lg">
              <FiUsers className="text-white" size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('totalStaff')}</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all">
          <div className="flex items-center">
            <div className="p-3 bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-lg">
              <FiUserCheck className="text-white" size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('activeStaff')}</p>
              <p className="text-2xl font-bold text-gray-900">{stats.active}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all">
          <div className="flex items-center">
            <div className="p-3 bg-gradient-to-r from-red-500 to-red-600 rounded-xl shadow-lg">
              <FiUserX className="text-white" size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('inactiveStaff')}</p>
              <p className="text-2xl font-bold text-gray-900">{stats.inactive}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all">
          <div className="flex items-center">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl shadow-lg">
              <FiSettings className="text-white" size={24} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">{t('managers')}</p>
              <p className="text-2xl font-bold text-gray-900">
                {stats.byRole[StoreRole.STORE_ADMIN] + stats.byRole[StoreRole.STORE_OWNER]}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和过滤 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <input
                type="text"
                placeholder={t('searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
              />
            </div>

            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value as StoreRole | 'all')}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
            >
              <option value="all">{t('allRoles')}</option>
              <option value={StoreRole.STORE_OWNER}>{t('roles.owner')}</option>
              <option value={StoreRole.STORE_ADMIN}>{t('roles.admin')}</option>
              <option value={StoreRole.STORE_STAFF}>{t('roles.staff')}</option>
            </select>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white/50 backdrop-blur-sm"
            >
              <option value="all">{t('allStatus')}</option>
              <option value="active">{t('active')}</option>
              <option value="inactive">{t('inactive')}</option>
            </select>
          </div>

          <Button
            onClick={() => setShowCreateModal(true)}
            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 shadow-lg hover:shadow-xl transition-all"
          >
            <FiPlus className="mr-2" size={16} />
            {t('addStaff')}
          </Button>
        </div>
      </div>

      {/* 员工列表 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">{t('loading')}</p>
          </div>
        ) : filteredStaff.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {searchTerm || roleFilter !== 'all' || statusFilter !== 'all' 
              ? t('noSearchResults')
              : t('noStaff')
            }
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-6">
            {filteredStaff.map((staff) => (
              <StaffCard
                key={staff.userData.uid}
                staff={staff}
                onEdit={handleEditStaff}
                onDelete={handleDeleteStaff}
                onToggleActive={handleToggleActive}
                onViewDetails={handleViewDetails}
              />
            ))}
          </div>
        )}
      </div>

      {/* 模态框 */}
      <CreateStaffModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateStaff}
        isLoading={createLoading}
      />

      <StaffDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        staff={selectedStaff}
        storeId={storeId}
      />
      </div>
    </div>
  );
} 