'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/app/[locale]/components/ui/Button';
import { Input } from '@/app/[locale]/components/ui/Input';
import { PhoneNumberInput, validatePhoneNumber } from '@/app/[locale]/components/ui/PhoneNumberInput';
import { useAuth, AuthenticatedRoute } from '@/app/lib/firebase/context/AuthContext';
import customerService from '@/app/lib/services/customer_service';
import petService from '@/app/lib/services/pet_service';
import { Customer, CustomerSource, Pet } from '@/app/lib/models/customer';
import { PetGender, PetType, PetVisibility } from '@/app/lib/models/types';
import { useTranslations } from 'next-intl';
import { StoreHeader } from '@/app/[locale]/components/ui/StoreHeader';
import { validateEmail } from '@/app/lib/utils/validations';
import { 
  FiPlus, 
  FiUsers, 
  FiSearch,
  FiDownload,
  FiUserPlus,
  FiUser,
  FiMail,
  FiEye,
  FiEdit3,
  FiTrash2,
  FiHeart,
  // FiCalendar,
  // FiTag,
  // FiCamera,
  FiX
} from 'react-icons/fi';
import { getUserStoreRole, UserStoreRole } from '../../../../lib/utils/permissions';
import { UserType } from '@/app/lib/models/types';

interface CustomerModalData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  password: string;
  notes: string;
}

interface PetModalData {
  name: string;
  type: PetType;
  breed: string;
  gender: PetGender;
  birthday: string;
  avatar: string;
  regId: string;
  notes: string;
  visibility: PetVisibility;
}

interface CustomerStats {
  totalCustomers: number;
  activeCustomers: number;
  oneNataCustomers: number;
  portalCreatedCustomers: number;
}

interface ProgressModalProps {
  isVisible: boolean;
  currentStep: number;
  totalSteps: number;
  message: string;
}

function ProgressModal({ isVisible, currentStep, totalSteps, message }: ProgressModalProps) {
  const t = useTranslations("ProgressModal");
  
  if (!isVisible) return null;

  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl p-8 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-violet-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FiUserPlus className="w-8 h-8 text-violet-600" />
          </div>
          <h3 className="text-lg font-semibold text-slate-900 mb-2">{t('creatingCustomer')}</h3>
          <p className="text-slate-600 mb-6">{message}</p>
          
          {/* Progress Bar */}
          <div className="w-full bg-slate-200 rounded-full h-3 mb-4">
            <div 
              className="bg-gradient-to-r from-violet-600 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          
          <div className="flex justify-between text-sm text-slate-500">
            <span>{t('step')} {currentStep}</span>
            <span>{t('totalSteps')} {totalSteps}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function CustomersPage() {
  return (
    <AuthenticatedRoute>
      <CustomersContent />
    </AuthenticatedRoute>
  );
}

function CustomersContent() {
  const router = useRouter();
  const params = useParams();
  const t = useTranslations("CustomerPage");
  const { userData } = useAuth();
  const storeId = params.id as string;
  
  // 店铺名称
  const storeName = t('customerManagement');

  // State
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sourceFilter, setSourceFilter] = useState<CustomerSource | ''>('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [userStoreRole, setUserStoreRole] = useState<UserStoreRole | null>(null);

  // Pet related state
  const [showPetModal, setShowPetModal] = useState(false);
  const [showPetDetailsModal, setShowPetDetailsModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  // const [selectedPet, setSelectedPet] = useState<Pet | null>(null);
  // const [petLoading, setPetLoading] = useState(false);
  // const [customerPets, setCustomerPets] = useState<Pet[]>([]);

  // Progress state
  const [showProgress, setShowProgress] = useState(false);
  const [progressStep, setProgressStep] = useState(0);
  const [progressTotal, setProgressTotal] = useState(8);
  const [progressMessage, setProgressMessage] = useState('');

  // Stats
  const [stats, setStats] = useState<CustomerStats>({
    totalCustomers: 0,
    activeCustomers: 0,
    oneNataCustomers: 0,
    portalCreatedCustomers: 0
  });

  // Modal state
  const [modalData, setModalData] = useState<CustomerModalData>({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '+1 ',
    password: '',
    notes: ''
  });

  // Pet modal state
  const [petModalData, setPetModalData] = useState<PetModalData>({
    name: '',
    type: PetType.DOG,
    breed: '',
    gender: PetGender.UNKNOWN,
    birthday: '',
    avatar: '',
    regId: '',
    notes: '',
    visibility: PetVisibility.PUBLIC
  });

  // Validation errors
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [petErrors, setPetErrors] = useState<Record<string, string>>({});

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!modalData.email.trim()) {
      newErrors.email = t('form.emailRequired');
    } else if (!validateEmail(modalData.email)) {
      newErrors.email = t('form.emailInvalid');
    }
    
    if (!modalData.firstName.trim()) {
      newErrors.firstName = t('form.firstNameRequired');
    }
    
    if (!modalData.lastName.trim()) {
      newErrors.lastName = t('form.lastNameRequired');
    }

    if (modalData.phoneNumber && modalData.phoneNumber.trim() !== '+1 ' && !validatePhoneNumber(modalData.phoneNumber)) {
      newErrors.phoneNumber = t('form.phoneInvalid');
    }

    if (!modalData.password.trim()) {
      newErrors.password = t('form.passwordRequired');
    } else if (modalData.password.length < 6) {
      newErrors.password = t('form.passwordTooShort');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Validate pet form
  const validatePetForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!petModalData.name.trim()) {
      newErrors.name = t('petForm.nameRequired');
    }
    
    if (!petModalData.breed.trim()) {
      newErrors.breed = t('petForm.breedRequired');
    }

    if (petModalData.birthday) {
      const selectedDate = new Date(petModalData.birthday);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
      
      if (selectedDate > today) {
        newErrors.birthday = t('petForm.birthdayInvalid');
      }
    }

    setPetErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Create pet
  const handleCreatePet = async () => {
    if (!userData?.uid || !selectedCustomer) {
      alert(t('pleaseLoginFirst'));
      return;
    }

    if (!validatePetForm()) {
      return;
    }

    try {
      // setPetLoading(true); // This line is removed
      
      const result = await petService.createPet({
        owner: selectedCustomer.customerData.uid || '',
        name: petModalData.name,
        type: petModalData.type,
        breed: petModalData.breed,
        gender: petModalData.gender,
        birthday: petModalData.birthday ? new Date(petModalData.birthday) : new Date(),
        avatar: petModalData.avatar || '',
        regId: petModalData.regId || '',
        notes: petModalData.notes || '',
        visibility: petModalData.visibility
      }, userData.uid);

      if (result.success) {
        setShowPetModal(false);
        setPetModalData({
          name: '',
          type: PetType.DOG,
          breed: '',
          gender: PetGender.UNKNOWN,
          birthday: '',
          avatar: '',
          regId: '',
          notes: '',
          visibility: PetVisibility.PUBLIC
        });
        setPetErrors({});
        
        // Reload customers to get updated pet data
        await loadCustomers();
        
        alert(t('petCreatedSuccessfully'));
      } else {
        alert(result.error || t('petCreationFailed'));
      }
    } catch (error) {
      console.error('Error creating pet:', error);
      alert(t('petCreationFailed'));
    } finally {
      // setPetLoading(false); // This line is removed
    }
  };

  // Open pet modal
  const openPetModal = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowPetModal(true);
  };

  // Open pet details modal
  const openPetDetailsModal = async (customer: Customer) => {
    setSelectedCustomer(customer);
    // setCustomerPets(customer.pets || []); // This line is removed
    setShowPetDetailsModal(true);
  };

  // Load customers
  const loadCustomers = async () => {
    try {
      const result = await customerService.getCustomersByStore(storeId);
      
      if (result.success && result.data) {
        const filteredCustomers = result.data;
        
        // 如果是staff，只显示自己的客户（通过预约关联）
        if (userStoreRole?.isStaff && userData?.uid) {
          // 这里需要根据预约数据过滤客户
          // 暂时显示所有客户，后续可以通过预约服务来过滤
          // TODO: 实现基于预约的客户过滤
          console.log('Staff can only see their own customers - filtering not yet implemented');
        }
        
        setCustomers(filteredCustomers);
        
        // 计算统计数据
        const totalCustomers = filteredCustomers.length;
        const activeCustomers = filteredCustomers.filter(c => 
          c.customerData.userType === UserType.PETSTORE_CUSTOMER_FROM_PORTAL
        ).length;
        const oneNataCustomers = filteredCustomers.filter(c => 
          c.customerData.userType === 'ONENATA_USER'
        ).length;
        const portalCreatedCustomers = filteredCustomers.filter(c => 
          c.customerData.userType === UserType.PETSTORE_CUSTOMER_FROM_PORTAL
        ).length;

        setStats({
          totalCustomers,
          activeCustomers,
          oneNataCustomers,
          portalCreatedCustomers
        });
      } else {
        console.error('Load customers error:', result.error);
      }
    } catch (error) {
      console.error('Load customers error:', error);
    }
  };

  // Create customer
  const handleCreateCustomer = async () => {
    if (!userData?.uid) {
      alert(t('pleaseLoginFirst'));
      return;
    }

    if (!validateForm()) {
      return;
    }

    try {
      setCreateLoading(true);
      setShowProgress(true);
      setProgressStep(0);
      setProgressTotal(8);
      
      const result = await customerService.createCustomer(
        {
          email: modalData.email,
          firstName: modalData.firstName,
          lastName: modalData.lastName,
          phoneNumber: modalData.phoneNumber === '+1 ' ? undefined : modalData.phoneNumber,
          note: modalData.notes || undefined,
          password: modalData.password
        },
        storeId,
        userData.uid,
        (step, total, message) => {
          setProgressStep(step);
          setProgressTotal(total);
          setProgressMessage(message);
        }
      );

      if (result.success) {
        setShowCreateModal(false);
        setShowProgress(false);
        setModalData({
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '+1 ',
          password: '',
          notes: ''
        });
        setErrors({});
        loadCustomers();
        alert(t('customerCreatedSuccessfully'));
      } else {
        setShowProgress(false);
        alert(result.error || t('customerCreationFailed'));
      }
    } catch (error) {
      console.error('Error creating customer:', error);
      setShowProgress(false);
      alert(t('customerCreationFailed'));
    } finally {
      setCreateLoading(false);
    }
  };

  // Filter customers based on search term and source
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = !searchTerm || 
      customer.customerData.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.customerData.lastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.customerData.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.customerData.phoneNumber?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSource = !sourceFilter || 
      (sourceFilter === CustomerSource.ONE_NATA && customer.customerData.userType === 'ONENATA_USER') ||
      (sourceFilter === CustomerSource.PORTAL && customer.customerData.userType === 'PETSTORE_CUSTOMER_FROM_PORTAL');

    return matchesSearch && matchesSource;
  });

  // Get display name for customer
  const getCustomerDisplayName = (customer: Customer): string => {
    if (customer.customerData.firstName && customer.customerData.lastName) {
      return `${customer.customerData.firstName} ${customer.customerData.lastName}`.trim();
    }
    return customer.customerData.firstName || customer.customerData.lastName || 'Unknown Customer';
  };

  // Get customer source display
  const getCustomerSourceDisplay = (customer: Customer): string => {
    if (customer.customerData.userType === 'ONENATA_USER') {
      return t('oneNataUser');
    } else if (customer.customerData.userType === 'PETSTORE_CUSTOMER_FROM_PORTAL') {
      return t('portalCreated');
    }
    return t('walkInCustomer');
  };

  // 加载客户列表
  useEffect(() => {
    loadData();
  }, [storeId]);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // 获取用户角色
      if (userData?.uid) {
        const role = await getUserStoreRole(userData.uid, storeId);
        setUserStoreRole(role);
      }
      
      await loadCustomers();
    } catch (error) {
      console.error('Load data error:', error);
    } finally {
      setLoading(false);
    }
  };

  // 检查是否可以添加客户
  const canAddCustomer = userStoreRole?.isOwner || userStoreRole?.isAdmin || false;

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={storeName} storeId={storeId} currentPage="customers" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-slate-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-slate-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={storeName} storeId={storeId} currentPage="customers" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 头部 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                <FiUsers className="w-8 h-8 mr-3 text-violet-600" />
                {t('customerManagement')}
              </h1>
              <p className="text-slate-600 font-medium mt-1">
                {t('manageCustomerInfoAndAppointments')} • {stats.totalCustomers} {t('customersNumbers')}
              </p>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => setShowImportModal(true)}
                className="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 flex items-center"
              >
                <FiDownload className="w-5 h-5 mr-2" />
                {t('importFromOneNata')}
              </Button>
              <Button
                onClick={() => setShowCreateModal(true)}
                className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 flex items-center"
                disabled={!canAddCustomer}
              >
                <FiUserPlus className="w-5 h-5 mr-2" />
                {t('addCustomer')}
              </Button>
            </div>
          </div>
          {!canAddCustomer && userStoreRole?.isStaff && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-blue-700 text-sm">
                {t('staffPermissionNote')}
              </p>
            </div>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FiUsers className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('totalCustomers')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totalCustomers}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <FiUser className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('activeCustomers')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.activeCustomers}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FiMail className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('oneNataCustomers')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.oneNataCustomers}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100">
                <FiUserPlus className="w-6 h-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('manualAdd')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.portalCreatedCustomers}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-500 w-5 h-5" />
                <Input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={t('searchCustomerNameEmailPhone')}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="md:w-48">
              <select
                value={sourceFilter}
                onChange={(e) => setSourceFilter(e.target.value as CustomerSource | '')}
                className="w-full px-4 py-3 bg-white border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200 text-slate-900"
              >
                <option value="">{t('allSources')}</option>
                <option value={CustomerSource.ONE_NATA}>{t('oneNataUser')}</option>
                <option value={CustomerSource.PORTAL}>{t('portalCreated')}</option>
              </select>
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-slate-50">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    {t('customerInfo')}
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    {t('contactInfo')}
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    {t('pets')}
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    {t('source')}
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    {t('actions')}
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {filteredCustomers.map((customer, index) => (
                  <tr key={customer.customerData.sid || index} className="hover:bg-slate-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-violet-100 flex items-center justify-center">
                            <FiUser className="w-5 h-5 text-violet-600" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-slate-900">
                            {getCustomerDisplayName(customer)}
                          </div>
                          <div className="text-sm text-slate-500">
                            ID: {customer.customerData.sid?.substring(0, 8)}...
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-900">{customer.customerData.email}</div>
                      <div className="text-sm text-slate-500">{customer.customerData.phoneNumber}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-900">
                        {customer.pets && customer.pets.length > 0 ? (
                          <div className="flex items-center space-x-2">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                              {customer.pets.length} {t('pets')}
                            </span>
                            <Button
                              onClick={() => openPetDetailsModal(customer)}
                              className="bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-1 text-xs flex items-center"
                            >
                              <FiEye className="w-3 h-3 mr-1" />
                              {t('viewPet')}
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center space-x-2">
                            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">
                              {t('noPets')}
                            </span>
                            <Button
                              onClick={() => openPetModal(customer)}
                              className="bg-violet-100 hover:bg-violet-200 text-violet-700 px-3 py-1 text-xs flex items-center"
                            >
                              <FiPlus className="w-3 h-3 mr-1" />
                              {t('addPet')}
                            </Button>
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                        customer.customerData.userType === 'ONENATA_USER'
                          ? 'bg-purple-100 text-purple-800'
                          : customer.customerData.userType === 'PETSTORE_CUSTOMER_FROM_PORTAL'
                          ? 'bg-violet-100 text-violet-800'
                          : 'bg-slate-100 text-slate-800'
                      }`}>
                        {getCustomerSourceDisplay(customer)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <Button
                          onClick={() => router.push(`/store/${storeId}/customers/${customer.customerData.uid}`)}
                          className="bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-1 text-xs flex items-center"
                        >
                          <FiEye className="w-3 h-3 mr-1" />
                          {t('viewDetails')}
                        </Button>
                        <Button
                          onClick={() => router.push(`/store/${storeId}/appointments/create?customerId=${customer.customerData.sid}`)}
                          className="bg-violet-100 hover:bg-violet-200 text-violet-700 px-3 py-1 text-xs flex items-center"
                        >
                          <FiPlus className="w-3 h-3 mr-1" />
                          {t('createAppointment')}
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredCustomers.length === 0 && (
            <div className="text-center py-12">
              <div className="text-slate-500 text-lg">{t('noCustomerData')}</div>
              <p className="text-slate-400 mt-2">{t('clickAddCustomerToManage')}</p>
            </div>
          )}
        </div>

        {/* Create Customer Modal */}
        {showCreateModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <FiUserPlus className="w-6 h-6 mr-2 text-violet-600" />
                {t('addNewCustomer')}
              </h2>
              
              <form onSubmit={(e) => { e.preventDefault(); handleCreateCustomer(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('name')} *
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <Input
                          type="text"
                          placeholder={t('firstName')}
                          value={modalData.firstName}
                          onChange={(e) => setModalData({...modalData, firstName: e.target.value})}
                          className={errors.firstName ? 'border-red-500' : ''}
                          required
                        />
                        {errors.firstName && (
                          <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
                        )}
                      </div>
                      <div>
                        <Input
                          type="text"
                          placeholder={t('lastName')}
                          value={modalData.lastName}
                          onChange={(e) => setModalData({...modalData, lastName: e.target.value})}
                          className={errors.lastName ? 'border-red-500' : ''}
                          required
                        />
                        {errors.lastName && (
                          <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('email')} *
                    </label>
                    <Input
                      type="email"
                      value={modalData.email}
                      onChange={(e) => setModalData({...modalData, email: e.target.value})}
                      className={errors.email ? 'border-red-500' : ''}
                      required
                    />
                    {errors.email && (
                      <p className="text-red-500 text-xs mt-1">{errors.email}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('password')} *
                    </label>
                    <Input
                      type="password"
                      value={modalData.password}
                      onChange={(e) => setModalData({...modalData, password: e.target.value})}
                      placeholder={t('passwordPlaceholder')}
                      className={errors.password ? 'border-red-500' : ''}
                      required
                    />
                    {errors.password && (
                      <p className="text-red-500 text-xs mt-1">{errors.password}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('phoneNumber')}
                    </label>
                    <PhoneNumberInput
                      value={modalData.phoneNumber}
                      onChange={(value) => setModalData({...modalData, phoneNumber: value})}
                      className={errors.phoneNumber ? 'border-red-500' : ''}
                    />
                    {errors.phoneNumber && (
                      <p className="text-red-500 text-xs mt-1">{errors.phoneNumber}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('notes')}
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                      rows={3}
                      placeholder={t('customerNotes')}
                      value={modalData.notes}
                      onChange={(e) => setModalData({...modalData, notes: e.target.value})}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    type="button"
                    onClick={() => {
                      setShowCreateModal(false);
                      setErrors({});
                      setModalData({
                        firstName: '',
                        lastName: '',
                        email: '',
                        phoneNumber: '+1 ',
                        password: '',
                        notes: ''
                      });
                    }}
                    className="bg-slate-100 hover:bg-slate-200 text-slate-700"
                    disabled={createLoading}
                  >
                    {t('cancel')}
                  </Button>
                  <Button
                    type="submit"
                    className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
                    disabled={createLoading}
                  >
                    {createLoading ? '创建中...' : t('createCustomer')}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Add Pet Modal */}
        {showPetModal && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <FiHeart className="w-6 h-6 mr-2 text-violet-600" />
                {t('addPetForCustomer')}
              </h2>
              <p className="text-sm text-slate-600 mb-4">
                {getCustomerDisplayName(selectedCustomer)}
              </p>
              
              <form onSubmit={(e) => { e.preventDefault(); handleCreatePet(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('petName')} *
                    </label>
                    <Input
                      type="text"
                      placeholder={t('petNamePlaceholder')}
                      value={petModalData.name}
                      onChange={(e) => setPetModalData({...petModalData, name: e.target.value})}
                      className={petErrors.name ? 'border-red-500' : ''}
                      required
                    />
                    {petErrors.name && (
                      <p className="text-red-500 text-xs mt-1">{petErrors.name}</p>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">
                        {t('petType')} *
                      </label>
                      <select
                        value={petModalData.type}
                        onChange={(e) => setPetModalData({...petModalData, type: e.target.value as PetType})}
                        className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                        required
                      >
                        <option value={PetType.DOG}>{t('petTypes.dog')}</option>
                        <option value={PetType.CAT}>{t('petTypes.cat')}</option>
                        <option value={PetType.BIRD}>{t('petTypes.bird')}</option>
                        <option value={PetType.FISH}>{t('petTypes.fish')}</option>
                        <option value={PetType.RABBIT}>{t('petTypes.rabbit')}</option>
                        <option value={PetType.HAMSTER}>{t('petTypes.hamster')}</option>
                        <option value={PetType.OTHER}>{t('petTypes.other')}</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-1">
                        {t('petGender')} *
                      </label>
                      <select
                        value={petModalData.gender}
                        onChange={(e) => setPetModalData({...petModalData, gender: e.target.value as PetGender})}
                        className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                        required
                      >
                        <option value={PetGender.MALE}>{t('petGenders.male')}</option>
                        <option value={PetGender.FEMALE}>{t('petGenders.female')}</option>
                        <option value={PetGender.UNKNOWN}>{t('petGenders.unknown')}</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('petBreed')} *
                    </label>
                    <Input
                      type="text"
                      placeholder={t('petBreedPlaceholder')}
                      value={petModalData.breed}
                      onChange={(e) => setPetModalData({...petModalData, breed: e.target.value})}
                      className={petErrors.breed ? 'border-red-500' : ''}
                      required
                    />
                    {petErrors.breed && (
                      <p className="text-red-500 text-xs mt-1">{petErrors.breed}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('petBirthday')}
                    </label>
                    <Input
                      type="date"
                      value={petModalData.birthday}
                      onChange={(e) => setPetModalData({...petModalData, birthday: e.target.value})}
                      max={new Date().toISOString().split('T')[0]}
                      className={petErrors.birthday ? 'border-red-500' : ''}
                    />
                    {petErrors.birthday && (
                      <p className="text-red-500 text-xs mt-1">{petErrors.birthday}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('petRegId')}
                    </label>
                    <Input
                      type="text"
                      placeholder={t('petRegIdPlaceholder')}
                      value={petModalData.regId}
                      onChange={(e) => setPetModalData({...petModalData, regId: e.target.value})}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('petNotes')}
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                      rows={3}
                      placeholder={t('petNotesPlaceholder')}
                      value={petModalData.notes}
                      onChange={(e) => setPetModalData({...petModalData, notes: e.target.value})}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    type="button"
                    onClick={() => {
                      setShowPetModal(false);
                      setPetErrors({});
                      setPetModalData({
                        name: '',
                        type: PetType.DOG,
                        breed: '',
                        gender: PetGender.UNKNOWN,
                        birthday: '',
                        avatar: '',
                        regId: '',
                        notes: '',
                        visibility: PetVisibility.PUBLIC
                      });
                    }}
                    className="bg-slate-100 hover:bg-slate-200 text-slate-700"
                    // disabled={petLoading} // This line is removed
                  >
                    {t('cancel')}
                  </Button>
                  <Button
                    type="submit"
                    className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
                    // disabled={petLoading} // This line is removed
                  >
                    {/* {petLoading ? '创建中...' : t('addPet')} */}
                    {t('addPet')}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* Pet Details Modal */}
        {showPetDetailsModal && selectedCustomer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold flex items-center text-slate-800">
                  <div className="w-8 h-8 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                    <FiHeart className="w-4 h-4 text-white" />
                  </div>
                  {t('petDetails')}
                </h2>
                <Button
                  onClick={() => setShowPetDetailsModal(false)}
                  className="w-8 h-8 bg-slate-100 hover:bg-slate-200 rounded-full flex items-center justify-center transition-colors"
                >
                  <FiX className="w-4 h-4 text-slate-600" />
                </Button>
              </div>
              
              <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                <p className="text-sm font-medium text-slate-700">
                  <span className="text-blue-600">{t('customerInfo')}:</span> {getCustomerDisplayName(selectedCustomer)}
                </p>
              </div>

              {selectedCustomer?.pets && selectedCustomer.pets.length > 0 ? (
                <div className="space-y-4">
                  {selectedCustomer.pets.map((pet: Pet, index: number) => (
                    <div key={pet.sid || index} className="border border-slate-200 rounded-xl p-6 bg-gradient-to-br from-white to-slate-50 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <div className="w-14 h-14 bg-gradient-to-br from-violet-400 to-purple-500 rounded-full flex items-center justify-center shadow-md">
                            <FiHeart className="w-7 h-7 text-white" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-slate-800 mb-1">{pet.name}</h3>
                            <div className="flex items-center space-x-3">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {t('petTypes.' + (pet.type || 'other'))}
                              </span>
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {t('petGenders.' + (pet.gender || 'unknown'))}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            onClick={() => {
                              // setSelectedPet(pet);
                              // TODO: Implement edit pet functionality
                            }}
                            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-3 py-2 text-sm font-medium rounded-lg flex items-center shadow-sm"
                          >
                            <FiEdit3 className="w-3 h-3 mr-1" />
                            {t('editPet')}
                          </Button>
                          <Button
                            onClick={() => {
                              if (confirm(t('confirmDeletePet'))) {
                                // TODO: Implement delete pet functionality
                              }
                            }}
                            className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white px-3 py-2 text-sm font-medium rounded-lg flex items-center shadow-sm"
                          >
                            <FiTrash2 className="w-3 h-3 mr-1" />
                            {t('delete')}
                          </Button>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-slate-200">
                            <span className="text-sm font-medium text-slate-600">{t('petBreed')}:</span>
                            <span className="text-sm font-semibold text-slate-800">{pet.breed || t('notSet')}</span>
                          </div>
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-slate-200">
                            <span className="text-sm font-medium text-slate-600">{t('petRegId')}:</span>
                            <span className="text-sm font-semibold text-slate-800">{pet.regId || t('notSet')}</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-slate-200">
                            <span className="text-sm font-medium text-slate-600">{t('petBirthday')}:</span>
                            <span className="text-sm font-semibold text-slate-800">
                              {pet.birthday ? new Date(pet.birthday).toLocaleDateString() : t('notSet')}
                            </span>
                          </div>
                          <div className="flex items-center justify-between py-2 px-3 bg-white rounded-lg border border-slate-200">
                            <span className="text-sm font-medium text-slate-600">{t('petStatus')}:</span>
                            <span className={`text-sm font-semibold ${pet.isLive ? 'text-green-600' : 'text-red-600'}`}>
                              {pet.isLive ? t('petActive') : t('petInactive')}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      {pet.notes && (
                        <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                          <div className="flex items-start">
                            <div className="w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                              <span className="text-amber-600 text-xs">📝</span>
                            </div>
                            <div>
                              <span className="text-sm font-medium text-amber-800">{t('petNotes')}:</span>
                              <p className="text-sm text-amber-700 mt-1">{pet.notes}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FiHeart className="w-10 h-10 text-slate-400" />
                  </div>
                  <div className="text-slate-500 text-lg font-medium mb-2">{t('noPets')}</div>
                  <p className="text-slate-400 mb-6">{t('addPetForCustomer')}</p>
                  <Button
                    onClick={() => {
                      setShowPetDetailsModal(false);
                      openPetModal(selectedCustomer);
                    }}
                    className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-medium shadow-lg"
                  >
                    <FiPlus className="w-4 h-4 mr-2" />
                    {t('addPet')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Import from OneNata Modal */}
        {showImportModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-2xl mx-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <FiDownload className="w-6 h-6 mr-2 text-emerald-600" />
                {t('importFromOneNata')}
              </h2>
              
              <div className="text-center py-12">
                <div className="text-slate-500 text-lg">{t('oneNataUserSearch')}</div>
                <p className="text-slate-400 mt-2">{t('oneNataUserSearchDesc')}</p>
                <p className="text-sm text-slate-400 mt-1">{t('oneNataUserSearchDesc2')}</p>
              </div>

              <div className="flex justify-end gap-3 mt-6">
                <Button
                  onClick={() => setShowImportModal(false)}
                  className="bg-slate-100 hover:bg-slate-200 text-slate-700"
                >
                  {t('close')}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Progress Modal */}
        <ProgressModal 
          isVisible={showProgress}
          currentStep={progressStep}
          totalSteps={progressTotal}
          message={progressMessage}
        />
      </div>
    </div>
  );
} 