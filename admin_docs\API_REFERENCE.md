# OneNata Admin Portal - API 参考文档

## 📋 目录

1. [认证 API](#认证-api)
2. [用户管理 API](#用户管理-api)
3. [店铺管理 API](#店铺管理-api)
4. [预约管理 API](#预约管理-api)
5. [客户管理 API](#客户管理-api)
6. [员工管理 API](#员工管理-api)
7. [服务管理 API](#服务管理-api)
8. [文件上传 API](#文件上传-api)
9. [地图服务 API](#地图服务-api)
10. [错误处理](#错误处理)

---

## 🔐 认证 API

### 用户登录

**端点**: `POST /api/auth/login`

**描述**: 用户登录认证

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "uid": "user123",
      "email": "<EMAIL>",
      "displayName": "<PERSON>"
    },
    "userData": {
      "sid": "user-data-123",
      "uid": "user123",
      "email": "<EMAIL>",
      "userType": "201",
      "isProfileCompleted": true
    }
  }
}
```

**错误响应**:
```json
{
  "success": false,
  "error": {
    "code": "auth/user-not-found",
    "message": "用户不存在"
  }
}
```

### 用户注册

**端点**: `POST /api/auth/register`

**描述**: 新用户注册

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "****** 123 4567"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "userAccountSid": "account-123",
    "userDataSid": "user-data-123",
    "message": "注册成功，请检查邮箱验证"
  }
}
```

### 邮箱验证

**端点**: `POST /api/auth/verify-email`

**描述**: 验证邮箱地址

**请求体**:
```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "邮箱验证成功"
  }
}
```

### 密码重置

**端点**: `POST /api/auth/forgot-password`

**描述**: 发送密码重置邮件

**请求体**:
```json
{
  "email": "<EMAIL>"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "密码重置邮件已发送"
  }
}
```

---

## 👤 用户管理 API

### 获取用户资料

**端点**: `GET /api/users/profile`

**描述**: 获取当前用户的详细资料

**请求头**:
```
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "sid": "user-data-123",
    "uid": "user123",
    "email": "<EMAIL>",
    "displayName": "John Doe",
    "firstName": "John",
    "lastName": "Doe",
    "phoneNumber": "****** 123 4567",
    "userType": "201",
    "photoURL": "https://example.com/avatar.jpg",
    "isProfileCompleted": true,
    "create_date": "2024-01-15T10:00:00Z",
    "update_date": "2024-01-15T10:00:00Z"
  }
}
```

### 更新用户资料

**端点**: `PUT /api/users/profile`

**描述**: 更新用户资料信息

**请求体**:
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "phoneNumber": "****** 123 4567",
  "photoURL": "https://example.com/avatar.jpg"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "资料更新成功"
  }
}
```

### 更改密码

**端点**: `PUT /api/users/change-password`

**描述**: 更改用户密码

**请求体**:
```json
{
  "currentPassword": "oldpassword123",
  "newPassword": "newpassword123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "密码更改成功"
  }
}
```

---

## 🏢 店铺管理 API

### 创建店铺

**端点**: `POST /api/stores`

**描述**: 创建新店铺

**请求体**:
```json
{
  "storeName": "Pet Store",
  "businessType": "pet_store",
  "description": "专业的宠物服务",
  "phone": "****** 123 4567",
  "email": "<EMAIL>",
  "website": "https://petstore.com",
  "currentAddress": {
    "addressLine1": "123 Main St",
    "addressLine2": "Suite 100",
    "city": "Vancouver",
    "province": "BC",
    "country": "Canada",
    "postCode": "V6B 1A1",
    "latitude": 49.2827,
    "longitude": -123.1207
  },
  "services": {
    "grooming": true,
    "veterinary": true,
    "training": false,
    "boarding": true,
    "dayCare": true,
    "mobileService": false
  },
  "businessHours": {
    "monday": {
      "open": true,
      "startTime": "09:00",
      "endTime": "18:00"
    },
    "tuesday": {
      "open": true,
      "startTime": "09:00",
      "endTime": "18:00"
    }
  },
  "googlePlaceId": "ChIJ...",
  "avatarUrl": "https://example.com/store-avatar.jpg",
  "storePhotos": [
    "https://example.com/photo1.jpg",
    "https://example.com/photo2.jpg"
  ]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "storeId": "store-123",
    "message": "店铺创建成功，等待审批"
  }
}
```

### 获取店铺列表

**端点**: `GET /api/stores`

**描述**: 获取用户关联的店铺列表

**查询参数**:
- `status`: 店铺状态 (active, pending, rejected)
- `limit`: 返回数量限制 (默认 20)
- `offset`: 偏移量 (默认 0)

**响应**:
```json
{
  "success": true,
  "data": {
    "stores": [
      {
        "storeId": "store-123",
        "storeName": "Pet Store",
        "businessType": "pet_store",
        "storeStatus": "active",
        "storeVerifiedStatus": "verified",
        "phone": "****** 123 4567",
        "email": "<EMAIL>",
        "currentAddress": {
          "city": "Vancouver",
          "province": "BC"
        },
        "create_date": "2024-01-15T10:00:00Z"
      }
    ],
    "total": 1,
    "hasMore": false
  }
}
```

### 获取店铺详情

**端点**: `GET /api/stores/{storeId}`

**描述**: 获取特定店铺的详细信息

**响应**:
```json
{
  "success": true,
  "data": {
    "storeId": "store-123",
    "storeName": "Pet Store",
    "businessType": "pet_store",
    "description": "专业的宠物服务",
    "phone": "****** 123 4567",
    "email": "<EMAIL>",
    "website": "https://petstore.com",
    "currentAddress": {
      "addressLine1": "123 Main St",
      "addressLine2": "Suite 100",
      "city": "Vancouver",
      "province": "BC",
      "country": "Canada",
      "postCode": "V6B 1A1",
      "latitude": 49.2827,
      "longitude": -123.1207
    },
    "services": {
      "grooming": true,
      "veterinary": true,
      "training": false,
      "boarding": true,
      "dayCare": true,
      "mobileService": false
    },
    "businessHours": {
      "monday": {
        "open": true,
        "startTime": "09:00",
        "endTime": "18:00"
      }
    },
    "storeStatus": "active",
    "storeVerifiedStatus": "verified",
    "appointmentOpen": true,
    "staffs": ["staff-1", "staff-2"],
    "customerList": ["customer-1", "customer-2"],
    "create_date": "2024-01-15T10:00:00Z",
    "update_date": "2024-01-15T10:00:00Z"
  }
}
```

### 更新店铺信息

**端点**: `PUT /api/stores/{storeId}`

**描述**: 更新店铺信息

**请求体**:
```json
{
  "storeName": "Updated Pet Store",
  "description": "更新后的描述",
  "phone": "****** 987 6543",
  "email": "<EMAIL>",
  "currentAddress": {
    "addressLine1": "456 New St",
    "city": "Vancouver",
    "province": "BC",
    "country": "Canada",
    "postCode": "V6B 2A2"
  },
  "businessHours": {
    "monday": {
      "open": true,
      "startTime": "08:00",
      "endTime": "19:00"
    }
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "店铺信息更新成功"
  }
}
```

### 店铺审批

**端点**: `POST /api/admin/store-approval/{storeId}`

**描述**: 管理员审批店铺 (仅管理员)

**请求体**:
```json
{
  "action": "approve", // approve 或 reject
  "notes": "审批通过"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "店铺审批成功"
  }
}
```

---

## 📅 预约管理 API

### 创建预约

**端点**: `POST /api/appointments`

**描述**: 创建新预约

**请求体**:
```json
{
  "storeId": "store-123",
  "customerId": "customer-123",
  "serviceId": "service-123",
  "staffId": "staff-123",
  "date": "2024-01-20",
  "time": "10:00",
  "duration": 60,
  "notes": "特殊要求",
  "petId": "pet-123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "appointmentId": "appointment-123",
    "message": "预约创建成功"
  }
}
```

### 获取预约列表

**端点**: `GET /api/appointments`

**描述**: 获取店铺的预约列表

**查询参数**:
- `storeId`: 店铺ID (必需)
- `status`: 预约状态 (draft, confirmed, in_progress, completed, cancelled)
- `staffId`: 员工ID
- `date`: 日期 (YYYY-MM-DD)
- `startDate`: 开始日期
- `endDate`: 结束日期
- `limit`: 返回数量限制
- `offset`: 偏移量

**响应**:
```json
{
  "success": true,
  "data": {
    "appointments": [
      {
        "appointmentId": "appointment-123",
        "storeId": "store-123",
        "customerId": "customer-123",
        "staffId": "staff-123",
        "serviceId": "service-123",
        "status": "confirmed",
        "source": "portal_created",
        "timeInfo": {
          "date": "2024-01-20",
          "time": "10:00",
          "duration": 60,
          "endTime": "11:00"
        },
        "customerInfo": {
          "name": "John Doe",
          "email": "<EMAIL>",
          "phoneNumber": "****** 123 4567"
        },
        "serviceInfo": {
          "serviceName": "宠物美容",
          "duration": 60,
          "price": 50.00
        },
        "staffInfo": {
          "staffName": "Jane Smith",
          "staffEmail": "<EMAIL>"
        },
        "notes": "特殊要求",
        "create_date": "2024-01-15T10:00:00Z"
      }
    ],
    "total": 1,
    "hasMore": false
  }
}
```

### 更新预约状态

**端点**: `PUT /api/appointments/{appointmentId}/status`

**描述**: 更新预约状态

**请求体**:
```json
{
  "status": "confirmed", // draft, confirmed, in_progress, completed, cancelled
  "notes": "状态更新备注"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "预约状态更新成功"
  }
}
```

### 获取可用时间段

**端点**: `GET /api/appointments/available-slots`

**描述**: 获取指定日期和服务的可用时间段

**查询参数**:
- `storeId`: 店铺ID
- `serviceId`: 服务ID
- `staffId`: 员工ID
- `date`: 日期 (YYYY-MM-DD)
- `duration`: 服务时长 (分钟)

**响应**:
```json
{
  "success": true,
  "data": {
    "availableSlots": [
      {
        "startTime": "09:00",
        "endTime": "10:00",
        "status": "available"
      },
      {
        "startTime": "10:00",
        "endTime": "11:00",
        "status": "booked"
      },
      {
        "startTime": "11:00",
        "endTime": "12:00",
        "status": "available"
      }
    ]
  }
}
```

---

## 👥 客户管理 API

### 创建客户

**端点**: `POST /api/customers`

**描述**: 创建新客户

**请求体**:
```json
{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "phoneNumber": "****** 987 6543",
  "note": "新客户",
  "password": "tempPassword123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "customerId": "customer-123",
    "message": "客户创建成功"
  }
}
```

### 获取客户列表

**端点**: `GET /api/customers`

**描述**: 获取店铺的客户列表

**查询参数**:
- `storeId`: 店铺ID (必需)
- `source`: 客户来源 (onenata_app, portal_created, walk_in)
- `search`: 搜索关键词 (姓名、邮箱、电话)
- `limit`: 返回数量限制
- `offset`: 偏移量

**响应**:
```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "customerId": "customer-123",
        "customerSource": "portal_created",
        "oneNataUserId": null,
        "firstName": "Jane",
        "lastName": "Smith",
        "email": "<EMAIL>",
        "phoneNumber": "****** 987 6543",
        "totalAppointments": 5,
        "completedAppointments": 3,
        "petIds": ["pet-1", "pet-2"],
        "create_date": "2024-01-15T10:00:00Z"
      }
    ],
    "total": 1,
    "hasMore": false
  }
}
```

### 获取客户详情

**端点**: `GET /api/customers/{customerId}`

**描述**: 获取客户详细信息

**响应**:
```json
{
  "success": true,
  "data": {
    "customerId": "customer-123",
    "customerSource": "portal_created",
    "oneNataUserId": null,
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "phoneNumber": "****** 987 6543",
    "totalAppointments": 5,
    "completedAppointments": 3,
    "petIds": ["pet-1", "pet-2"],
    "pets": [
      {
        "petId": "pet-1",
        "name": "Buddy",
        "species": "dog",
        "breed": "Golden Retriever",
        "age": 3
      }
    ],
    "appointments": [
      {
        "appointmentId": "appointment-123",
        "date": "2024-01-20",
        "time": "10:00",
        "serviceName": "宠物美容",
        "status": "confirmed"
      }
    ],
    "create_date": "2024-01-15T10:00:00Z"
  }
}
```

---

## 👨‍💼 员工管理 API

### 获取员工列表

**端点**: `GET /api/staff`

**描述**: 获取店铺的员工列表

**查询参数**:
- `storeId`: 店铺ID (必需)
- `status`: 员工状态 (active, inactive)
- `role`: 员工角色 (owner, admin, staff)

**响应**:
```json
{
  "success": true,
  "data": {
    "staff": [
      {
        "staffId": "staff-123",
        "userData": {
          "uid": "user-123",
          "email": "<EMAIL>",
          "displayName": "Jane Smith",
          "firstName": "Jane",
          "lastName": "Smith",
          "phoneNumber": "****** 123 4567"
        },
        "storeRole": "staff",
        "isActive": true,
        "services": [
          {
            "serviceId": "service-123",
            "serviceCategory": "grooming",
            "serviceBreed": "dog",
            "serviceAmount": 50.00
          }
        ],
        "schedule": {
          "monday": {
            "open": true,
            "startTime": "09:00",
            "endTime": "18:00"
          }
        },
        "create_date": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### 添加员工到店铺

**端点**: `POST /api/staff`

**描述**: 将员工添加到店铺

**请求体**:
```json
{
  "storeId": "store-123",
  "staffId": "staff-123",
  "role": "staff", // owner, admin, staff
  "services": [
    {
      "serviceCategory": "grooming",
      "serviceBreed": "dog",
      "serviceAmount": 50.00,
      "minDuration": 30,
      "maxDuration": 120,
      "defaultDuration": 60
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "员工添加成功"
  }
}
```

### 更新员工信息

**端点**: `PUT /api/staff/{staffId}`

**描述**: 更新员工信息

**请求体**:
```json
{
  "role": "admin",
  "isActive": true,
  "services": [
    {
      "serviceId": "service-123",
      "serviceCategory": "grooming",
      "serviceBreed": "dog",
      "serviceAmount": 60.00
    }
  ],
  "schedule": {
    "monday": {
      "open": true,
      "startTime": "08:00",
      "endTime": "19:00"
    }
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "员工信息更新成功"
  }
}
```

---

## 🛠️ 服务管理 API

### 获取店铺服务

**端点**: `GET /api/store-services`

**描述**: 获取店铺的服务列表

**查询参数**:
- `storeId`: 店铺ID (必需)
- `status`: 服务状态 (active, inactive)
- `category`: 服务类别 (grooming, veterinary, training, boarding, day_care, mobile_service)

**响应**:
```json
{
  "success": true,
  "data": {
    "services": [
      {
        "serviceId": "service-123",
        "storeId": "store-123",
        "serviceName": "宠物美容",
        "serviceCategory": "grooming",
        "serviceBreed": "dog",
        "commission": 0.1,
        "isOnlineBookingEnabled": true,
        "requiresApproval": false,
        "staffIds": ["staff-1", "staff-2"],
        "totalBookings": 25,
        "completedBookings": 20,
        "create_date": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### 创建店铺服务

**端点**: `POST /api/store-services`

**描述**: 创建新的店铺服务

**请求体**:
```json
{
  "storeId": "store-123",
  "serviceName": "宠物美容",
  "serviceCategory": "grooming",
  "serviceBreed": "dog",
  "commission": 0.1,
  "isOnlineBookingEnabled": true,
  "requiresApproval": false,
  "staffIds": ["staff-1", "staff-2"]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "serviceId": "service-123",
    "message": "服务创建成功"
  }
}
```

### 更新店铺服务

**端点**: `PUT /api/store-services/{serviceId}`

**描述**: 更新店铺服务信息

**请求体**:
```json
{
  "serviceName": "高级宠物美容",
  "commission": 0.15,
  "isOnlineBookingEnabled": true,
  "requiresApproval": true,
  "staffIds": ["staff-1", "staff-2", "staff-3"]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "message": "服务更新成功"
  }
}
```

---

## 📁 文件上传 API

### 上传头像

**端点**: `POST /api/upload/avatar`

**描述**: 上传用户头像

**请求头**:
```
Content-Type: multipart/form-data
```

**请求体**:
```
avatar: <file>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "url": "https://storage.googleapis.com/bucket/avatars/user-123.jpg",
    "filename": "user-123.jpg"
  }
}
```

### 上传店铺照片

**端点**: `POST /api/upload/store-photos`

**描述**: 上传店铺照片

**请求头**:
```
Content-Type: multipart/form-data
```

**请求体**:
```
photos: <files>
storeId: store-123
```

**响应**:
```json
{
  "success": true,
  "data": {
    "urls": [
      "https://storage.googleapis.com/bucket/store-photos/photo1.jpg",
      "https://storage.googleapis.com/bucket/store-photos/photo2.jpg"
    ],
    "filenames": ["photo1.jpg", "photo2.jpg"]
  }
}
```

---

## 🗺️ 地图服务 API

### 地址搜索

**端点**: `GET /api/google-maps/search`

**描述**: 搜索地址和地点

**查询参数**:
- `query`: 搜索关键词
- `location`: 搜索中心点 (lat,lng)
- `radius`: 搜索半径 (米)

**响应**:
```json
{
  "success": true,
  "data": {
    "places": [
      {
        "placeId": "ChIJ...",
        "name": "Vancouver Pet Store",
        "address": "123 Main St, Vancouver, BC V6B 1A1",
        "location": {
          "lat": 49.2827,
          "lng": -123.1207
        },
        "types": ["pet_store", "establishment"]
      }
    ]
  }
}
```

### 地址验证

**端点**: `POST /api/google-maps/validate`

**描述**: 验证地址信息

**请求体**:
```json
{
  "address": {
    "addressLine1": "123 Main St",
    "city": "Vancouver",
    "province": "BC",
    "country": "Canada",
    "postCode": "V6B 1A1"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "formattedAddress": "123 Main St, Vancouver, BC V6B 1A1, Canada",
    "placeId": "ChIJ...",
    "location": {
      "lat": 49.2827,
      "lng": -123.1207
    }
  }
}
```

---

## ❌ 错误处理

### 错误响应格式

所有 API 错误都遵循统一的格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      "field": "具体字段",
      "value": "错误值"
    }
  }
}
```

### 常见错误代码

| 错误代码 | 描述 | HTTP 状态码 |
|----------|------|-------------|
| `AUTH_REQUIRED` | 需要认证 | 401 |
| `PERMISSION_DENIED` | 权限不足 | 403 |
| `NOT_FOUND` | 资源不存在 | 404 |
| `VALIDATION_ERROR` | 数据验证失败 | 400 |
| `DUPLICATE_ENTRY` | 重复数据 | 409 |
| `RATE_LIMIT_EXCEEDED` | 请求频率超限 | 429 |
| `INTERNAL_ERROR` | 服务器内部错误 | 500 |

### 认证错误

```json
{
  "success": false,
  "error": {
    "code": "AUTH_REQUIRED",
    "message": "请先登录"
  }
}
```

### 权限错误

```json
{
  "success": false,
  "error": {
    "code": "PERMISSION_DENIED",
    "message": "您没有权限执行此操作"
  }
}
```

### 验证错误

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": {
      "field": "email",
      "value": "invalid-email",
      "message": "请输入有效的邮箱地址"
    }
  }
}
```

---

## 📝 使用示例

### JavaScript/TypeScript

```typescript
// 登录
const login = async (email: string, password: string) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ email, password }),
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error.message);
  }
  
  return data.data;
};

// 创建预约
const createAppointment = async (appointmentData: any) => {
  const response = await fetch('/api/appointments', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(appointmentData),
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error.message);
  }
  
  return data.data;
};

// 上传文件
const uploadAvatar = async (file: File) => {
  const formData = new FormData();
  formData.append('avatar', file);
  
  const response = await fetch('/api/upload/avatar', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error.message);
  }
  
  return data.data;
};
```

### cURL

```bash
# 登录
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 获取店铺列表
curl -X GET "http://localhost:3000/api/stores?status=active&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 创建预约
curl -X POST http://localhost:3000/api/appointments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "storeId": "store-123",
    "customerId": "customer-123",
    "serviceId": "service-123",
    "staffId": "staff-123",
    "date": "2024-01-20",
    "time": "10:00",
    "duration": 60
  }'
```

---

## 📚 相关文档

- **完整文档**: [DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)
- **快速开始**: [QUICK_START.md](./QUICK_START.md)
- **数据库设计**: 查看 `DEVELOPER_DOCUMENTATION.md` 的数据库设计章节

---

*API 参考文档 - v1.0.0* 