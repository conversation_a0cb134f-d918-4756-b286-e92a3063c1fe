<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Auth 手机验证测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #F2D3A4, #FDECCE);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #A126FF;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #A126FF;
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.5);
        }
        .test-section h2 {
            color: #A126FF;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }
        input, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: linear-gradient(135deg, #A126FF, #8a20d8);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(161, 38, 255, 0.3);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-weight: 500;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .note {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .test-link {
            display: block;
            padding: 15px;
            background: linear-gradient(135deg, #A126FF, #8a20d8);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .test-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(161, 38, 255, 0.3);
        }
        #recaptcha-container {
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Auth 手机验证测试</h1>
        
        <div class="note">
            <h3>📋 测试说明</h3>
            <ul>
                <li><strong>开发环境:</strong> 使用Firebase Auth模拟器，任何6位数字验证码都有效</li>
                <li><strong>测试手机号:</strong> 可以使用任何格式的手机号码</li>
                <li><strong>验证码:</strong> 在模拟器中，输入任何6位数字即可验证成功</li>
                <li><strong>生产环境:</strong> 需要真实的手机号码和reCAPTCHA验证</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📱 手机验证测试</h2>
            
            <div class="form-group">
                <label for="phoneNumber">手机号码:</label>
                <input type="tel" id="phoneNumber" placeholder="+86 13800138000" value="+86 13800138000">
            </div>
            
            <button id="sendCodeBtn" onclick="sendVerificationCode()">发送验证码</button>
            <div id="recaptcha-container"></div>
            <div id="sendResult"></div>
            
            <div class="form-group" style="margin-top: 20px;">
                <label for="verificationCode">验证码:</label>
                <input type="text" id="verificationCode" placeholder="输入6位验证码" maxlength="6">
            </div>
            
            <button id="verifyBtn" onclick="verifyCode()" disabled>验证码验证</button>
            <div id="verifyResult"></div>
        </div>

        <div class="test-section">
            <h2>🔗 相关页面测试</h2>
            <div class="test-links">
                <a href="/zh-CN/auth/verify-phone?phone=+8613800138000" class="test-link">中文验证页面</a>
                <a href="/en/auth/verify-phone?phone=+8613800138000" class="test-link">English Page</a>
                <a href="/de/auth/verify-phone?phone=+8613800138000" class="test-link">Deutsche Seite</a>
                <a href="/zh-TW/auth/verify-phone?phone=+8613800138000" class="test-link">繁體中文頁面</a>
            </div>
        </div>

        <div class="test-section">
            <h2>🛠️ 技术信息</h2>
            <div class="info">
                <p><strong>Firebase Auth 模拟器:</strong> localhost:9099</p>
                <p><strong>验证方式:</strong> Firebase Auth 原生手机验证</p>
                <p><strong>开发环境:</strong> 任何6位数字验证码都有效</p>
                <p><strong>生产环境:</strong> 需要真实短信验证码</p>
            </div>
        </div>
    </div>

    <script>
        // 模拟Firebase Auth服务的测试函数
        let isCodeSent = false;
        
        async function sendVerificationCode() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const sendBtn = document.getElementById('sendCodeBtn');
            const resultDiv = document.getElementById('sendResult');
            
            if (!phoneNumber.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入手机号码</div>';
                return;
            }
            
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            resultDiv.innerHTML = '<div class="info">正在发送验证码...</div>';
            
            try {
                // 模拟API调用
                const response = await fetch('/api/auth/send-phone-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phoneNumber })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        resultDiv.innerHTML = '<div class="success">✅ 验证码发送成功！在开发环境中，您可以使用任何6位数字作为验证码。</div>';
                        isCodeSent = true;
                        document.getElementById('verifyBtn').disabled = false;
                        
                        // 启动60秒冷却时间
                        let cooldown = 60;
                        const interval = setInterval(() => {
                            sendBtn.textContent = `重新发送 (${cooldown}s)`;
                            cooldown--;
                            if (cooldown < 0) {
                                clearInterval(interval);
                                sendBtn.disabled = false;
                                sendBtn.textContent = '发送验证码';
                            }
                        }, 1000);
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                        sendBtn.disabled = false;
                        sendBtn.textContent = '发送验证码';
                    }
                } else {
                    throw new Error('网络请求失败');
                }
            } catch (error) {
                console.error('发送验证码错误:', error);
                resultDiv.innerHTML = '<div class="error">❌ 发送验证码失败，请检查网络连接</div>';
                sendBtn.disabled = false;
                sendBtn.textContent = '发送验证码';
            }
        }
        
        async function verifyCode() {
            const phoneNumber = document.getElementById('phoneNumber').value;
            const verificationCode = document.getElementById('verificationCode').value;
            const verifyBtn = document.getElementById('verifyBtn');
            const resultDiv = document.getElementById('verifyResult');
            
            if (!verificationCode.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入验证码</div>';
                return;
            }
            
            if (verificationCode.length !== 6) {
                resultDiv.innerHTML = '<div class="error">验证码必须是6位数字</div>';
                return;
            }
            
            verifyBtn.disabled = true;
            verifyBtn.textContent = '验证中...';
            resultDiv.innerHTML = '<div class="info">正在验证...</div>';
            
            try {
                // 模拟API调用
                const response = await fetch('/api/auth/verify-phone-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ phoneNumber, code: verificationCode })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        resultDiv.innerHTML = '<div class="success">🎉 验证成功！手机号码验证完成。</div>';
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ ${data.message}</div>`;
                    }
                } else {
                    throw new Error('网络请求失败');
                }
            } catch (error) {
                console.error('验证码验证错误:', error);
                resultDiv.innerHTML = '<div class="error">❌ 验证失败，请检查网络连接</div>';
            } finally {
                verifyBtn.disabled = false;
                verifyBtn.textContent = '验证码验证';
            }
        }
        
        // 监听验证码输入，只允许数字
        document.getElementById('verificationCode').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '').slice(0, 6);
        });
        
        // 页面加载时的信息
        console.log('🔥 Firebase Auth 手机验证测试页面已加载');
        console.log('📱 在开发环境中，您可以使用任何6位数字作为验证码');
        console.log('🧪 测试手机号: +86 13800138000');
    </script>
</body>
</html> 