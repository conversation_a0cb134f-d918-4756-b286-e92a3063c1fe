.section-component-node {
  height: 352px;
  left: 192px;
  position: absolute;
  top: 4197px;
  width: 1536px;
}

.section-component-node .heading-loved-by {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 55px;
  font-weight: 700;
  height: 48px;
  left: 489px;
  letter-spacing: 0;
  line-height: 55px;
  position: absolute;
  text-align: center;
  top: -1px;
  white-space: nowrap;
  width: 557px;
}

.section-component-node .background-shadow {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0px 4px 6px #0000001a;
  height: 280px;
  left: 64px;
  position: absolute;
  top: 112px;
  width: 443px;
}

.section-component-node .onenata-has-been-a {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 112px;
  left: 24px;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  top: 22px;
  width: 390px;
}

.section-component-node .sarah-k {
  background-image: url(https://c.animaapp.com/So8Difff/img/<EMAIL>);
  background-position: 50% 50%;
  background-size: cover;
  height: 56px;
  width: 56px;
  border-radius: 28px;
  position: absolute;
  top: 180px;
  left: 24px;
}

.section-component-node .mark-l {
  background-image: url(https://c.animaapp.com/So8Difff/img/<EMAIL>);
  background-position: 50% 50%;
  background-size: cover;
  height: 56px;
  width: 56px;
  border-radius: 28px;
  position: absolute;
  top: 180px;
  left: 24px;
}

.section-component-node .emily-p {
  background-image: url(https://c.animaapp.com/So8Difff/img/<EMAIL>);
  background-position: 50% 50%;
  background-size: cover;
  height: 56px;
  width: 56px;
  border-radius: 28px;
  position: absolute;
  top: 180px;
  left: 24px;
}

.section-component-node .text-wrapper-7 {
  color: #a025ff;
  font-family: "Manrope", Helvetica;
  font-size: 18px;
  font-weight: 600;
  height: 28px;
  left: 100px;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  top: 181px;
  white-space: nowrap;
  width: 70px;
}

.section-component-node .text-wrapper-8 {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 16px;
  font-weight: 400;
  height: 24px;
  left: 100px;
  letter-spacing: 0;
  line-height: 24px;
  position: absolute;
  top: 209px;
  white-space: nowrap;
  width: 94px;
}

.section-component-node .background-shadow-2 {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0px 4px 6px #0000001a;
  height: 280px;
  left: 547px;
  position: absolute;
  top: 112px;
  width: 442px;
}

.section-component-node .finding-pet-friendly {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 112px;
  left: 24px;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  top: 22px;
  width: 392px;
}

.section-component-node .text-wrapper-9 {
  color: #a025ff;
  font-family: "Manrope", Helvetica;
  font-size: 18px;
  font-weight: 600;
  height: 28px;
  left: 100px;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  top: 181px;
  white-space: nowrap;
  width: 61px;
}

.section-component-node .background-shadow-3 {
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0px 4px 6px #0000001a;
  height: 280px;
  left: 1029px;
  position: absolute;
  top: 112px;
  width: 443px;
}

.section-component-node .i-was-skeptical {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 112px;
  left: 24px;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  top: 22px;
  width: 391px;
}

.section-component-node .text-wrapper-10 {
  color: #a025ff;
  font-family: "Manrope", Helvetica;
  font-size: 18px;
  font-weight: 600;
  height: 28px;
  left: 100px;
  letter-spacing: 0;
  line-height: 28px;
  position: absolute;
  top: 181px;
  white-space: nowrap;
  width: 63px;
}
