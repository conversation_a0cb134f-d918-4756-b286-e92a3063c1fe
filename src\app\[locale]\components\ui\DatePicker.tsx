'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';

interface DatePickerProps {
  id?: string;
  label?: string;
  value?: string; // ISO date string (YYYY-MM-DD)
  onChange?: (value: string) => void;
  error?: string;
  placeholder?: string;
  className?: string;
  required?: boolean;
  minDate?: string; // ISO date string
  maxDate?: string; // ISO date string
}

export const DatePicker: React.FC<DatePickerProps> = ({
  id,
  label,
  value = '',
  onChange,
  error,
  placeholder,
  className = '',
  required = false,
  minDate,
  maxDate = new Date().toISOString().split('T')[0], // 默认最大日期为今天
}) => {
  const t = useTranslations('common');
  const tDate = useTranslations('datePicker');
  const [selectedDate, setSelectedDate] = useState({
    year: '',
    month: '',
    day: ''
  });

  // 解析初始值
  useEffect(() => {
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setSelectedDate({
          year: date.getFullYear().toString(),
          month: (date.getMonth() + 1).toString().padStart(2, '0'),
          day: date.getDate().toString().padStart(2, '0')
        });
      }
    }
  }, [value]);

  // 生成年份选项 (当前年份往前100年)
  const generateYears = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let year = currentYear; year >= currentYear - 100; year--) {
      years.push(year);
    }
    return years;
  };

  // 生成月份选项
  const generateMonths = () => {
    const months = [];
    for (let month = 1; month <= 12; month++) {
      months.push({
        value: month.toString().padStart(2, '0'),
        label: month.toString().padStart(2, '0')
      });
    }
    return months;
  };

  // 生成日期选项
  const generateDays = () => {
    if (!selectedDate.year || !selectedDate.month) return [];
    
    const year = parseInt(selectedDate.year);
    const month = parseInt(selectedDate.month);
    const daysInMonth = new Date(year, month, 0).getDate();
    
    const days = [];
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day.toString().padStart(2, '0'));
    }
    return days;
  };

  // 处理日期变化
  const handleDateChange = (type: 'year' | 'month' | 'day', newValue: string) => {
    const newDate = { ...selectedDate, [type]: newValue };
    setSelectedDate(newDate);

    // 如果所有字段都有值，则触发onChange
    if (newDate.year && newDate.month && newDate.day) {
      const isoDate = `${newDate.year}-${newDate.month}-${newDate.day}`;
      onChange?.(isoDate);
    } else {
      onChange?.('');
    }
  };

  // 验证日期是否在允许范围内
  const isDateValid = (dateString: string) => {
    if (!dateString) return true;
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return false;
    
    if (minDate && dateString < minDate) return false;
    if (maxDate && dateString > maxDate) return false;
    
    return true;
  };

  const years = generateYears();
  const months = generateMonths();
  const days = generateDays();

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="grid grid-cols-3 gap-3">
        {/* 年份选择 */}
        <div>
          <select
            value={selectedDate.year}
            onChange={(e) => handleDateChange('year', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200"
          >
            <option value="">{tDate('year')}</option>
            {years.map(year => (
              <option key={year} value={year}>
                {year}{tDate('yearUnit')}
              </option>
            ))}
          </select>
        </div>

        {/* 月份选择 */}
        <div>
          <select
            value={selectedDate.month}
            onChange={(e) => handleDateChange('month', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200"
            disabled={!selectedDate.year}
          >
            <option value="">{tDate('month')}</option>
            {months.map(month => (
              <option key={month.value} value={month.value}>
                {month.value}{tDate('monthUnit')}
              </option>
            ))}
          </select>
        </div>

        {/* 日期选择 */}
        <div>
          <select
            value={selectedDate.day}
            onChange={(e) => handleDateChange('day', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200"
            disabled={!selectedDate.year || !selectedDate.month}
          >
            <option value="">{tDate('day')}</option>
            {days.map(day => (
              <option key={day} value={day}>
                {day}{tDate('dayUnit')}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 错误消息 */}
      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}

      {/* 验证提示 */}
      {value && !isDateValid(value) && (
        <p className="text-sm text-red-600 mt-1">
          {t('selectValidDate')}
        </p>
      )}

      {/* 占位符提示 */}
      {placeholder && !value && (
        <p className="text-sm text-gray-500 mt-1">{placeholder}</p>
      )}
    </div>
  );
};

export default DatePicker; 