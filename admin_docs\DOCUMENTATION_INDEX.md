# OneNata Admin Portal - 文档索引

## 📋 文档概览

本项目包含以下文档，按使用场景分类：

---

## 🚀 快速开始

### [QUICK_START.md](./QUICK_START.md)
**适用场景**: 新开发者快速上手项目
- ✅ 5分钟快速启动指南
- ✅ 项目概览和核心功能
- ✅ 开发命令和关键文件结构
- ✅ 用户类型和权限矩阵
- ✅ 核心业务流程
- ✅ 常见开发任务
- ✅ 常见问题解答

---

## 📚 完整文档

### [DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)
**适用场景**: 深入了解项目架构和开发指南
- ✅ 项目概述和技术架构
- ✅ 详细的项目结构说明
- ✅ 环境设置和配置指南
- ✅ 业务逻辑和核心功能
- ✅ 开发指南和代码规范
- ✅ 部署指南和故障排除
- ✅ API 文档和数据库设计
- ✅ 测试和性能优化

---

## 🔌 API 开发

### [API_REFERENCE.md](./API_REFERENCE.md)
**适用场景**: API 开发和集成
- ✅ 认证 API (登录、注册、验证)
- ✅ 用户管理 API (资料、密码)
- ✅ 店铺管理 API (CRUD、审批)
- ✅ 预约管理 API (创建、查询、状态)
- ✅ 客户管理 API (创建、查询、详情)
- ✅ 员工管理 API (列表、添加、更新)
- ✅ 服务管理 API (店铺服务、员工服务)
- ✅ 文件上传 API (头像、照片)
- ✅ 地图服务 API (搜索、验证)
- ✅ 错误处理和响应格式
- ✅ 使用示例 (JavaScript/cURL)

---

## 🏗️ 项目架构

### [README.md](./README.md)
**适用场景**: 项目介绍和基本使用
- ✅ 项目简介和功能特性
- ✅ 技术栈和快速开始
- ✅ 项目结构和可用脚本
- ✅ 页面路由和认证流程
- ✅ 响应式设计和国际化
- ✅ 部署指南

---

## 📖 开发文档

### `.cursor/` 目录
**适用场景**: 开发过程中的逻辑文档
- ✅ `1. 文档概述.md` - 项目整体架构
- ✅ `store.md` - 店铺管理逻辑
- ✅ `auth.md` - 认证系统设计
- ✅ `login.md` - 登录流程详细说明
- ✅ `Data Structure.md` - 数据结构设计
- ✅ `Appoitment.md` - 预约系统逻辑
- ✅ `staff.md` - 员工管理逻辑
- ✅ `Geo.md` - 地理位置服务

### `.docs/` 目录
**适用场景**: 功能实现文档
- ✅ `APPOINTMENT_SYSTEM_DOCUMENTATION.md` - 预约系统完整实现
- ✅ `STAFF_MANAGEMENT_README.md` - 员工管理实现
- ✅ `CUSTOMER_SERVICE_IMPLEMENTATION.md` - 客户服务实现
- ✅ `STORE_EDIT_I18N_IMPLEMENTATION.md` - 店铺编辑国际化
- ✅ `SMART_ADDRESS_SEARCH_IMPLEMENTATION.md` - 智能地址搜索
- ✅ `PHOTO_UPLOAD_INTEGRATION_GUIDE.md` - 照片上传集成
- ✅ `FIREBASE_AUTH_IMPLEMENTATION.md` - Firebase 认证实现
- ✅ `I18N_SETUP.md` - 国际化设置指南

---

## 🎯 按角色查找文档

### 👨‍💻 前端开发者
1. **[QUICK_START.md](./QUICK_START.md)** - 快速上手
2. **[DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)** - 开发指南
3. **[API_REFERENCE.md](./API_REFERENCE.md)** - API 调用
4. `.cursor/` 目录 - 业务逻辑理解

### 🔧 后端开发者
1. **[DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)** - 架构设计
2. **[API_REFERENCE.md](./API_REFERENCE.md)** - API 设计
3. `.docs/` 目录 - 功能实现细节
4. `firebase.json` - Firebase 配置

### 🚀 DevOps 工程师
1. **[DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)** - 部署指南
2. **[README.md](./README.md)** - 环境配置
3. `firebase.json` - Firebase 部署配置
4. `vercel.json` - Vercel 部署配置

### 📊 产品经理
1. **[QUICK_START.md](./QUICK_START.md)** - 功能概览
2. `.cursor/` 目录 - 业务逻辑
3. `.docs/` 目录 - 功能实现文档
4. **[API_REFERENCE.md](./API_REFERENCE.md)** - API 功能

---

## 🔍 按功能查找文档

### 认证系统
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 认证 API
- `.cursor/auth.md` - 认证系统设计
- `.cursor/login.md` - 登录流程
- `.docs/FIREBASE_AUTH_IMPLEMENTATION.md` - 实现细节

### 店铺管理
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 店铺 API
- `.cursor/store.md` - 店铺管理逻辑
- `.docs/STORE_EDIT_I18N_IMPLEMENTATION.md` - 店铺编辑

### 预约系统
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 预约 API
- `.cursor/Appoitment.md` - 预约逻辑
- `.docs/APPOINTMENT_SYSTEM_DOCUMENTATION.md` - 完整实现

### 客户管理
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 客户 API
- `.docs/CUSTOMER_SERVICE_IMPLEMENTATION.md` - 客户服务实现

### 员工管理
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 员工 API
- `.cursor/staff.md` - 员工管理逻辑
- `.docs/STAFF_MANAGEMENT_README.md` - 员工管理实现

### 国际化
- **[DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)** - i18n 配置
- `.docs/I18N_SETUP.md` - 国际化设置
- `.docs/STORE_EDIT_I18N_IMPLEMENTATION.md` - 店铺编辑国际化

### 文件上传
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 上传 API
- `.docs/PHOTO_UPLOAD_INTEGRATION_GUIDE.md` - 照片上传集成

### 地图服务
- **[API_REFERENCE.md](./API_REFERENCE.md)** - 地图 API
- `.cursor/Geo.md` - 地理位置服务
- `.docs/SMART_ADDRESS_SEARCH_IMPLEMENTATION.md` - 智能地址搜索

---

## 📝 文档更新日志

### v1.0.0 (2024-01-15)
- ✅ 创建完整的开发者文档
- ✅ 创建快速开始指南
- ✅ 创建详细的 API 参考文档
- ✅ 创建文档索引
- ✅ 更新 README.md 引用

---

## 🤝 贡献文档

### 添加新文档
1. 在相应目录创建文档
2. 更新此索引文件
3. 添加文档链接和描述

### 文档规范
- 使用 Markdown 格式
- 包含目录和章节导航
- 提供代码示例
- 添加使用场景说明

---

## 📞 获取帮助

- **GitHub Issues**: [项目 Issues 页面]
- **文档问题**: 检查相关文档章节
- **代码示例**: 查看 `src/app/` 目录中的实现

---

*文档索引 - v1.0.0* 