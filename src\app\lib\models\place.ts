import { BaseModel, BaseModelImpl, JsonUtil } from './base_model';

/**
 * Business status enum for places
 */
export enum BusinessStatus {
  OPERATIONAL = 'OPERATIONAL',
  CLOSED_TEMPORARILY = 'CLOSED_TEMPORARILY',
  CLOSED_PERMANENTLY = 'CLOSED_PERMANENTLY'
}

/**
 * Address interface for structured address information
 */
export interface Address {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  province: string;
  country: string;
  postCode: string;
}

/**
 * Geographic location coordinates
 */
export interface GeoLocation {
  lat: number;
  lng: number;
}

/**
 * Display name structure for places
 */
export interface GeoDisplayName {
  text: string;
  languageCode?: string;
}

/**
 * Postal address structure
 */
export interface GeoPostalAddress {
  regionCode?: string;
  languageCode?: string;
  postalCode?: string;
  sortingCode?: string;
  administrativeArea?: string;
  locality?: string;
  sublocality?: string;
  addressLines?: string[];
}

/**
 * Opening hours structure
 */
export interface GeoOpeningHours {
  openNow?: boolean;
  periods?: Array<{
    open: {
      day: number;
      time: string;
    };
    close?: {
      day: number;
      time: string;
    };
  }>;
  weekdayText?: string[];
}

/**
 * Photo structure for places
 */
export interface GeoPhoto {
  name: string;
  widthPx?: number;
  heightPx?: number;
  authorAttributions?: Array<{
    displayName: string;
    uri?: string;
    photoUri?: string;
  }>;
}

/**
 * Review structure for places
 */
export interface GeoReviews {
  name: string;
  relativePublishTimeDescription?: string;
  rating?: number;
  text?: {
    text: string;
    languageCode?: string;
  };
  originalText?: {
    text: string;
    languageCode?: string;
  };
  authorAttribution?: {
    displayName: string;
    uri?: string;
    photoUri?: string;
  };
  publishTime?: string;
}

/**
 * Main GeoPlace model based on BaseModel structure
 * Represents a place that can be either Google Maps registered or OneNata custom place
 */
export interface GeoPlace extends BaseModel {
  ONPlaceId: string; // UUID v4 generated place ID (same as sid)
  GMapPlaceId?: string; // Google Maps place ID (if available)
  internationalPhoneNumber?: string;
  types?: string[]; // Place types from Google Maps
  allowsDogs?: boolean;
  formattedAddress?: string;
  displayName?: GeoDisplayName;
  postalAddress?: GeoPostalAddress;
  businessStatus?: BusinessStatus;
  location?: GeoLocation;
  regularOpeningHours?: GeoOpeningHours;
  photos?: GeoPhoto[];
  rating?: number;
  reviews?: GeoReviews[];
  storeId?: string; // OneNata admin store ID
}

/**
 * Implementation class for GeoPlace model
 */
export class GeoPlaceImpl extends BaseModelImpl implements GeoPlace {
  ONPlaceId: string;
  GMapPlaceId?: string;
  internationalPhoneNumber?: string;
  types?: string[];
  allowsDogs?: boolean;
  formattedAddress?: string;
  displayName?: GeoDisplayName;
  postalAddress?: GeoPostalAddress;
  businessStatus?: BusinessStatus;
  location?: GeoLocation;
  regularOpeningHours?: GeoOpeningHours;
  photos?: GeoPhoto[];
  rating?: number;
  reviews?: GeoReviews[];
  storeId?: string;

  constructor(data: Partial<GeoPlace>) {
    super(data);
    this.ONPlaceId = data.ONPlaceId || data.sid || '';
    this.GMapPlaceId = data.GMapPlaceId;
    this.internationalPhoneNumber = data.internationalPhoneNumber;
    this.types = data.types;
    this.allowsDogs = data.allowsDogs;
    this.formattedAddress = data.formattedAddress;
    this.displayName = data.displayName;
    this.postalAddress = data.postalAddress;
    this.businessStatus = data.businessStatus;
    this.location = data.location;
    this.regularOpeningHours = data.regularOpeningHours;
    this.photos = data.photos;
    this.rating = data.rating;
    this.reviews = data.reviews;
    this.storeId = data.storeId;
  }

  static fromJson(json: Record<string, unknown>): GeoPlaceImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new GeoPlaceImpl({
      ...baseModel,
      ONPlaceId: JsonUtil.stringFromJson(json.ONPlaceId) || JsonUtil.stringFromJson(json.sid) || '',
      GMapPlaceId: JsonUtil.stringFromJson(json.GMapPlaceId),
      internationalPhoneNumber: JsonUtil.stringFromJson(json.internationalPhoneNumber),
      types: json.types as string[],
      allowsDogs: JsonUtil.boolFromJson(json.allowsDogs),
      formattedAddress: JsonUtil.stringFromJson(json.formattedAddress),
      displayName: json.displayName as GeoDisplayName,
      postalAddress: json.postalAddress as GeoPostalAddress,
      businessStatus: json.businessStatus as BusinessStatus,
      location: json.location as GeoLocation,
      regularOpeningHours: json.regularOpeningHours as GeoOpeningHours,
      photos: json.photos as GeoPhoto[],
      rating: JsonUtil.numberFromJson(json.rating),
      reviews: json.reviews as GeoReviews[],
      storeId: JsonUtil.stringFromJson(json.storeId),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      ONPlaceId: this.ONPlaceId,
      GMapPlaceId: JsonUtil.stringToJson(this.GMapPlaceId),
      internationalPhoneNumber: JsonUtil.stringToJson(this.internationalPhoneNumber),
      types: this.types,
      allowsDogs: JsonUtil.boolToJson(this.allowsDogs),
      formattedAddress: JsonUtil.stringToJson(this.formattedAddress),
      displayName: this.displayName,
      postalAddress: this.postalAddress,
      businessStatus: this.businessStatus,
      location: this.location,
      regularOpeningHours: this.regularOpeningHours,
      photos: this.photos,
      rating: JsonUtil.numberToJson(this.rating),
      reviews: this.reviews,
      storeId: JsonUtil.stringToJson(this.storeId),
    };
  }

  /**
   * Check if this place is registered in Google Maps
   */
  isGoogleMapsPlace(): boolean {
    return !!this.GMapPlaceId;
  }

  /**
   * Get the primary place ID (Google Maps ID if available, otherwise OneNata ID)
   */
  getPrimaryPlaceId(): string {
    return this.GMapPlaceId || this.ONPlaceId;
  }
}

/**
 * Legacy Place interface for backward compatibility
 */
export interface Place {
  placeId: string;
  googlePlaceId?: string;
  name: string;
  formattedAddress: Address;
  location: {
    lat: number;
    lng: number;
  };
  addressComponents: {
    street?: string;
    streetNumber?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
}

/**
 * Places API response types
 */
export interface PlacesResponse {
  success: boolean;
  data?: Place[];
  error?: string;
  message?: string;
  total?: number;
}

export interface PlaceResponse {
  success: boolean;
  data?: Place;
  error?: string;
  message?: string;
}

export interface GeoPlaceResponse {
  success: boolean;
  data?: GeoPlace;
  error?: string;
  message?: string;
}

/**
 * Google Places API types
 */
export interface PlacePrediction {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
}

export interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface PlaceResult {
  place_id: string;
  name: string;
  formatted_address: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  vicinity?: string;
  address_components: AddressComponent[];
  types?: string[];
  website?: string;
  formatted_phone_number?: string;
  international_phone_number?: string;
  business_status?: string;
  rating?: number;
  user_ratings_total?: number;
  opening_hours?: {
    open_now?: boolean;
    weekday_text?: string[];
  };
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
    html_attributions: string[];
  }>;
}

/**
 * Address validation result
 */
export interface AddressValidationResult {
  isValid: boolean;
  hasGooglePlace: boolean;
  googlePlaceId?: string;
  formattedAddress?: string;
  location?: GeoLocation;
  message?: string;
  error?: string;
}