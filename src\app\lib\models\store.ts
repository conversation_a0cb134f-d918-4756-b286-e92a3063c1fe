import { BaseModel, BaseModelImpl, JsonUtil } from './base_model';
import { 
  StoreVerifiedStatus,
  StoreStatus,
  BusinessType,
  ServiceStatus,
  ProductStatus,
  InventoryStatus,
  TransactionStatus,
  PaymentStatus,
  EventType,
  EventStatus,
  Currency,
  Timestamp,
  Address,
  BusinessHours,
  Services,
  ProductAttributes,
  ServiceBreed,
  ServiceCategory
} from './types';

/**
 * Store Account - 店铺账户
 * 存储在 Firestore collection "store-account"
 */
export interface StoreAccount extends BaseModel {
  ownerId: string; // 店主 ID
  storeName: string;
  storeVerifiedStatus?: StoreVerifiedStatus;
  storeStatus: StoreStatus;
  googlePlaceId: string;
  salt?: string;
  hashedCredential?: string; // 店铺密码
}

export class StoreAccountImpl extends BaseModelImpl implements StoreAccount {
  ownerId: string;
  storeName: string;
  storeVerifiedStatus?: StoreVerifiedStatus;
  storeStatus: StoreStatus;
  googlePlaceId: string;
  salt?: string;
  hashedCredential?: string;

  constructor(data: Partial<StoreAccount>) {
    super(data);
    this.ownerId = data.ownerId || '';
    this.storeName = data.storeName || '';
    this.storeVerifiedStatus = data.storeVerifiedStatus;
    this.storeStatus = data.storeStatus || StoreStatus.ACTIVE;
    this.googlePlaceId = data.googlePlaceId || '';
    this.salt = data.salt;
    this.hashedCredential = data.hashedCredential;
  }

  static fromJson(json: Record<string, unknown>): StoreAccountImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new StoreAccountImpl({
      ...baseModel,
      ownerId: JsonUtil.stringFromJson(json.ownerId) || '',
      storeName: JsonUtil.stringFromJson(json.storeName) || '',
      storeVerifiedStatus: json.storeVerifiedStatus as StoreVerifiedStatus,
      storeStatus: (json.storeStatus as StoreStatus) || StoreStatus.ACTIVE,
      googlePlaceId: JsonUtil.stringFromJson(json.googlePlaceId) || '',
      salt: JsonUtil.stringFromJson(json.salt),
      hashedCredential: JsonUtil.stringFromJson(json.hashedCredential),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      ownerId: this.ownerId,
      storeName: this.storeName,
      storeVerifiedStatus: this.storeVerifiedStatus,
      storeStatus: this.storeStatus,
      googlePlaceId: this.googlePlaceId,
      salt: JsonUtil.stringToJson(this.salt),
      hashedCredential: JsonUtil.stringToJson(this.hashedCredential),
    };
  }

  isActive(): boolean {
    return this.storeStatus === StoreStatus.ACTIVE;
  }

  isVerified(): boolean {
    return this.storeVerifiedStatus === StoreVerifiedStatus.APPROVED;
  }
}

/**
 * Store Info - 店铺信息
 * 存储在 Firestore collection "store-info"
 */
export interface StoreInfo extends BaseModel {
  storeId: string; // Store account sid
  currentAddress: Address;
  phone: string;
  email: string;
  businessType: BusinessType;
  description?: string;
  services: Services;
  businessHours?: BusinessHours;
  businessTimeZone?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  staffs?: string[]; // Portal user sids
  hasSpecialEvent?: boolean;
  customerList?: string[]; // OneNata user sids
  appointmentOpen?: boolean;
  avatarUrl?: string;
  storePhotos?: string[]; // Firebase storage paths
  storeSubscriptionPlan?: string;
  website?: string; // Store website URL
}

export class StoreInfoImpl extends BaseModelImpl implements StoreInfo {
  storeId: string;
  currentAddress: Address;
  phone: string;
  email: string;
  businessType: BusinessType;
  description?: string;
  services: Services;
  businessHours?: BusinessHours;
  businessTimeZone?: string;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  staffs?: string[];
  hasSpecialEvent?: boolean;
  customerList?: string[];
  appointmentOpen?: boolean;
  avatarUrl?: string;
  storePhotos?: string[];
  storeSubscriptionPlan?: string;
  website?: string;

  constructor(data: Partial<StoreInfo>) {
    super(data);
    this.storeId = data.storeId || '';
    this.currentAddress = data.currentAddress || {
      addressLine1: '',
      city: '',
      province: '',
      country: '',
      postCode: ''
    };
    this.phone = data.phone || '';
    this.email = data.email || '';
    this.businessType = data.businessType || BusinessType.OTHER;
    this.description = data.description;
    this.services = data.services || {
      grooming: false,
      boarding: false,
      veterinary: false,
      training: false,
      retail: false
    };
    this.businessHours = data.businessHours;
    this.businessTimeZone = data.businessTimeZone;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.staffs = data.staffs;
    this.hasSpecialEvent = data.hasSpecialEvent;
    this.customerList = data.customerList;
    this.appointmentOpen = data.appointmentOpen;
    this.avatarUrl = data.avatarUrl;
    this.storePhotos = data.storePhotos;
    this.storeSubscriptionPlan = data.storeSubscriptionPlan;
    this.website = data.website;
  }

  static fromJson(json: Record<string, unknown>): StoreInfoImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new StoreInfoImpl({
      ...baseModel,
      storeId: JsonUtil.stringFromJson(json.storeId) || '',
      currentAddress: (json.currentAddress as Address) || {
        addressLine1: '',
        city: '',
        province: '',
        country: '',
        postCode: ''
      },
      phone: JsonUtil.stringFromJson(json.phone) || '',
      email: JsonUtil.stringFromJson(json.email) || '',
      businessType: (json.businessType as BusinessType) || BusinessType.OTHER,
      description: JsonUtil.stringFromJson(json.description),
      services: (json.services as Services) || {
        grooming: false,
        boarding: false,
        veterinary: false,
        training: false,
        retail: false
      },
      businessHours: json.businessHours as BusinessHours,
      businessTimeZone: JsonUtil.stringFromJson(json.businessTimeZone),
      createdAt: json.createdAt ? new Date(json.createdAt as string) : undefined,
      updatedAt: json.updatedAt ? new Date(json.updatedAt as string) : undefined,
      staffs: json.staffs as string[],
      hasSpecialEvent: JsonUtil.boolFromJson(json.hasSpecialEvent),
      customerList: json.customerList as string[],
      appointmentOpen: JsonUtil.boolFromJson(json.appointmentOpen),
      avatarUrl: JsonUtil.stringFromJson(json.avatarUrl),
      storePhotos: json.storePhotos as string[],
      storeSubscriptionPlan: JsonUtil.stringFromJson(json.storeSubscriptionPlan),
      website: JsonUtil.stringFromJson(json.website),
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      storeId: this.storeId,
      currentAddress: this.currentAddress,
      phone: this.phone,
      email: this.email,
      businessType: this.businessType,
      description: JsonUtil.stringToJson(this.description),
      services: this.services,
      businessHours: this.businessHours,
      businessTimeZone: JsonUtil.stringToJson(this.businessTimeZone),
      createdAt: this.createdAt ? new Date(this.createdAt).toISOString() : undefined,
      updatedAt: this.updatedAt ? new Date(this.updatedAt).toISOString() : undefined,
      staffs: this.staffs,
      hasSpecialEvent: JsonUtil.boolToJson(this.hasSpecialEvent),
      customerList: this.customerList,
      appointmentOpen: JsonUtil.boolToJson(this.appointmentOpen),
      avatarUrl: JsonUtil.stringToJson(this.avatarUrl),
      storePhotos: this.storePhotos,
      storeSubscriptionPlan: JsonUtil.stringToJson(this.storeSubscriptionPlan),
      website: JsonUtil.stringToJson(this.website),
    };
  }

  getFullAddress(): string {
    const { addressLine1, addressLine2, city, province, postCode } = this.currentAddress;
    const parts = [addressLine1, addressLine2, city, province, postCode].filter(Boolean);
    return parts.join(', ');
  }

  isAppointmentAvailable(): boolean {
    return this.appointmentOpen === true;
  }

  getAvailableServices(): string[] {
    const availableServices: string[] = [];
    if (this.services.grooming) availableServices.push('grooming');
    if (this.services.boarding) availableServices.push('boarding');
    if (this.services.veterinary) availableServices.push('veterinary');
    if (this.services.training) availableServices.push('training');
    if (this.services.retail) availableServices.push('retail');
    return availableServices;
  }
}

/**
 * Product - 产品
 */
export interface Product extends BaseModel {
  productName: string;
  price: number;
  currency: Currency;
  description: string;
  fragileItems: boolean;
  sku: string;
  brand: string;
  attributes: ProductAttributes;
  status: ProductStatus;
  photoURL: string[];
}

export class ProductImpl extends BaseModelImpl implements Product {
  productName: string;
  price: number;
  currency: Currency;
  description: string;
  fragileItems: boolean;
  sku: string;
  brand: string;
  attributes: ProductAttributes;
  status: ProductStatus;
  photoURL: string[];

  constructor(data: Partial<Product>) {
    super(data);
    this.productName = data.productName || '';
    this.price = data.price || 0;
    this.currency = data.currency || Currency.CAD;
    this.description = data.description || '';
    this.fragileItems = data.fragileItems || false;
    this.sku = data.sku || '';
    this.brand = data.brand || '';
    this.attributes = data.attributes || {};
    this.status = data.status || ProductStatus.ACTIVE;
    this.photoURL = data.photoURL || [];
  }

  static fromJson(json: Record<string, unknown>): ProductImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new ProductImpl({
      ...baseModel,
      productName: JsonUtil.stringFromJson(json.productName) || '',
      price: JsonUtil.numberFromJson(json.price) || 0,
      currency: (json.currency as Currency) || Currency.CAD,
      description: JsonUtil.stringFromJson(json.description) || '',
      fragileItems: JsonUtil.boolFromJson(json.fragileItems) || false,
      sku: JsonUtil.stringFromJson(json.sku) || '',
      brand: JsonUtil.stringFromJson(json.brand) || '',
      attributes: (json.attributes as ProductAttributes) || {},
      status: (json.status as ProductStatus) || ProductStatus.ACTIVE,
      photoURL: (json.photoURL as string[]) || [],
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      productName: this.productName,
      price: this.price,
      currency: this.currency,
      description: this.description,
      fragileItems: this.fragileItems,
      sku: this.sku,
      brand: this.brand,
      attributes: this.attributes,
      status: this.status,
      photoURL: this.photoURL,
    };
  }

  isActive(): boolean {
    return this.status === ProductStatus.ACTIVE;
  }

  getFormattedPrice(): string {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: this.currency
    }).format(this.price);
  }
}

/**
 * Inventory - 库存
 */
export interface Inventory extends BaseModel {
  productId: string;
  warehouseId: string;
  stock: number;
  reserved: number; // 已锁定库存
  available: number; // 可售库存 = stock - reserved
  lowStockThreshold: number; // 低库存预警
  location?: string; // 仓库具体存储位置
  status: InventoryStatus;
}

export class InventoryImpl extends BaseModelImpl implements Inventory {
  productId: string;
  warehouseId: string;
  stock: number;
  reserved: number;
  available: number;
  lowStockThreshold: number;
  location?: string;
  status: InventoryStatus;

  constructor(data: Partial<Inventory>) {
    super(data);
    this.productId = data.productId || '';
    this.warehouseId = data.warehouseId || '';
    this.stock = data.stock || 0;
    this.reserved = data.reserved || 0;
    this.available = data.available || 0;
    this.lowStockThreshold = data.lowStockThreshold || 10;
    this.location = data.location;
    this.status = data.status || InventoryStatus.IN_STOCK;
  }

  static fromJson(json: Record<string, unknown>): InventoryImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new InventoryImpl({
      ...baseModel,
      productId: JsonUtil.stringFromJson(json.productId) || '',
      warehouseId: JsonUtil.stringFromJson(json.warehouseId) || '',
      stock: JsonUtil.numberFromJson(json.stock) || 0,
      reserved: JsonUtil.numberFromJson(json.reserved) || 0,
      available: JsonUtil.numberFromJson(json.available) || 0,
      lowStockThreshold: JsonUtil.numberFromJson(json.lowStockThreshold) || 10,
      location: JsonUtil.stringFromJson(json.location),
      status: (json.status as InventoryStatus) || InventoryStatus.IN_STOCK,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      productId: this.productId,
      warehouseId: this.warehouseId,
      stock: this.stock,
      reserved: this.reserved,
      available: this.available,
      lowStockThreshold: this.lowStockThreshold,
      location: JsonUtil.stringToJson(this.location),
      status: this.status,
    };
  }

  isLowStock(): boolean {
    return this.available <= this.lowStockThreshold;
  }

  isOutOfStock(): boolean {
    return this.available <= 0;
  }

  updateAvailable(): void {
    this.available = Math.max(0, this.stock - this.reserved);
  }
}

/**
 * Store Service - 店铺服务
 */
export interface StoreService extends BaseModel {
  serviceId: string; // Service SID
  storeId: string;
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  description?: string;
  serviceEmployeeUid: string;
  commission?: number; // 佣金比例 (0-1)
  updateTime: Timestamp;
  serviceStatus: ServiceStatus;
  servicePhotos?: string[];
  staffServiceIds: string[];
  isOnlineBookingEnabled: boolean;
  requiresApproval: boolean;
  cancellationPolicy?: string;
  totalBookings: number;
  completedBookings: number;
  currency: Currency; 
}

export class StoreServiceImpl extends BaseModelImpl implements StoreService {
  storeId: string;
  serviceName: string;
  serviceCategory: ServiceCategory;
  serviceBreed: ServiceBreed;
  description?: string;
  serviceId: string;
  serviceEmployeeUid: string;
  // price: number;
  commission: number;
  updateTime: Timestamp;
  serviceStatus: ServiceStatus;
  servicePhotos?: string[];
  staffServiceIds: string[];
  isOnlineBookingEnabled: boolean;
  requiresApproval: boolean;
  cancellationPolicy?: string;
  totalBookings: number;
  completedBookings: number;
  currency: Currency;

  constructor(data: Partial<StoreService>) {
    super(data);
    this.storeId = data.storeId || '';
    this.serviceName = data.serviceName || '';
    this.serviceCategory = data.serviceCategory || ServiceCategory.GROOMING;
    this.serviceBreed = data.serviceBreed || ServiceBreed.DOG;
    this.description = data.description;
    this.serviceId = data.serviceId || '';
    this.serviceEmployeeUid = data.serviceEmployeeUid || '';
    // ÷this.price = data.price || 0;
    this.commission = data.commission || 0;
    this.updateTime = data.updateTime || new Date();
    this.serviceStatus = data.serviceStatus || ServiceStatus.ACTIVE;
    this.servicePhotos = data.servicePhotos || [];
    this.staffServiceIds = data.staffServiceIds || [];
    this.isOnlineBookingEnabled = data.isOnlineBookingEnabled || false;
    this.requiresApproval = data.requiresApproval || false;
    this.cancellationPolicy = data.cancellationPolicy || '';
    this.totalBookings = data.totalBookings || 0;
    this.completedBookings = data.completedBookings || 0;
    this.currency = data.currency || Currency.CAD;
  }

  static fromJson(json: Record<string, unknown>): StoreServiceImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new StoreServiceImpl({
      ...baseModel,
      storeId: JsonUtil.stringFromJson(json.storeId) || '',
      serviceName: JsonUtil.stringFromJson(json.serviceName) || '',
      serviceCategory: (json.serviceCategory as ServiceCategory) || ServiceCategory.GROOMING,
      serviceBreed: (json.serviceBreed as ServiceBreed) || ServiceBreed.DOG,
      description: JsonUtil.stringFromJson(json.description),
      serviceId: JsonUtil.stringFromJson(json.serviceId) || '',
      serviceEmployeeUid: JsonUtil.stringFromJson(json.serviceEmployeeUid) || '',
      // price: JsonUtil.numberFromJson(json.price) || 0,
      commission: JsonUtil.numberFromJson(json.commission) || 0,
      updateTime: json.updateTime ? new Date(json.updateTime as string) : new Date(),
      serviceStatus: (json.serviceStatus as ServiceStatus) || ServiceStatus.ACTIVE,
      servicePhotos: (json.servicePhotos as string[]) || [],
      staffServiceIds: (json.staffServiceIds as string[]) || [],
      isOnlineBookingEnabled: JsonUtil.boolFromJson(json.isOnlineBookingEnabled) || false,
      requiresApproval: JsonUtil.boolFromJson(json.requiresApproval) || false,
      cancellationPolicy: JsonUtil.stringFromJson(json.cancellationPolicy),
      totalBookings: JsonUtil.numberFromJson(json.totalBookings) || 0,
      completedBookings: JsonUtil.numberFromJson(json.completedBookings) || 0,
      currency: (json.currency as Currency) || Currency.CAD,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      storeId: this.storeId,
      serviceName: this.serviceName,
      serviceCategory: this.serviceCategory,
      serviceBreed: this.serviceBreed,
      description: JsonUtil.stringToJson(this.description),
      serviceId: this.serviceId,
      serviceEmployeeUid: this.serviceEmployeeUid,
      // price: this.price,
      commission: this.commission,
      updateTime: new Date(this.updateTime).toISOString(),
      serviceStatus: this.serviceStatus,
      servicePhotos: this.servicePhotos,
      staffServiceIds: this.staffServiceIds,
      isOnlineBookingEnabled: this.isOnlineBookingEnabled,
      requiresApproval: this.requiresApproval,
      cancellationPolicy: this.cancellationPolicy,
      totalBookings: this.totalBookings,
      completedBookings: this.completedBookings,
      currency: this.currency,
    };
  }

  isActive(): boolean {
    return this.serviceStatus === ServiceStatus.ACTIVE;
  }

  // getCommissionAmount(): number {
  //   // return this.price * this.commission;
  //   return 0;
  // }

  // getFormattedPrice(): string {
  //   return new Intl.NumberFormat('en-CA', {
  //     style: 'currency',
  //     currency: Currency.CAD
  //   }).format(this.price);
  // }
}

/**
 * Store Service Transaction - 店铺服务交易
 */
export interface StoreServiceTransaction extends BaseModel {
  storeServiceId: string; // StoreService SID
  serviceEmployeeTransactionId: string;
  updateTime: Timestamp;
  paymentId: string;
  reviewId?: string;
  transactionStatus: TransactionStatus;
  paymentStatus: PaymentStatus;
}

export class StoreServiceTransactionImpl extends BaseModelImpl implements StoreServiceTransaction {
  storeServiceId: string;
  serviceEmployeeTransactionId: string;
  updateTime: Timestamp;
  paymentId: string;
  reviewId?: string;
  transactionStatus: TransactionStatus;
  paymentStatus: PaymentStatus;

  constructor(data: Partial<StoreServiceTransaction>) {
    super(data);
    this.storeServiceId = data.storeServiceId || '';
    this.serviceEmployeeTransactionId = data.serviceEmployeeTransactionId || '';
    this.updateTime = data.updateTime || new Date();
    this.paymentId = data.paymentId || '';
    this.reviewId = data.reviewId;
    this.transactionStatus = data.transactionStatus || TransactionStatus.DRAFT;
    this.paymentStatus = data.paymentStatus || PaymentStatus.UNPAID;
  }

  static fromJson(json: Record<string, unknown>): StoreServiceTransactionImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new StoreServiceTransactionImpl({
      ...baseModel,
      storeServiceId: JsonUtil.stringFromJson(json.storeServiceId) || '',
      serviceEmployeeTransactionId: JsonUtil.stringFromJson(json.serviceEmployeeTransactionId) || '',
      updateTime: json.updateTime ? new Date(json.updateTime as string) : new Date(),
      paymentId: JsonUtil.stringFromJson(json.paymentId) || '',
      reviewId: JsonUtil.stringFromJson(json.reviewId),
      transactionStatus: (json.transactionStatus as TransactionStatus) || TransactionStatus.DRAFT,
      paymentStatus: (json.paymentStatus as PaymentStatus) || PaymentStatus.UNPAID,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      storeServiceId: this.storeServiceId,
      serviceEmployeeTransactionId: this.serviceEmployeeTransactionId,
      updateTime: new Date(this.updateTime).toISOString(),
      paymentId: this.paymentId,
      reviewId: JsonUtil.stringToJson(this.reviewId),
      transactionStatus: this.transactionStatus,
      paymentStatus: this.paymentStatus,
    };
  }

  isCompleted(): boolean {
    return this.transactionStatus === TransactionStatus.COMPLETED;
  }
}

/**
 * Store Review - 店铺评价
 */
export interface StoreReview extends BaseModel {
  storeId: string; // Store SID
  reviewerId: string; // OneNata App 用户 SID
  reviewText: string;
  reviewLanguageCode: string;
  rating: number; // 评分 1-5
  transactionId: string; // 交易 SID
  photos: string[];
}

export class StoreReviewImpl extends BaseModelImpl implements StoreReview {
  storeId: string;
  reviewerId: string;
  reviewText: string;
  reviewLanguageCode: string;
  rating: number;
  transactionId: string;
  photos: string[];

  constructor(data: Partial<StoreReview>) {
    super(data);
    this.storeId = data.storeId || '';
    this.reviewerId = data.reviewerId || '';
    this.reviewText = data.reviewText || '';
    this.reviewLanguageCode = data.reviewLanguageCode || 'en';
    this.rating = data.rating || 5;
    this.transactionId = data.transactionId || '';
    this.photos = data.photos || [];
  }

  static fromJson(json: Record<string, unknown>): StoreReviewImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new StoreReviewImpl({
      ...baseModel,
      storeId: JsonUtil.stringFromJson(json.storeId) || '',
      reviewerId: JsonUtil.stringFromJson(json.reviewerId) || '',
      reviewText: JsonUtil.stringFromJson(json.reviewText) || '',
      reviewLanguageCode: JsonUtil.stringFromJson(json.reviewLanguageCode) || 'en',
      rating: JsonUtil.numberFromJson(json.rating) || 5,
      transactionId: JsonUtil.stringFromJson(json.transactionId) || '',
      photos: (json.photos as string[]) || [],
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      storeId: this.storeId,
      reviewerId: this.reviewerId,
      reviewText: this.reviewText,
      reviewLanguageCode: this.reviewLanguageCode,
      rating: this.rating,
      transactionId: this.transactionId,
      photos: this.photos,
    };
  }

  getRatingStars(): string {
    return '★'.repeat(this.rating) + '☆'.repeat(5 - this.rating);
  }
}

/**
 * Special Event - 特殊活动
 */
export interface SpecialEvent extends BaseModel {
  eventFullName: string;
  eventSlogan: string;
  eventDescription: string;
  eventLocation: string;
  eventType: EventType;
  eventStatus: EventStatus;
  eventImageUrl: string;
  eventLink: string;
  eventStartTime: Timestamp;
  eventEndTime: Timestamp;
  eventUpdatedAt: Timestamp;
  eventMaximumParticipants: number;
  eventStaffs: string[];
  eventParticipants: string[];
  eventAddress?: Address;
}

export class SpecialEventImpl extends BaseModelImpl implements SpecialEvent {
  eventFullName: string;
  eventSlogan: string;
  eventDescription: string;
  eventLocation: string;
  eventType: EventType;
  eventStatus: EventStatus;
  eventImageUrl: string;
  eventLink: string;
  eventStartTime: Timestamp;
  eventEndTime: Timestamp;
  eventUpdatedAt: Timestamp;
  eventMaximumParticipants: number;
  eventStaffs: string[];
  eventParticipants: string[];
  eventAddress?: Address;

  constructor(data: Partial<SpecialEvent>) {
    super(data);
    this.eventFullName = data.eventFullName || '';
    this.eventSlogan = data.eventSlogan || '';
    this.eventDescription = data.eventDescription || '';
    this.eventLocation = data.eventLocation || '';
    this.eventType = data.eventType || EventType.OTHER;
    this.eventStatus = data.eventStatus || EventStatus.PENDING;
    this.eventImageUrl = data.eventImageUrl || '';
    this.eventLink = data.eventLink || '';
    this.eventStartTime = data.eventStartTime || new Date();
    this.eventEndTime = data.eventEndTime || new Date();
    this.eventUpdatedAt = data.eventUpdatedAt || new Date();
    this.eventMaximumParticipants = data.eventMaximumParticipants || 0;
    this.eventStaffs = data.eventStaffs || [];
    this.eventParticipants = data.eventParticipants || [];
    this.eventAddress = data.eventAddress;
  }

  static fromJson(json: Record<string, unknown>): SpecialEventImpl {
    const baseModel = BaseModelImpl.fromJson(json);
    return new SpecialEventImpl({
      ...baseModel,
      eventFullName: JsonUtil.stringFromJson(json.eventFullName) || '',
      eventSlogan: JsonUtil.stringFromJson(json.eventSlogan) || '',
      eventDescription: JsonUtil.stringFromJson(json.eventDescription) || '',
      eventLocation: JsonUtil.stringFromJson(json.eventLocation) || '',
      eventType: (json.eventType as EventType) || EventType.OTHER,
      eventStatus: (json.eventStatus as EventStatus) || EventStatus.PENDING,
      eventImageUrl: JsonUtil.stringFromJson(json.eventImageUrl) || '',
      eventLink: JsonUtil.stringFromJson(json.eventLink) || '',
      eventStartTime: json.eventStartTime ? new Date(json.eventStartTime as string) : new Date(),
      eventEndTime: json.eventEndTime ? new Date(json.eventEndTime as string) : new Date(),
      eventUpdatedAt: json.eventUpdatedAt ? new Date(json.eventUpdatedAt as string) : new Date(),
      eventMaximumParticipants: JsonUtil.numberFromJson(json.eventMaximumParticipants) || 0,
      eventStaffs: (json.eventStaffs as string[]) || [],
      eventParticipants: (json.eventParticipants as string[]) || [],
      eventAddress: json.eventAddress as Address,
    });
  }

  toJson(): Record<string, unknown> {
    return {
      ...super.toJson(),
      eventFullName: this.eventFullName,
      eventSlogan: this.eventSlogan,
      eventDescription: this.eventDescription,
      eventLocation: this.eventLocation,
      eventType: this.eventType,
      eventStatus: this.eventStatus,
      eventImageUrl: this.eventImageUrl,
      eventLink: this.eventLink,
      eventStartTime: new Date(this.eventStartTime).toISOString(),
      eventEndTime: new Date(this.eventEndTime).toISOString(),
      eventUpdatedAt: new Date(this.eventUpdatedAt).toISOString(),
      eventMaximumParticipants: this.eventMaximumParticipants,
      eventStaffs: this.eventStaffs,
      eventParticipants: this.eventParticipants,
      eventAddress: this.eventAddress,
    };
  }

  isActive(): boolean {
    return this.eventStatus === EventStatus.ACTIVE;
  }

  isUpcoming(): boolean {
    const now = new Date();
    return new Date(this.eventStartTime) > now;
  }

  isOngoing(): boolean {
    const now = new Date();
    return new Date(this.eventStartTime) <= now && new Date(this.eventEndTime) >= now;
  }

  isCompleted(): boolean {
    return this.eventStatus === EventStatus.COMPLETED || new Date(this.eventEndTime) < new Date();
  }

  getAvailableSpots(): number {
    return Math.max(0, this.eventMaximumParticipants - this.eventParticipants.length);
  }

  isFull(): boolean {
    return this.eventParticipants.length >= this.eventMaximumParticipants;
  }
} 