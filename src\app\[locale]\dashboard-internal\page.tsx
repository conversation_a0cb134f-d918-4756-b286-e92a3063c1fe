'use client';

import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { useAuth, AdminRoute } from '../../lib/firebase/context/AuthContext';
import StoreService, { StoreListItem } from '../../lib/services/store_services';
import { StoreVerifiedStatus, BusinessType, StoreStatus } from '../../lib/models/types';
import { 
  MdPerson, 
  MdSettings, 
  MdKeyboardArrowDown,
  MdRefresh
} from 'react-icons/md';
// import NotificationCenter from '../components/notifications/NotificationCenter';

interface StoreStats {
  totalStores: number;
  pendingStores: number;
  approvedStores: number;
  rejectedStores: number;
  totalUsers: number;
  notificationCount: number;
}

export default function DashboardInternalPage() {
  const { userData, user, signOut } = useAuth();
  const t = useTranslations('common');
  const [loading, setLoading] = useState(true);
  const [stores, setStores] = useState<StoreListItem[]>([]);
  const [stats, setStats] = useState<StoreStats | null>(null);
  const [selectedTab, setSelectedTab] = useState<'all' | 'pending' | 'approved' | 'rejected'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showStoreModal, setShowStoreModal] = useState(false);
  const [selectedStore, setSelectedStore] = useState<StoreListItem | null>(null);
  const [approvalReason, setApprovalReason] = useState('');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [storeOwnerInfo, setStoreOwnerInfo] = useState<{
    displayName: string;
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber?: string;
    userType: string;
    photoURL?: string;
    createdAt: Date;
  } | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editFormData, setEditFormData] = useState({
    name: '',
    phone: '',
    email: '',
    description: '',
    businessType: '',
    addressLine1: '',
    city: '',
    province: '',
    country: '',
    postCode: ''
  });
  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [showBatchModal, setShowBatchModal] = useState(false);
  const [batchAction, setBatchAction] = useState<'approve' | 'reject' | 'suspend' | 'activate'>('approve');

  // 加载数据
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // 并行加载统计数据和店铺列表
      const [statsResult, storesResult] = await Promise.all([
        StoreService.getAllStoresStats(),
        StoreService.getAllStores()
      ]);

      if (statsResult.success && statsResult.data) {
        setStats(statsResult.data);
      }

      if (storesResult.success && storesResult.data) {
        setStores(storesResult.data);
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理登出
  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  // 筛选店铺
  const filteredStores = stores.filter(store => {
    const matchesTab = selectedTab === 'all' || 
      (selectedTab === 'pending' && store.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.PENDING) ||
      (selectedTab === 'approved' && store.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.APPROVED) ||
      (selectedTab === 'rejected' && store.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.REJECTED);

    const matchesSearch = !searchTerm || 
      store.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.phone?.includes(searchTerm);

    return matchesTab && matchesSearch;
  });

  // 更新店铺审核状态
  const updateStoreStatus = async (storeId: string, status: StoreVerifiedStatus, reason?: string) => {
    if (!userData?.uid) return;

    try {
      const result = await StoreService.updateStoreVerificationStatus(
        storeId,
        status,
        userData.uid,
        reason
      );

      if (result.success) {
        // 重新加载数据
        await loadData();
        setShowStoreModal(false);
        setSelectedStore(null);
        setApprovalReason('');
      } else {
        alert('更新失败: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating store status:', error);
      alert('更新失败');
    }
  };

  // 获取店铺创建者信息
  const loadStoreOwnerInfo = async (ownerId: string) => {
    try {
      const result = await StoreService.getStoreOwnerInfo(ownerId);
      if (result.success && result.data) {
        setStoreOwnerInfo(result.data);
      }
    } catch (error) {
      console.error('Error loading store owner info:', error);
    }
  };

  // 修改店铺信息
  const updateStoreInfo = async () => {
    if (!selectedStore || !userData?.uid) return;

    try {
      const result = await StoreService.updateStoreInfoByAdmin(
        selectedStore.storeId,
        {
          name: editFormData.name,
          phone: editFormData.phone,
          email: editFormData.email,
          description: editFormData.description,
          businessType: editFormData.businessType as BusinessType,
          currentAddress: {
            addressLine1: editFormData.addressLine1,
            city: editFormData.city,
            province: editFormData.province,
            country: editFormData.country,
            postCode: editFormData.postCode
          }
        },
        userData.uid
      );

      if (result.success) {
        await loadData();
        setShowEditModal(false);
        setEditFormData({
          name: '',
          phone: '',
          email: '',
          description: '',
          businessType: '',
          addressLine1: '',
          city: '',
          province: '',
          country: '',
          postCode: ''
        });
        alert('店铺信息更新成功');
      } else {
        alert('更新失败: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating store info:', error);
      alert('更新失败');
    }
  };

  // 让店铺歇业/恢复营业
  const toggleStoreStatus = async (storeId: string, status: 'active' | 'temp-closed' | 'deactivated') => {
    if (!userData?.uid) return;

    try {
      const result = await StoreService.toggleStoreStatus(
        storeId,
        status as StoreStatus,
        userData.uid,
        status === 'temp-closed' ? '管理员暂停营业' : status === 'deactivated' ? '管理员停业' : '管理员恢复营业'
      );

      if (result.success) {
        await loadData();
        alert(status === 'active' ? '店铺已恢复营业' : '店铺已歇业');
      } else {
        alert('操作失败: ' + result.error);
      }
    } catch (error) {
      console.error('Error toggling store status:', error);
      alert('操作失败');
    }
  };

  // 批量操作店铺
  const batchUpdateStores = async () => {
    if (!userData?.uid || selectedStores.length === 0) return;

    try {
      const updates: Record<string, unknown> = {};
      
      switch (batchAction) {
        case 'approve':
          updates.storeVerifiedStatus = StoreVerifiedStatus.APPROVED;
          break;
        case 'reject':
          updates.storeVerifiedStatus = StoreVerifiedStatus.REJECTED;
          break;
        case 'suspend':
          updates.storeStatus = 'temp-closed';
          break;
        case 'activate':
          updates.storeStatus = 'active';
          break;
      }

      const result = await StoreService.batchUpdateStores(
        selectedStores,
        updates,
        userData.uid
      );

      if (result.success) {
        await loadData();
        setShowBatchModal(false);
        setSelectedStores([]);
        alert(`批量${batchAction === 'approve' ? '批准' : batchAction === 'reject' ? '拒绝' : batchAction === 'suspend' ? '暂停' : '激活'}成功`);
      } else {
        alert('批量操作失败: ' + result.error);
      }
    } catch (error) {
      console.error('Error batch updating stores:', error);
      alert('批量操作失败');
    }
  };

  // 获取状态标签样式
  const getStatusBadge = (status: StoreVerifiedStatus) => {
    switch (status) {
      case StoreVerifiedStatus.APPROVED:
        return 'bg-green-100 text-green-800';
      case StoreVerifiedStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case StoreVerifiedStatus.REJECTED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态文本
  const getStatusText = (status: StoreVerifiedStatus) => {
    switch (status) {
      case StoreVerifiedStatus.APPROVED:
        return '已批准';
      case StoreVerifiedStatus.PENDING:
        return '待审核';
      case StoreVerifiedStatus.REJECTED:
        return '已拒绝';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <AdminRoute>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">加载中...</p>
          </div>
        </div>
      </AdminRoute>
    );
  }

  return (
    <AdminRoute>
      <div className="min-h-screen bg-gradient-to-br from-[#FDECCE] via-[#F2D3A4] to-[#F2D3A4] p-8">
        <div className="max-w-7xl mx-auto">
          {/* 头部 */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20 mb-8 relative z-50">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-2">
                  OneNata 内部管理系统
                </h1>
                <p className="text-[#C6C6C6] text-lg">
                  欢迎回来，{userData?.displayName || '管理员'}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                {/* <NotificationCenter /> */}
                
                {/* 刷新按钮 */}
                <button 
                  onClick={loadData}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors flex items-center space-x-2"
                >
                  <MdRefresh className="h-5 w-5" />
                  <span>刷新</span>
                </button>
                
                {/* 用户菜单 */}
                <div className="relative">
                  <button
                    ref={buttonRef}
                    onClick={() => {
                      if (!showUserMenu && buttonRef.current) {
                        const rect = buttonRef.current.getBoundingClientRect();
                        setMenuPosition({
                          top: rect.bottom + 8,
                          right: window.innerWidth - rect.right
                        });
                      }
                      setShowUserMenu(!showUserMenu);
                    }}
                    className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gray-100 transition-colors"
                  >
                    {userData?.photoURL ? (
                      <img 
                        src={userData.photoURL} 
                        alt={userData.displayName || 'User'}
                        className="w-10 h-10 rounded-full"
                      />
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-gradient-to-br from-[#A126FF] to-[#8a20d8] flex items-center justify-center text-white font-semibold">
                        {userData?.firstName?.[0] || userData?.displayName?.[0] || 'A'}
                      </div>
                    )}
                    <div className="text-left">
                      <p className="text-sm font-medium text-gray-900">
                        {userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim() || '管理员'}
                      </p>
                      <p className="text-xs text-gray-500">
                        系统管理员
                      </p>
                    </div>
                    <MdKeyboardArrowDown className="w-4 h-4 text-gray-400" />
                  </button>
                </div>

                {/* Portal渲染的下拉菜单 */}
                {showUserMenu && typeof window !== 'undefined' && createPortal(
                  <>
                    {/* 背景遮罩 */}
                    <div 
                      className="fixed inset-0 z-[999999]"
                      onClick={() => setShowUserMenu(false)}
                    />
                    
                    {/* 菜单内容 */}
                    <div 
                      className="fixed w-56 bg-white rounded-xl shadow-xl border border-gray-200 z-[999999] py-2 drop-shadow-2xl"
                      style={{
                        top: `${menuPosition.top}px`,
                        right: `${menuPosition.right}px`
                      }}
                    >
                      <div className="px-4 py-3 border-b border-gray-100">
                        <p className="text-sm font-medium text-gray-900">
                          {userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim() || '管理员'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {user?.email}
                        </p>
                      </div>
                      
                      <div className="py-1">
                        <Link
                          href="/auth/profile-edit"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <MdPerson className="w-4 h-4 mr-3" />
                          {t('header.editProfile')}
                        </Link>
                        <Link
                          href="/settings"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <MdSettings className="w-4 h-4 mr-3" />
                          {t('header.settings')}
                        </Link>
                      </div>
                      
                      <div className="border-t border-gray-100 py-1">
                        <button
                          onClick={() => {
                            setShowUserMenu(false);
                            handleSignOut();
                          }}
                          className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                        >
                          <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                          </svg>
                          {t('logout')}
                        </button>
                      </div>
                    </div>
                  </>,
                  document.body
                )}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white/95 backdrop-blur-sm rounded-3xl shadow-2xl p-8 border border-white/20">
            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">总店铺数</p>
                    <p className="text-3xl font-bold">{stats?.totalStores || 0}</p>
                  </div>
                  <svg className="w-10 h-10 text-blue-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-2xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">总用户数</p>
                    <p className="text-3xl font-bold">{stats?.totalUsers || 0}</p>
                  </div>
                  <svg className="w-10 h-10 text-green-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-2.239" />
                  </svg>
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-2xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-sm">待审核店铺</p>
                    <p className="text-3xl font-bold">{stats?.pendingStores || 0}</p>
                  </div>
                  <svg className="w-10 h-10 text-yellow-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-2xl p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">消息通知</p>
                    <p className="text-3xl font-bold">{stats?.notificationCount || 0}</p>
                  </div>
                  <svg className="w-10 h-10 text-purple-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zM12 12l0 0" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636" />
                  </svg>
                </div>
              </div>
            </div>

            {/* 店铺管理区域 */}
            <div className="bg-white rounded-2xl shadow-lg border border-gray-100 mb-8">
              <div className="px-6 py-4 border-b border-gray-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">店铺管理</h3>
                    <p className="text-sm text-gray-600 mt-1">管理所有店铺的审核、编辑和状态</p>
                  </div>
                  <div className="mt-4 sm:mt-0 flex items-center space-x-4">
                    {/* 批量操作按钮 */}
                    {selectedStores.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">已选择 {selectedStores.length} 个店铺</span>
                        <button
                          onClick={() => setShowBatchModal(true)}
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                        >
                          批量操作
                        </button>
                      </div>
                    )}
                    
                    {/* 搜索框 */}
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="搜索店铺..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-purple-500 focus:border-purple-500"
                      />
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 标签页 */}
                <div className="mt-4 border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8">
                    <button
                      onClick={() => setSelectedTab('all')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        selectedTab === 'all'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      全部 ({stores.length})
                    </button>
                    <button
                      onClick={() => setSelectedTab('pending')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        selectedTab === 'pending'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      待审核 ({stats?.pendingStores || 0})
                    </button>
                    <button
                      onClick={() => setSelectedTab('approved')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        selectedTab === 'approved'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      已批准 ({stats?.approvedStores || 0})
                    </button>
                    <button
                      onClick={() => setSelectedTab('rejected')}
                      className={`py-2 px-1 border-b-2 font-medium text-sm ${
                        selectedTab === 'rejected'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      已拒绝 ({stats?.rejectedStores || 0})
                    </button>
                  </nav>
                </div>
              </div>

              {/* 店铺列表 */}
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <input
                          type="checkbox"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedStores(filteredStores.map(s => s.storeId));
                            } else {
                              setSelectedStores([]);
                            }
                          }}
                          className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                        />
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        店铺信息
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        联系方式
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建者
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        业务类型
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        创建时间
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        操作
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredStores.map((store) => (
                      <tr key={store.storeId} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedStores.includes(store.storeId)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedStores([...selectedStores, store.storeId]);
                              } else {
                                setSelectedStores(selectedStores.filter(id => id !== store.storeId));
                              }
                            }}
                            className="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                <span className="text-purple-600 font-medium text-sm">
                                  {store.name?.charAt(0) || '?'}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {store.name || '未知店铺'}
                              </div>
                              <div className="text-sm text-gray-500">
                                {store.currentAddress.city}, {store.currentAddress.province}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{store.phone}</div>
                          <div className="text-sm text-gray-500">{store.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <button
                            onClick={() => loadStoreOwnerInfo(store.accountInfo.ownerId)}
                            className="text-sm text-blue-600 hover:text-blue-900 underline"
                          >
                            查看创建者
                          </button>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {store.businessType.replace('_', ' ')}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadge(store.accountInfo.storeVerifiedStatus || StoreVerifiedStatus.PENDING)}`}>
                            {getStatusText(store.accountInfo.storeVerifiedStatus || StoreVerifiedStatus.PENDING)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {store.accountInfo.createdAt ? 
                            new Date(store.accountInfo.createdAt as unknown as string).toLocaleDateString('zh-CN') : 
                            '未知'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => {
                              setSelectedStore(store);
                              setShowStoreModal(true);
                            }}
                            className="text-purple-600 hover:text-purple-900 mr-2"
                          >
                            查看详情
                          </button>
                          <button
                            onClick={() => {
                              setSelectedStore(store);
                              setEditFormData({
                                name: store.name || '',
                                phone: store.phone || '',
                                email: store.email || '',
                                description: store.description || '',
                                businessType: store.businessType || '',
                                addressLine1: store.currentAddress.addressLine1 || '',
                                city: store.currentAddress.city || '',
                                province: store.currentAddress.province || '',
                                country: store.currentAddress.country || '',
                                postCode: store.currentAddress.postCode || ''
                              });
                              setShowEditModal(true);
                            }}
                            className="text-blue-600 hover:text-blue-900 mr-2"
                          >
                            编辑
                          </button>
                          {store.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.PENDING && (
                            <>
                              <button
                                onClick={() => updateStoreStatus(store.storeId, StoreVerifiedStatus.APPROVED)}
                                className="text-green-600 hover:text-green-900 mr-2"
                              >
                                批准
                              </button>
                              <button
                                onClick={() => updateStoreStatus(store.storeId, StoreVerifiedStatus.REJECTED)}
                                className="text-red-600 hover:text-red-900 mr-2"
                              >
                                拒绝
                              </button>
                            </>
                          )}
                          {store.accountInfo.storeStatus === 'active' ? (
                            <button
                              onClick={() => toggleStoreStatus(store.storeId, 'temp-closed')}
                              className="text-orange-600 hover:text-orange-900"
                            >
                              歇业
                            </button>
                          ) : (
                            <button
                              onClick={() => toggleStoreStatus(store.storeId, 'active')}
                              className="text-green-600 hover:text-green-900"
                            >
                              恢复
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                
                {filteredStores.length === 0 && (
                  <div className="text-center py-12">
                    <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <h3 className="mt-2 text-sm font-medium text-gray-900">暂无店铺</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm ? '没有找到匹配的店铺' : '还没有店铺申请'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* 店铺详情模态框 */}
        {showStoreModal && selectedStore && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    店铺详情 - {selectedStore.name}
                  </h3>
                  <button
                    onClick={() => setShowStoreModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">店铺名称</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedStore.name}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">业务类型</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedStore.businessType}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">电话</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedStore.phone}</p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">邮箱</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedStore.email}</p>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">地址</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedStore.currentAddress.addressLine1}, {selectedStore.currentAddress.city}, {selectedStore.currentAddress.province}, {selectedStore.currentAddress.country}
                    </p>
                  </div>
                  
                  {selectedStore.description && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">描述</label>
                      <p className="mt-1 text-sm text-gray-900">{selectedStore.description}</p>
                    </div>
                  )}
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">提供的服务</label>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {selectedStore.services.grooming && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          美容
                        </span>
                      )}
                      {selectedStore.services.boarding && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          寄养
                        </span>
                      )}
                      {selectedStore.services.veterinary && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          兽医
                        </span>
                      )}
                      {selectedStore.services.training && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          训练
                        </span>
                      )}
                      {selectedStore.services.retail && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          零售
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {selectedStore.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.PENDING && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">审核备注</label>
                      <textarea
                        value={approvalReason}
                        onChange={(e) => setApprovalReason(e.target.value)}
                        placeholder="请输入审核备注（可选）"
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                        rows={3}
                      />
                    </div>
                  )}
                </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setShowStoreModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    关闭
                  </button>
                  {selectedStore.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.PENDING && (
                    <>
                      <button
                        onClick={() => updateStoreStatus(selectedStore.storeId, StoreVerifiedStatus.REJECTED, approvalReason)}
                        className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                      >
                        拒绝
                      </button>
                      <button
                        onClick={() => updateStoreStatus(selectedStore.storeId, StoreVerifiedStatus.APPROVED, approvalReason)}
                        className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                      >
                        批准
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 编辑店铺模态框 */}
        {showEditModal && selectedStore && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    编辑店铺信息 - {selectedStore.name}
                  </h3>
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">店铺名称</label>
                      <input
                        type="text"
                        value={editFormData.name}
                        onChange={(e) => setEditFormData({...editFormData, name: e.target.value})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">业务类型</label>
                      <select
                        value={editFormData.businessType}
                        onChange={(e) => setEditFormData({...editFormData, businessType: e.target.value})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      >
                        <option value="">选择业务类型</option>
                        <option value="PET_GROOMING">宠物美容</option>
                        <option value="PET_BOARDING">宠物寄养</option>
                        <option value="VETERINARY">兽医服务</option>
                        <option value="PET_TRAINING">宠物训练</option>
                        <option value="PET_RETAIL">宠物零售</option>
                        <option value="OTHER">其他</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">电话</label>
                      <input
                        type="text"
                        value={editFormData.phone}
                        onChange={(e) => setEditFormData({...editFormData, phone: e.target.value})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">邮箱</label>
                      <input
                        type="email"
                        value={editFormData.email}
                        onChange={(e) => setEditFormData({...editFormData, email: e.target.value})}
                        className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">地址</label>
                    <input
                      type="text"
                      value={editFormData.addressLine1}
                      onChange={(e) => setEditFormData({...editFormData, addressLine1: e.target.value})}
                      placeholder="详细地址"
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                    />
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      <input
                        type="text"
                        value={editFormData.city}
                        onChange={(e) => setEditFormData({...editFormData, city: e.target.value})}
                        placeholder="城市"
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      />
                      <input
                        type="text"
                        value={editFormData.province}
                        onChange={(e) => setEditFormData({...editFormData, province: e.target.value})}
                        placeholder="省份"
                        className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700">描述</label>
                    <textarea
                      value={editFormData.description}
                      onChange={(e) => setEditFormData({...editFormData, description: e.target.value})}
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setShowEditModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    取消
                  </button>
                  <button
                    onClick={updateStoreInfo}
                    className="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    保存更改
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 批量操作模态框 */}
        {showBatchModal && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    批量操作 ({selectedStores.length} 个店铺)
                  </h3>
                  <button
                    onClick={() => setShowBatchModal(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">选择操作</label>
                    <select
                      value={batchAction}
                      onChange={(e) => setBatchAction(e.target.value as 'approve' | 'reject' | 'suspend' | 'activate')}
                      className="block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="approve">批量批准</option>
                      <option value="reject">批量拒绝</option>
                      <option value="suspend">批量歇业</option>
                      <option value="activate">批量恢复营业</option>
                    </select>
                  </div>
                  
                  <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                    <p className="text-sm text-yellow-800">
                      此操作将影响 {selectedStores.length} 个店铺，请确认操作。
                    </p>
                  </div>
                </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setShowBatchModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    取消
                  </button>
                  <button
                    onClick={batchUpdateStores}
                    className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    确认执行
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 创建者信息模态框 */}
        {storeOwnerInfo && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">
                    创建者信息
                  </h3>
                  <button
                    onClick={() => setStoreOwnerInfo(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    {storeOwnerInfo.photoURL ? (
                      <img 
                        src={storeOwnerInfo.photoURL} 
                        alt={storeOwnerInfo.displayName}
                        className="w-12 h-12 rounded-full"
                      />
                    ) : (
                      <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <span className="text-purple-600 font-semibold">
                          {storeOwnerInfo.displayName?.charAt(0) || 'U'}
                        </span>
                      </div>
                    )}
                    <div>
                      <p className="text-lg font-medium text-gray-900">{storeOwnerInfo.displayName}</p>
                      <p className="text-sm text-gray-500">{storeOwnerInfo.userType}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">邮箱</label>
                      <p className="mt-1 text-sm text-gray-900">{storeOwnerInfo.email}</p>
                    </div>
                    {storeOwnerInfo.phoneNumber && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700">电话</label>
                        <p className="mt-1 text-sm text-gray-900">{storeOwnerInfo.phoneNumber}</p>
                      </div>
                    )}
                    <div>
                      <label className="block text-sm font-medium text-gray-700">注册时间</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {storeOwnerInfo.createdAt.toLocaleDateString('zh-CN')}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 flex justify-end">
                  <button
                    onClick={() => setStoreOwnerInfo(null)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminRoute>
  );
} 