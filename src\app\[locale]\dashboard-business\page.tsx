'use client';

import { useAuth, BusinessDashboardRoute } from '../../lib/firebase/context/AuthContext';
import { useTranslations } from 'next-intl';
import { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import Link from 'next/link';
import { UserType, StoreRole } from '../../lib/models/types';
import { 
  MdStorefront, 
  MdAdd, 
  MdEdit, 
  MdSettings, 
  MdVisibility,
  MdPendingActions,
  MdCheckCircle,
  MdCancel,
  MdRefresh,
  MdSearch,
  MdPerson,
  MdKeyboardArrowDown
} from 'react-icons/md';
import { FirestoreService } from '../../lib/firebase/services';
import StoreService, { StoreListItem } from '../../lib/services/store_services';
import NotificationCenter from '../components/notifications/NotificationCenter';

export default function DashboardPage() {
  return (
    <BusinessDashboardRoute>
      <DashboardContent />
    </BusinessDashboardRoute>
  );
}

// 扩展 StoreListItem 以包含员工角色信息
interface StoreData extends StoreListItem {
  userRole?: StoreRole;
  staffStartTime?: Date;
  staffEndTime?: Date;
  staffNote?: string;
}

interface StoreStats {
  totalStores: number;
  activeStores: number;
  pendingStores: number;
  rejectedStores: number;
}

function DashboardContent() {
  const { user, userData, signOut } = useAuth();
  const t = useTranslations('common');
  const tDashboard = useTranslations('dashboard');
  const tStore = useTranslations('storeManagement');

  // 权限检查函数
  const canCreateStore = () => {
    return canCreateStorePermission;
  };

  const canEditStore = () => {
    return userData?.userType === UserType.PETSTORE_OWNER || userData?.userType === UserType.PETSTORE_ADMIN;
  };

  const canManageStaff = () => {
    return userData?.userType === UserType.PETSTORE_OWNER || userData?.userType === UserType.PETSTORE_ADMIN;
  };

  const isStoreStaff = () => {
    return userData?.userType === UserType.PETSTORE_STAFF;
  };

  // 获取角色显示名称
  const getRoleDisplayName = () => {
    switch (userData?.userType) {
      case UserType.PETSTORE_OWNER:
        return tDashboard('roles.PETSTORE_OWNER');
      case UserType.PETSTORE_ADMIN:
        return tDashboard('roles.PETSTORE_ADMIN');
      case UserType.PETSTORE_STAFF:
        return tDashboard('roles.PETSTORE_STAFF');
      default:
        return 'user';
    }
  };

  const [stores, setStores] = useState<StoreData[]>([]);
  const [storeStats, setStoreStats] = useState<StoreStats>({
    totalStores: 0,
    activeStores: 0,
    pendingStores: 0,
    rejectedStores: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'pending' | 'rejected'>('all');
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, right: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [canCreateStorePermission, setCanCreateStorePermission] = useState(false);

  // 获取用户店铺数据
  useEffect(() => {
    const fetchStores = async () => {
      if (!userData?.uid || !userData?.sid) return;

      try {
        setLoading(true);
        
        // 检查创建店铺权限
        const canCreate = await StoreService.canCreateStore(userData.sid);
        setCanCreateStorePermission(canCreate);
        
        // 使用 StoreService 获取用户店铺
        const storesResult = await StoreService.getUserStores(userData.uid);
        // console.log('storesResult:  ', storesResult);
        
        if (storesResult.success && storesResult.data) {
          console.log('Store service result:', storesResult.data);
          
          // 转换为 StoreData 类型（添加员工角色信息）
          const storeList: StoreData[] = [];
          
          if (userData.userType === UserType.PETSTORE_OWNER) {
            // 店铺所有者：直接使用返回的数据
            storeList.push(...storesResult.data);
          } else if (userData.userType === UserType.PETSTORE_ADMIN || userData.userType === UserType.PETSTORE_STAFF) {
            // 店铺管理员或员工：需要获取员工角色信息
            for (const store of storesResult.data) {
              const staffInfoResult = await FirestoreService.getMany('store-staff-info', {
                where: [
                  { field: 'uid', operator: '==', value: userData.uid },
                  { field: 'storeId', operator: '==', value: store.sid },
                  { field: 'active', operator: '==', value: true }
                ]
              });

              if (staffInfoResult.success && staffInfoResult.data && staffInfoResult.data.length > 0) {
                 const staffInfo = staffInfoResult.data[0];
                 storeList.push({
                   ...store,
                   userRole: staffInfo.role as StoreRole,
                   staffStartTime: staffInfo.startTime as Date,
                   staffEndTime: staffInfo.endTime as Date,
                   staffNote: staffInfo.note as string
                 });
               } else {
                 // 如果没有找到员工信息，使用默认值
                 storeList.push({
                   ...store,
                   userRole: userData.userType === UserType.PETSTORE_ADMIN ? StoreRole.STORE_ADMIN : StoreRole.STORE_STAFF
                 });
               }
            }
          }
          
          console.log('Final store list:', storeList);
          setStores(storeList);
          
          // 使用 StoreService 获取统计信息
          const statsResult = await StoreService.getStoreStats(userData.uid);
          if (statsResult.success && statsResult.data) {
            setStoreStats(statsResult.data);
          } else {
            console.error('Failed to fetch store stats:', statsResult.error);
            setStoreStats({
              totalStores: 0,
              activeStores: 0,
              pendingStores: 0,
              rejectedStores: 0
            });
          }
        } else {
          console.error('Failed to fetch stores:', storesResult.error);
          setStores([]);
          setStoreStats({
            totalStores: 0,
            activeStores: 0,
            pendingStores: 0,
            rejectedStores: 0
          });
        }
      } catch (error) {
        console.error('Error fetching stores:', error);
        setStores([]);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
  }, [userData, user]);

  // 过滤店铺
  const filteredStores = stores.filter(store => {
    const storeName = (store.storeName || store.name || '') as string;
    const businessType = (store.businessType || '') as string;
    const verifiedStatus = store.accountInfo?.storeVerifiedStatus || 'pending';
    
    const matchesSearch = storeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         businessType.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (statusFilter === 'all') return matchesSearch;
    return matchesSearch && verifiedStatus === statusFilter;
  });

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'rejected': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <MdCheckCircle className="h-4 w-4" />;
      case 'pending': return <MdPendingActions className="h-4 w-4" />;
      case 'rejected': return <MdCancel className="h-4 w-4" />;
      default: return null;
    }
  };

  return (
    console.log('storeList:  ', stores),
    <div className="min-h-screen bg-gradient-to-br from-[#FDECCE] via-[#F2D3A4] to-[#F2D3A4] p-4 sm:p-8">
      <div className="max-w-7xl mx-auto">
        {/* 头部 */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-8 border border-white/20 mb-6 sm:mb-8 relative z-50">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
            <div>
              <h1 className="text-2xl sm:text-4xl font-bold text-gray-900 mb-2 leading-tight">
                {tDashboard('welcome')}{userData?.displayName || user?.displayName || tDashboard('user')}
              </h1>
              <p className="text-[#C6C6C6] text-base sm:text-lg">
                {tDashboard('adminPanel')}
              </p>
            </div>
            <div className="flex items-center space-x-3 sm:space-x-4">
              <div className="hidden sm:block">
                <NotificationCenter />
              </div>
              
              {/* 用户菜单 */}
              <div className="relative">
                <button
                  ref={buttonRef}
                  onClick={() => {
                    if (!showUserMenu && buttonRef.current) {
                      const rect = buttonRef.current.getBoundingClientRect();
                      setMenuPosition({
                        top: rect.bottom + 8,
                        right: window.innerWidth - rect.right
                      });
                    }
                    setShowUserMenu(!showUserMenu);
                  }}
                  className="flex items-center space-x-2 sm:space-x-3 p-2 rounded-xl hover:bg-gray-100 transition-colors"
                >
                  {userData?.photoURL ? (
                    <img 
                      src={userData.photoURL} 
                      alt={userData.displayName || 'User'}
                      className="w-8 h-8 sm:w-10 sm:h-10 rounded-full"
                    />
                  ) : (
                    <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-br from-[#A126FF] to-[#8a20d8] flex items-center justify-center text-white font-semibold text-sm sm:text-base">
                      {userData?.firstName?.[0] || userData?.displayName?.[0] || 'U'}
                    </div>
                  )}
                  <div className="text-left hidden sm:block">
                    <p className="text-sm font-medium text-gray-900">
                      {userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim() || '用户'}
                    </p>
                    <p className="text-xs text-gray-500">
                      {getRoleDisplayName()}
                    </p>
                  </div>
                  <MdKeyboardArrowDown className="w-4 h-4 text-gray-400" />
                </button>
              </div>

              {/* Portal渲染的下拉菜单 */}
              {showUserMenu && typeof window !== 'undefined' && createPortal(
                <>
                  {/* 背景遮罩 */}
                  <div 
                    className="fixed inset-0 z-[999999]"
                    onClick={() => setShowUserMenu(false)}
                  />
                  
                  {/* 菜单内容 */}
                  <div 
                    className="fixed w-48 sm:w-56 bg-white rounded-xl shadow-xl border border-gray-200 z-[999999] py-2 drop-shadow-2xl"
                    style={{
                      top: `${menuPosition.top}px`,
                      right: `${menuPosition.right}px`
                    }}
                  >
                    <div className="px-4 py-3 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">
                        {userData?.displayName || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim() || '用户'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {user?.email}
                      </p>
                    </div>
                    
                    <div className="py-1">
                      <Link
                        href="/auth/profile-edit"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <MdPerson className="w-4 h-4 mr-3" />
                        {t('header.editProfile')}
                      </Link>
                      <Link
                        href="/settings"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <MdSettings className="w-4 h-4 mr-3" />
                        {t('header.settings')}
                      </Link>
                    </div>
                    
                    <div className="border-t border-gray-100 py-1">
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          handleSignOut();
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                      >
                        <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                        </svg>
                        {t('logout')}
                      </button>
                    </div>
                  </div>
                </>,
                document.body
              )}
            </div>
          </div>
        </div>

        {/* 店铺管理区域 */}
        <div className="mb-6 sm:mb-8">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl p-4 sm:p-8 border border-white/20">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-4 sm:space-y-0">
              <div className="flex items-center">
                <MdStorefront className="h-6 w-6 sm:h-8 sm:w-8 text-[#A126FF] mr-2 sm:mr-3" />
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900">{tDashboard('storeManagement')}</h2>
                  <p className="text-sm sm:text-base text-gray-600">{tStore('subtitle')}</p>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                <button 
                  onClick={() => window.location.reload()}
                  className="px-3 sm:px-4 py-2 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors flex items-center justify-center space-x-2"
                >
                  <MdRefresh className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="text-sm sm:text-base">{tStore('actions.refresh')}</span>
                </button>
{canCreateStore() && (
                  <Link
                    href="/store/create"
                    className="px-4 sm:px-6 py-2 sm:py-3 bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white font-semibold rounded-xl hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 flex items-center justify-center space-x-2"
                  >
                    <MdAdd className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="text-sm sm:text-base">{tStore('createNewStore')}</span>
                  </Link>
                )}
              </div>
            </div>

            {/* 店铺统计卡片 */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6 mb-6 sm:mb-8">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl sm:rounded-2xl p-3 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-xs sm:text-sm">{tStore('stats.totalStores')}</p>
                    <p className="text-xl sm:text-3xl font-bold">{storeStats.totalStores}</p>
                  </div>
                  <MdStorefront className="h-6 w-6 sm:h-10 sm:w-10 text-blue-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl sm:rounded-2xl p-3 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-xs sm:text-sm">{tStore('stats.activeStores')}</p>
                    <p className="text-xl sm:text-3xl font-bold">{storeStats.activeStores}</p>
                  </div>
                  <MdCheckCircle className="h-6 w-6 sm:h-10 sm:w-10 text-green-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-xl sm:rounded-2xl p-3 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-xs sm:text-sm">{tStore('stats.pendingStores')}</p>
                    <p className="text-xl sm:text-3xl font-bold">{storeStats.pendingStores}</p>
                  </div>
                  <MdPendingActions className="h-6 w-6 sm:h-10 sm:w-10 text-yellow-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl sm:rounded-2xl p-3 sm:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-red-100 text-xs sm:text-sm">{tStore('stats.rejectedStores')}</p>
                    <p className="text-xl sm:text-3xl font-bold">{storeStats.rejectedStores}</p>
                  </div>
                  <MdCancel className="h-6 w-6 sm:h-10 sm:w-10 text-red-200" />
                </div>
              </div>
            </div>

            {/* 搜索和筛选 */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 mb-4 sm:mb-6">
              <div className="relative flex-1 max-w-md">
                <MdSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4 sm:h-5 sm:w-5" />
                <input
                  type="text"
                  placeholder={tStore('filters.searchPlaceholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-9 sm:pl-10 pr-4 py-2 sm:py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#A126FF] focus:border-transparent text-sm sm:text-base"
                />
              </div>
              
              <div className="flex flex-wrap gap-2 sm:space-x-2">
                {['all', 'active', 'pending', 'rejected'].map((status) => (
                  <button
                    key={status}
                    onClick={() => setStatusFilter(status as 'all' | 'active' | 'pending' | 'rejected')}
                    className={`px-3 sm:px-4 py-2 rounded-xl font-medium transition-colors text-sm sm:text-base ${
                      statusFilter === status
                        ? 'bg-[#A126FF] text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {tStore(`filters.${status}`)}
                  </button>
                ))}
              </div>
            </div>

            {/* 店铺列表 */}
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#A126FF]"></div>
              </div>
            ) : filteredStores.length === 0 ? (
              <div className="text-center py-12">
                <MdStorefront className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">
                  {isStoreStaff() ? 'no stores assigned' : tStore('noStoresMessage')}
                </h3>
                <p className="text-gray-500 mb-6">
                  {isStoreStaff() 
                    ? 'Please contact the admin to assign you store permissions' 
                    : tStore('createFirstStoreMessage')
                  }
                </p>
                {canCreateStore() && (
                  <Link
                    href="/store/create"
                    className="px-6 py-3 bg-gradient-to-r from-[#A126FF] to-[#C084FC] text-white font-semibold rounded-xl hover:shadow-lg transform hover:scale-[1.02] transition-all duration-200 inline-flex items-center space-x-2"
                  >
                    <MdAdd className="h-5 w-5" />
                    <span>{tStore('createNewStore')}</span>
                  </Link>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {filteredStores.map((store) => (
                  <div key={store.storeId} className="bg-white rounded-xl sm:rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <div className="p-4 sm:p-6">
                      <div className="flex items-start justify-between mb-3 sm:mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-1 line-clamp-1">
                            {(store.storeName || store.name || '') as string}
                          </h3>
                          <p className="text-gray-600 text-sm mb-2 line-clamp-1">
                            {(store.businessType || '') as string}
                          </p>
                          <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full text-xs font-medium ${getStatusColor((store.accountInfo?.storeVerifiedStatus || 'pending') as string)}`}>
                            {getStatusIcon((store.accountInfo?.storeVerifiedStatus || 'pending') as string)}
                            <span className="ml-1">
                              {tDashboard((store.accountInfo?.storeVerifiedStatus || 'pending') as string)}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2 mb-3 sm:mb-4">
                        <div className="flex items-center text-xs sm:text-sm text-gray-600">
                          <span className="font-medium w-12 sm:w-16 flex-shrink-0">{tStore('storeCard.address')}:</span>
                          <span className="truncate">{store.currentAddress?.city}, {store.currentAddress?.province}</span>
                        </div>
                        <div className="flex items-center text-xs sm:text-sm text-gray-600">
                          <span className="font-medium w-12 sm:w-16 flex-shrink-0">{tStore('storeCard.phone')}:</span>
                          <span>{store.phone}</span>
                        </div>
                        {store.website && (
                          <div className="flex items-center text-sm text-gray-600">
                            <span className="font-medium w-16">{tStore('storeCard.website')}:</span>
                            <a 
                              href={store.website} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 truncate"
                            >
                              {store.website.replace(/^https?:\/\//, '')}
                            </a>
                          </div>
                        )}
                        {store.userRole && (
                          <div className="flex items-center text-xs sm:text-sm text-gray-600">
                            <span className="font-medium w-12 sm:w-16 flex-shrink-0">我的角色:</span>
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md">
                              {store.userRole === StoreRole.STORE_OWNER ? '店铺所有者' :
                               store.userRole === StoreRole.STORE_ADMIN ? '店铺管理员' :
                               store.userRole === StoreRole.STORE_STAFF ? '店铺员工' : store.userRole}
                            </span>
                          </div>
                        )}
                        {store.staffStartTime && (
                          <div className="flex items-center text-xs sm:text-sm text-gray-600">
                            <span className="font-medium w-12 sm:w-16 flex-shrink-0">入职时间:</span>
                            <span>{new Date(store.staffStartTime).toLocaleDateString()}</span>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3 sm:mb-4">
                        {Object.entries(store.services || {}).map(([service, enabled]) => (
                          enabled && (
                            <span key={service} className="px-2 py-1 bg-[#F2D3A4] text-[#A126FF] text-xs rounded-md">
                              {tDashboard(service)}
                            </span>
                          )
                        ))}
                      </div>

                      <div className="flex flex-col sm:flex-row gap-2 sm:space-x-2 sm:gap-0">
                        <Link
                          href={`/store/${store.storeId}`}
                          className="flex-1 px-3 py-2 bg-[#F2D3A4] text-[#A126FF] text-sm font-medium rounded-lg hover:bg-[#FDECCE] transition-colors text-center flex items-center justify-center space-x-1"
                        >
                          <MdVisibility className="h-4 w-4" />
                          <span>{isStoreStaff() ? '查看服务' : tStore('storeCard.viewDetails')}</span>
                        </Link>
                        {canEditStore() && (
                          <Link
                            href={`/store/${store.storeId}/edit`}
                            className="px-3 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center"
                          >
                            <MdEdit className="h-4 w-4" />
                          </Link>
                        )}
                        {canManageStaff() && (
                          <Link
                            href={`/store/${store.storeId}/staff`}
                            className="px-3 py-2 bg-blue-100 text-blue-700 text-sm font-medium rounded-lg hover:bg-blue-200 transition-colors flex items-center justify-center"
                            title="管理员工"
                          >
                            <MdPerson className="h-4 w-4" />
                          </Link>
                        )}
                        <button className="px-3 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors">
                          <MdSettings className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}