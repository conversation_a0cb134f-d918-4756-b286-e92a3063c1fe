import React from "react";
import "./Div.css";

export const Div = (): JSX.Element => {
  return (
    <div className="div">
      <div className="stay-connected-with-wrapper">
        <div className="stay-connected-with">
          Stay connected with pet
          <br />
          lovers
        </div>
      </div>

      <p className="create-a-profile-for-2">
        Create a profile for your pet, share adorable moments, and connect
        <br />
        with a vibrant community of fellow pet enthusiasts.
      </p>

      <div className="list-3">
        <div className="item-3">
          <div className="text-wrapper-5">check_circle</div>

          <p className="text-wrapper-6">
            Create and customize your pet&#39;s profile
          </p>
        </div>

        <div className="item-3">
          <div className="text-wrapper-5">check_circle</div>

          <p className="text-wrapper-6">
            Post photos, videos, or quick updates
          </p>
        </div>

        <div className="item-3">
          <div className="text-wrapper-5">check_circle</div>

          <p className="text-wrapper-6">
            Follow other pet profiles and interact
          </p>
        </div>

        <div className="item-3">
          <div className="text-wrapper-5">check_circle</div>

          <p className="text-wrapper-6">
            Join groups based on breed or interests
          </p>
        </div>
      </div>

      <div className="overlap-3">
        <div className="ellipse-2" />

        <div className="element-pacific-blue">
          <div className="overlap-4">
            <div className="element">
              <div className="mockup-wrapper">
                <div className="mockup-2" />
              </div>
            </div>

            <img
              className="reflection"
              alt="Reflection"
              src="https://c.animaapp.com/9exExmER/img/reflection.png"
            />

            <div className="overlap-group-wrapper">
              <div className="overlap-5">
                <div className="shadow">
                  <img
                    className="rectangle-2"
                    alt="Rectangle"
                    src="https://c.animaapp.com/9exExmER/img/rectangle.png"
                  />
                </div>

                <img
                  className="shadow-2"
                  alt="Shadow"
                  src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
                />

                <img
                  className="iphone-pro"
                  alt="Iphone pro"
                  src="https://c.animaapp.com/9exExmER/img/iphone-12-pro-1.png"
                />

                <div className="mask-group-wrapper">
                  <img
                    className="mask-group-2"
                    alt="Mask group"
                    src="https://c.animaapp.com/9exExmER/img/mask-group-2.png"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

