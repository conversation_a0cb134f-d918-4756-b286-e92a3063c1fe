/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/app/[locale]/components/ui/Button';
import { Input } from '@/app/[locale]/components/ui/Input';
import { useAuth, AuthenticatedRoute } from '@/app/lib/firebase/context/AuthContext';
import customerService from '@/app/lib/services/customer_service';
import appointmentService from '@/app/lib/services/appointment_service';
import staffService from '@/app/lib/services/staff_services';
import storeService from '@/app/lib/services/store_services';
import { Customer } from '@/app/lib/models/customer';
import { AppointmentStatus, StoreServiceStatus } from '@/app/lib/models/types';
import { useTranslations } from 'next-intl';
import { StoreHeader } from '@/app/[locale]/components/ui/StoreHeader';
import { 
  FiArrowLeft, 
  FiUser, 
  FiMail, 
  FiPhone, 
  FiCalendar,
  FiClock,

  FiHeart,
  FiPlus,

  FiEye,
  FiCheck,

  FiStar,
  FiClock as FiTime
} from 'react-icons/fi';

interface AppointmentData {
  serviceId: string;
  staffId: string;
  date: string;
  startTime: string;
  duration: number;
  notes?: string;
}

interface CustomerDetailStats {
  totalAppointments: number;
  completedAppointments: number;
  upcomingAppointments: number;
  totalPets: number;
}

export default function CustomerDetailPage() {
  return (
    <AuthenticatedRoute>
      <CustomerDetailContent />
    </AuthenticatedRoute>
  );
}

function CustomerDetailContent() {
  const router = useRouter();
  const params = useParams();
  const t = useTranslations("CustomerDetail");
  const { user } = useAuth();
  const storeId = params.id as string;
  const customerId = params.customerId as string;
  
  // State
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<CustomerDetailStats>({
    totalAppointments: 0,
    completedAppointments: 0,
    upcomingAppointments: 0,
    totalPets: 0
  });
  const [appointments, setAppointments] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [staff, setStaff] = useState<any[]>([]);
  
  // Appointment creation state
  const [showCreateAppointment, setShowCreateAppointment] = useState(false);
  const [appointmentData, setAppointmentData] = useState<AppointmentData>({
    serviceId: '',
    staffId: '',
    date: '',
    startTime: '',
    duration: 60,
    notes: ''
  });
  const [createLoading, setCreateLoading] = useState(false);

  // Load customer data
  const loadCustomerData = async () => {
    try {
      setLoading(true);
      
      // Get all customers for the store
      const customersResult = await customerService.getCustomersByStore(storeId);
      
      if (customersResult.success && customersResult.data) {
        const foundCustomer = customersResult.data.find(c => 
          c.customerData.sid === customerId || c.customerData.uid === customerId
        );
        
        if (foundCustomer) {
          setCustomer(foundCustomer);
          
          // Calculate stats
          const totalPets = foundCustomer.pets?.length || 0;
          setStats(prev => ({ ...prev, totalPets }));
          
          // Load appointments for this customer
          await loadCustomerAppointments(foundCustomer.customerData.uid);
        } else {
          console.error('Customer not found');
          router.push(`/store/${storeId}/customers`);
        }
      }
    } catch (error) {
      console.error('Error loading customer data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load customer appointments
  const loadCustomerAppointments = async (customerUid: string) => {
    try {
      const appointmentsResult = await appointmentService.getCustomerAppointments(customerUid, {
        limit: 10
      });
      
      if (appointmentsResult.success && appointmentsResult.data) {
        setAppointments(appointmentsResult.data);
        
        // Calculate appointment stats
        const total = appointmentsResult.data.length;
        const completed = appointmentsResult.data.filter(
          apt => apt.status === AppointmentStatus.COMPLETED
        ).length;
        const upcoming = appointmentsResult.data.filter(
          apt => apt.status === AppointmentStatus.CONFIRMED || apt.status === AppointmentStatus.DRAFT
        ).length;
        
        setStats(prev => ({
          ...prev,
          totalAppointments: total,
          completedAppointments: completed,
          upcomingAppointments: upcoming
        }));
      }
    } catch (error) {
      console.error('Error loading appointments:', error);
    }
  };

  // Load services and staff for appointment creation
  const loadServicesAndStaff = async () => {
    try {
      // Load services
      const servicesResult = await storeService.getStoreServices(storeId);
      if (servicesResult.success && servicesResult.data) {
        // Filter only active services that allow online booking
        const activeServices = servicesResult.data.filter(service => 
          service.status === StoreServiceStatus.ACTIVE && 
          service.isOnlineBookingEnabled
        );
        setServices(activeServices);
      }
      
      // Load staff members
      const staffResult = await staffService.getStoreStaff(storeId);
      if (staffResult.success && staffResult.data) {
        setStaff(staffResult.data);
      }
    } catch (error) {
      console.error('Error loading services and staff:', error);
    }
  };

  // Create appointment
  const handleCreateAppointment = async () => {
    if (!user?.uid || !customer) {
      alert(t('pleaseLoginFirst'));
      return;
    }

    if (!appointmentData.serviceId || !appointmentData.staffId || !appointmentData.date || !appointmentData.startTime) {
      alert(t('fillAllRequiredFields'));
      return;
    }

    try {
      setCreateLoading(true);
      
      const result = await appointmentService.createAppointment(
        storeId,
        {
          customerId: customer.customerData.uid,
          staffId: appointmentData.staffId,
          serviceId: appointmentData.serviceId,
          date: appointmentData.date,
          startTime: appointmentData.startTime,
          duration: appointmentData.duration,
          notes: appointmentData.notes
        },
        user.uid
      );

      if (result.success) {
        setShowCreateAppointment(false);
        setAppointmentData({
          serviceId: '',
          staffId: '',
          date: '',
          startTime: '',
          duration: 60,
          notes: ''
        });
        
        // Reload appointments
        await loadCustomerAppointments(customer.customerData.uid);
        alert(t('appointmentCreatedSuccessfully'));
      } else {
        alert(result.error || t('appointmentCreationFailed'));
      }
    } catch (error) {
      console.error('Error creating appointment:', error);
      alert(t('appointmentCreationFailed'));
    } finally {
      setCreateLoading(false);
    }
  };

  // Get customer display name
  const getCustomerDisplayName = (customer: Customer): string => {
    if (customer.customerData.firstName && customer.customerData.lastName) {
      return `${customer.customerData.firstName} ${customer.customerData.lastName}`.trim();
    }
    return customer.customerData.firstName || customer.customerData.lastName || 'Unknown Customer';
  };

  // Get customer source display
  const getCustomerSourceDisplay = (customer: Customer): string => {
    if (customer.customerData.userType === 'ONENATA_USER') {
      return t('oneNataUser');
    } else if (customer.customerData.userType === 'PETSTORE_CUSTOMER_FROM_PORTAL') {
      return t('portalCreated');
    }
    return t('walkInCustomer');
  };

  // Get appointment status display
  const getAppointmentStatusDisplay = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.DRAFT:
        return { text: t('draft'), color: 'bg-gray-100 text-gray-800' };
      case AppointmentStatus.CONFIRMED:
        return { text: t('confirmed'), color: 'bg-blue-100 text-blue-800' };
      case AppointmentStatus.IN_PROGRESS:
        return { text: t('inProgress'), color: 'bg-yellow-100 text-yellow-800' };
      case AppointmentStatus.COMPLETED:
        return { text: t('completed'), color: 'bg-green-100 text-green-800' };
      case AppointmentStatus.CANCELLED:
        return { text: t('cancelled'), color: 'bg-red-100 text-red-800' };
      default:
        return { text: t('unknown'), color: 'bg-gray-100 text-gray-800' };
    }
  };

  // Load data on mount
  useEffect(() => {
    if (storeId && customerId) {
      loadCustomerData();
      loadServicesAndStaff();
    }
  }, [storeId, customerId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('customerDetail')} storeId={storeId} currentPage="customers" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-slate-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-slate-200 rounded-xl"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('customerDetail')} storeId={storeId} currentPage="customers" />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="text-slate-500 text-lg">{t('customerNotFound')}</div>
            <Button
              onClick={() => router.push(`/store/${storeId}/customers`)}
              className="mt-4 bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
            >
              <FiArrowLeft className="w-4 h-4 mr-2" />
              {t('backToCustomers')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('customerDetail')} storeId={storeId} currentPage="customers" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push(`/store/${storeId}/customers`)}
                className="bg-slate-100 hover:bg-slate-200 text-slate-700"
              >
                <FiArrowLeft className="w-4 h-4 mr-2" />
                {t('back')}
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                  <FiUser className="w-8 h-8 mr-3 text-violet-600" />
                  {getCustomerDisplayName(customer)}
                </h1>
                <p className="text-slate-600 font-medium mt-1">
                  {t('customerDetail')} • {getCustomerSourceDisplay(customer)}
                </p>
              </div>
            </div>
            <div className="flex gap-3">
              <Button
                onClick={() => router.push(`/store/${storeId}/appointments/create?customerId=${customer.customerData.sid}`)}
                className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 flex items-center"
              >
                <FiPlus className="w-5 h-5 mr-2" />
                {t('createAppointment')}
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FiCalendar className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('totalAppointments')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totalAppointments}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <FiCheck className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('completedAppointments')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.completedAppointments}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100">
                <FiClock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('upcomingAppointments')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.upcomingAppointments}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FiHeart className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-600">{t('totalPets')}</p>
                <p className="text-2xl font-bold text-slate-900">{stats.totalPets}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Information */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiUser className="w-5 h-5 mr-2 text-violet-600" />
              {t('customerInformation')}
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <FiMail className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-sm text-slate-500">{t('email')}</p>
                  <p className="text-slate-900">{customer.customerData.email || t('notSet')}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiPhone className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-sm text-slate-500">{t('phoneNumber')}</p>
                  <p className="text-slate-900">{customer.customerData.phoneNumber || t('notSet')}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <FiCalendar className="w-4 h-4 text-slate-400" />
                <div>
                  <p className="text-sm text-slate-500">{t('memberSince')}</p>
                  <p className="text-slate-900">
                    {customer.customerData.create_date ? 
                      new Date(customer.customerData.create_date).toLocaleDateString() : 
                      t('notSet')
                    }
                  </p>
                </div>
              </div>
              
              {customer.customerData.bio && (
                <div className="flex items-start space-x-3">
                  <FiStar className="w-4 h-4 text-slate-400 mt-1" />
                  <div>
                    <p className="text-sm text-slate-500">{t('notes')}</p>
                    <p className="text-slate-900">{customer.customerData.bio}</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Pets Information */}
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
              <FiHeart className="w-5 h-5 mr-2 text-violet-600" />
              {t('pets')} ({stats.totalPets})
            </h2>
            
            {customer.pets && customer.pets.length > 0 ? (
              <div className="space-y-3">
                {customer.pets.map((pet, index) => (
                  <div key={pet.sid || index} className="border border-slate-200 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-violet-100 rounded-full flex items-center justify-center">
                          <FiHeart className="w-4 h-4 text-violet-600" />
                        </div>
                        <div>
                          <p className="font-medium text-slate-900">{pet.name}</p>
                          <p className="text-sm text-slate-500">
                            {t('petTypes.' + (pet.type || 'other'))} • {t('petGenders.' + (pet.gender || 'unknown'))}
                          </p>
                        </div>
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        pet.isLive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {pet.isLive ? t('active') : t('inactive')}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="text-slate-500 text-lg">{t('noPets')}</div>
                <p className="text-slate-400 mt-2">{t('addPetForCustomer')}</p>
              </div>
            )}
          </div>
        </div>

        {/* Recent Appointments */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
          <h2 className="text-lg font-semibold text-slate-900 mb-4 flex items-center">
            <FiTime className="w-5 h-5 mr-2 text-violet-600" />
            {t('recentAppointments')}
          </h2>
          
          {appointments.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      {t('service')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      {t('date')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      {t('time')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      {t('status')}
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                      {t('actions')}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-slate-200">
                  {appointments.map((appointment, index) => {
                    const statusDisplay = getAppointmentStatusDisplay(appointment.status);
                    return (
                      <tr key={appointment.sid || index} className="hover:bg-slate-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-slate-900">
                            {appointment.serviceInfo?.serviceName || t('unknownService')}
                          </div>
                          <div className="text-sm text-slate-500">
                            {appointment.serviceInfo?.serviceCategory || ''}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                          {appointment.timeInfo?.date ? 
                            new Date(appointment.timeInfo.date).toLocaleDateString() : 
                            t('notSet')
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-slate-900">
                          {appointment.timeInfo?.startTime || t('notSet')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusDisplay.color}`}>
                            {statusDisplay.text}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Button
                            onClick={() => router.push(`/store/${storeId}/appointments/${appointment.sid}`)}
                            className="bg-slate-100 hover:bg-slate-200 text-slate-700 px-3 py-1 text-xs flex items-center"
                          >
                            <FiEye className="w-3 h-3 mr-1" />
                            {t('viewDetails')}
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-slate-500 text-lg">{t('noAppointments')}</div>
              <p className="text-slate-400 mt-2">{t('createFirstAppointment')}</p>
            </div>
          )}
        </div>

        {/* Create Appointment Modal */}
        {showCreateAppointment && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-2xl p-6 w-full max-w-md mx-4">
              <h2 className="text-xl font-bold mb-4 flex items-center">
                <FiPlus className="w-6 h-6 mr-2 text-violet-600" />
                {t('createAppointment')}
              </h2>
              
              <form onSubmit={(e) => { e.preventDefault(); handleCreateAppointment(); }}>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('service')} *
                    </label>
                    <select
                      value={appointmentData.serviceId}
                      onChange={(e) => setAppointmentData({...appointmentData, serviceId: e.target.value})}
                      className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                      required
                    >
                      <option value="">{t('selectService')}</option>
                      {services.map((service) => (
                        <option key={service.sid} value={service.sid}>
                          {service.serviceName}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('staff')} *
                    </label>
                    <select
                      value={appointmentData.staffId}
                      onChange={(e) => setAppointmentData({...appointmentData, staffId: e.target.value})}
                      className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                      required
                    >
                      <option value="">{t('selectStaff')}</option>
                      {staff.map((staffMember) => (
                        <option key={staffMember.sid} value={staffMember.sid}>
                          {staffMember.displayName || staffMember.firstName}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('date')} *
                    </label>
                    <Input
                      type="date"
                      value={appointmentData.date}
                      onChange={(e) => setAppointmentData({...appointmentData, date: e.target.value})}
                      min={new Date().toISOString().split('T')[0]}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('time')} *
                    </label>
                    <Input
                      type="time"
                      value={appointmentData.startTime}
                      onChange={(e) => setAppointmentData({...appointmentData, startTime: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('duration')} (minutes)
                    </label>
                    <select
                      value={appointmentData.duration}
                      onChange={(e) => setAppointmentData({...appointmentData, duration: parseInt(e.target.value)})}
                      className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                    >
                      <option value={30}>30 {t('minutes')}</option>
                      <option value={60}>60 {t('minutes')}</option>
                      <option value={90}>90 {t('minutes')}</option>
                      <option value={120}>120 {t('minutes')}</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-1">
                      {t('notes')}
                    </label>
                    <textarea
                      className="w-full px-3 py-2 border-2 border-slate-300 rounded-xl focus:ring-4 focus:ring-violet-500/20 focus:border-violet-500 transition-all duration-200"
                      rows={3}
                      placeholder={t('appointmentNotes')}
                      value={appointmentData.notes}
                      onChange={(e) => setAppointmentData({...appointmentData, notes: e.target.value})}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <Button
                    type="button"
                    onClick={() => {
                      setShowCreateAppointment(false);
                      setAppointmentData({
                        serviceId: '',
                        staffId: '',
                        date: '',
                        startTime: '',
                        duration: 60,
                        notes: ''
                      });
                    }}
                    className="bg-slate-100 hover:bg-slate-200 text-slate-700"
                    disabled={createLoading}
                  >
                    {t('cancel')}
                  </Button>
                  <Button
                    type="submit"
                    className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
                    disabled={createLoading}
                  >
                    {createLoading ? t('creating') : t('createAppointment')}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 