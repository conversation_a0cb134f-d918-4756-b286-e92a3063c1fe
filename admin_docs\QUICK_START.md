# OneNata Admin Portal - 快速开始指南

## 🚀 5分钟快速启动

### 1. 克隆项目
```bash
git clone <repository-url>
cd onenata_admin
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
创建 `.env.local` 文件：
```bash
# Firebase 配置 (使用你的 Firebase 项目信息)
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# 开发环境模拟器
NEXT_PUBLIC_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
NEXT_PUBLIC_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

### 4. 启动开发环境
```bash
# 启动 Firebase 模拟器
firebase emulators:start --import=./emulators-data --export-on-exit=./emulators-data

# 新终端窗口启动开发服务器
npm run dev
```

### 5. 访问应用
打开浏览器访问 [http://localhost:3000](http://localhost:3000)

---

## 📋 项目概览

### 核心功能
- ✅ **用户认证** - 登录/注册/邮箱验证
- ✅ **店铺管理** - 创建/编辑/审批店铺
- ✅ **预约系统** - 智能预约管理
- ✅ **客户管理** - 客户档案管理
- ✅ **员工管理** - 员工排班和服务
- ✅ **国际化** - 中英文支持

### 技术栈
- **前端**: Next.js 15 + React 19 + TypeScript
- **样式**: Tailwind CSS 4
- **后端**: Firebase (Auth + Firestore + Storage + Functions)
- **国际化**: next-intl

---

## 🛠️ 开发命令

```bash
# 开发
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查

# Firebase
npm run test:firebase # 测试 Firebase 配置
npm run test:auth    # 测试认证流程
npm run emulator     # 启动 Firebase 模拟器

# 调试
npm run debug        # 启动调试模式
```

---

## 📁 关键文件结构

```
src/app/
├── [locale]/                    # 国际化路由
│   ├── auth/                   # 认证页面
│   ├── dashboard/              # 仪表板
│   ├── store/                  # 店铺管理
│   └── components/             # 共享组件
├── lib/
│   ├── firebase/              # Firebase 配置
│   ├── models/                # 数据模型
│   ├── services/              # 业务服务
│   └── utils/                 # 工具函数
└── i18n/                      # 国际化配置

messages/                       # 翻译文件
├── en.json                    # 英文
└── zh-CN.json                # 中文
```

---

## 🔐 用户类型和权限

### 用户类型
```typescript
enum UserType {
  ONENATA_ADMIN = '101',           // OneNata 管理员
  PETSTORE_OWNER = '201',          // 宠物店店主
  PETSTORE_ADMIN = '202',          // 宠物店管理员
  PETSTORE_STAFF = '203',          // 宠物店员工
  PETSTORE_CUSTOMER_FROM_PORTAL = '204', // Portal 创建的客户
  PETSTORE_CUSTOMER_FROM_APP = '205'     // App 创建的客户
}
```

### 权限矩阵
| 功能 | 管理员 | 店主 | 管理员 | 员工 |
|------|--------|------|--------|------|
| 店铺审批 | ✅ | ❌ | ❌ | ❌ |
| 创建店铺 | ❌ | ✅ | ❌ | ❌ |
| 管理员工 | ❌ | ✅ | ✅ | ❌ |
| 管理预约 | ❌ | ✅ | ✅ | ✅ |
| 管理客户 | ❌ | ✅ | ✅ | ✅ |

---

## 🏢 核心业务流程

### 1. 店铺创建流程
```
用户注册 → 资料设置 → 创建店铺 → 管理员审批 → 店铺激活
```

### 2. 预约管理流程
```
选择客户 → 选择服务 → 选择员工 → 选择时间 → 确认预约
```

### 3. 客户管理流程
```
创建客户 → 设置资料 → 关联店铺 → 管理预约
```

---

## 🔧 常见开发任务

### 添加新页面
1. 在 `src/app/[locale]/` 创建新目录
2. 添加 `page.tsx` 文件
3. 在 `messages/` 添加翻译
4. 更新导航配置

### 添加新 API
1. 在 `src/app/api/` 创建新文件
2. 实现 API 逻辑
3. 添加错误处理
4. 更新类型定义

### 添加新组件
1. 在 `src/app/[locale]/components/` 创建组件
2. 添加 TypeScript 类型
3. 添加样式
4. 添加测试

---

## 🚨 常见问题

### Q: Firebase 连接失败？
A: 检查 `.env.local` 中的 Firebase 配置是否正确

### Q: 模拟器无法启动？
A: 确保已安装 Firebase CLI: `npm install -g firebase-tools`

### Q: 构建失败？
A: 清理缓存: `rm -rf .next && npm run build`

### Q: 类型错误？
A: 运行 `npm run lint` 检查代码规范

---

## 📚 相关文档

- **完整文档**: [DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md)
- **API 文档**: 查看 `DEVELOPER_DOCUMENTATION.md` 的 API 文档章节
- **数据库设计**: 查看 `DEVELOPER_DOCUMENTATION.md` 的数据库设计章节
- **部署指南**: 查看 `DEVELOPER_DOCUMENTATION.md` 的部署指南章节

---

## 🆘 获取帮助

- **GitHub Issues**: [项目 Issues 页面]
- **文档**: 查看 `DEVELOPER_DOCUMENTATION.md`
- **代码示例**: 查看 `src/app/` 目录中的现有实现

---

*快速开始指南 - v1.0.0* 