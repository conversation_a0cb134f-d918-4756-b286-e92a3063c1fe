html, body {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: 'Manrope', Helvetica, Arial, sans-serif;
  background: #fff;
}
*, *::before, *::after {
  box-sizing: inherit;
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
  vertical-align: middle;
}

button, .button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  font-size: 18px;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  background: linear-gradient(90deg, #a126ff 0%, #601799 100%);
  color: #fff;
  transition: background 0.2s;
}
button .material-icons, .button .material-icons {
  margin-right: 8px;
  font-size: 22px;
}

/*
h1, h2, h3, h4, h5, h6 {
  font-family: inherit;
  font-weight: 800;
  margin: 0 0 16px 0;
}

.section-title, .main-title {
  font-size: 60px;
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 16px;
}

.section-desc, .main-desc {
  font-size: 22px;
  color: #444;
  margin-bottom: 32px;
}
*/
