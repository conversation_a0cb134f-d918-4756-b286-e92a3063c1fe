/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import { Button } from '@/app/[locale]/components/ui/Button';
import { useAuth, AuthenticatedRoute } from '@/app/lib/firebase/context/AuthContext';
import { StoreHeader } from '@/app/[locale]/components/ui/StoreHeader';
import CalendarDatePicker from '@/app/[locale]/components/ui/CalendarDatePicker';
import { useTranslations } from 'next-intl';
import customerService from '@/app/lib/services/customer_service';
import appointmentService from '@/app/lib/services/appointment_service';
import staffService from '@/app/lib/services/staff_services';
import storeService from '@/app/lib/services/store_services';
import { Customer, Pet } from '@/app/lib/models/customer';
import { ServiceCategory, ServiceBreed, StoreServiceStatus, TimeSlotStatus } from '@/app/lib/models/types';
import { StoreServiceData } from '@/app/lib/services/store_services';
import { StaffMember } from '@/app/lib/services/staff_services';
import { 
  FiArrowLeft, 
  FiCalendar,
  FiCheck,
  FiUser,
  FiHeart,
  FiSettings,
  FiUsers,
  FiChevronRight,
  FiChevronLeft,
  FiExternalLink,
  FiAlertCircle
} from 'react-icons/fi';

// Interfaces
interface CreateAppointmentData {
  customerId: string;
  petId?: string;
  storeServiceId: string;
  staffId: string;
  date: string;
  startTime: string;
  duration: number;
  notes?: string;
}

interface AvailableStoreService {
  storeService: StoreServiceData;
  availableStaff: StaffMember[];
}

interface TimeSlot {
  startTime: string;
  endTime: string;
  available: boolean;
  status: string; // 'available' | 'booked' | 'blocked' | 'holiday'
}

// Step enum
enum CreateStep {
  CUSTOMER_PET = 1,
  SERVICE_CATEGORY = 2,
  STORE_SERVICE = 3,
  STAFF = 4,
  DATE = 5,
  TIME = 6,
  CONFIRM = 7
}

export default function CreateAppointmentPage() {
  return (
    <AuthenticatedRoute>
      <CreateAppointmentContent />
    </AuthenticatedRoute>
  );
}

function CreateAppointmentContent() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const { userData } = useAuth();
  const t = useTranslations('createAppointment');
  const storeId = params.id as string;
  const preSelectedCustomerId = searchParams.get('customerId');
  const preSelectedDate = searchParams.get('date');

  // State
  const [currentStep, setCurrentStep] = useState<CreateStep>(CreateStep.CUSTOMER_PET);
  const [loading, setLoading] = useState(false);
  const [stepLoading, setStepLoading] = useState(false);

  // Data states
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [allStoreServices, setAllStoreServices] = useState<StoreServiceData[]>([]);
  const [availableCategories, setAvailableCategories] = useState<ServiceCategory[]>([]);
  const [categoryServices, setCategoryServices] = useState<AvailableStoreService[]>([]);
  const [availableStaff, setAvailableStaff] = useState<StaffMember[]>([]);
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);

  // Selection states
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [selectedPet, setSelectedPet] = useState<Pet | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<ServiceCategory | null>(null);
  const [selectedStoreService, setSelectedStoreService] = useState<StoreServiceData | null>(null);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');

  // Form data
  const [appointmentData, setAppointmentData] = useState<CreateAppointmentData>({
    customerId: preSelectedCustomerId || '',
    petId: undefined,
    storeServiceId: '',
    staffId: '',
    date: '',
    startTime: '',
    duration: 60,
    notes: ''
  });

  console.log('CreateAppointmentContent', storeId);

  // Load initial data
  useEffect(() => {
    loadCustomers();
    loadStoreServices();
  }, [storeId]);

  // Handle pre-selected customer
  useEffect(() => {
    if (preSelectedCustomerId && customers.length > 0) {
      const customer = customers.find(c => 
        c.customerData.uid === preSelectedCustomerId
      );
      if (customer) {
        setSelectedCustomer(customer);
        setAppointmentData(prev => ({ 
          ...prev, 
          customerId: customer.customerData.uid  || ''
        }));
      }
    }
  }, [preSelectedCustomerId, customers]);

  // Handle pre-selected date
  useEffect(() => {
    if (preSelectedDate) {
      setSelectedDate(preSelectedDate);
      setAppointmentData(prev => ({ 
        ...prev, 
        date: preSelectedDate
      }));
    }
  }, [preSelectedDate]);

  const loadCustomers = async () => {
    try {
      setLoading(true);
      console.log('loadCustomers', storeId);
      const result = await customerService.getCustomersByStore(storeId);
      if (result.success && result.data) {
        setCustomers(result.data);
      }
    } catch (error) {
      console.error('Error loading customers:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadStoreServices = async () => {
    try {
      setLoading(true);
      const result = await storeService.getStoreServices(storeId);
      console.log('loadStoreServices', result);
      if (result.success && result.data) {
        // 只显示活跃且支持在线预约的服务
        const activeServices = result.data.filter(service => 
          service.status === StoreServiceStatus.ACTIVE && 
          service.isOnlineBookingEnabled &&
          service.staffIds && 
          service.staffIds.length > 0
        );
        
        setAllStoreServices(activeServices);
        
        // 提取可用的服务类别
        const categories = [...new Set(activeServices.map(service => service.serviceCategory))];
        setAvailableCategories(categories);
      }
    } catch (error) {
      console.error('Error loading store services:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadServicesForCategory = async (category: ServiceCategory) => {
    try {
      setStepLoading(true);
      
      // 获取该类别的所有服务
      const categoryStoreServices = allStoreServices.filter(service => 
        service.serviceCategory === category
      );

      if (categoryStoreServices.length === 0) {
        setCategoryServices([]);
        return;
      }

      // 为每个服务获取可用员工
      const servicesWithStaff: AvailableStoreService[] = [];
      
      for (const storeService of categoryStoreServices) {
        const availableStaffForService: StaffMember[] = [];
        
        if (storeService.staffIds && storeService.staffIds.length > 0) {
          // 获取所有店铺员工
          const allStaffResult = await staffService.getStoreStaff(storeId);
          
          if (allStaffResult.success && allStaffResult.data) {
            // 筛选能提供此服务的员工
            const staffForService = allStaffResult.data.filter(staff => 
              storeService.staffIds!.includes(staff.userData.uid) && staff.isActive
            );
            
            availableStaffForService.push(...staffForService);
          }
        }
        
        if (availableStaffForService.length > 0) {
          servicesWithStaff.push({
            storeService,
            availableStaff: availableStaffForService
          });
        }
      }
      
      setCategoryServices(servicesWithStaff);
    } catch (error) {
      console.error('Error loading services for category:', error);
      setCategoryServices([]);
    } finally {
      setStepLoading(false);
    }
  };

  const loadStaffForService = async (storeService: StoreServiceData) => {
    try {
      setStepLoading(true);
      
      if (!storeService.staffIds || storeService.staffIds.length === 0) {
        setAvailableStaff([]);
        return;
      }

      const allStaffResult = await staffService.getStoreStaff(storeId);
      
      if (allStaffResult.success && allStaffResult.data) {
        const staffForService = allStaffResult.data.filter(staff => 
          storeService.staffIds!.includes(staff.userData.uid) && staff.isActive
        );
        setAvailableStaff(staffForService);
      }
    } catch (error) {
      console.error('Error loading staff for service:', error);
      setAvailableStaff([]);
    } finally {
      setStepLoading(false);
    }
  };

  const loadAvailableDates = async (staffId: string) => {
    try {
      setStepLoading(true);
      const result = await appointmentService.getStaffAvailableDates(staffId, storeId);
      if (result.success && result.data) {
        setAvailableDates(result.data);
      }
    } catch (error) {
      console.error('Error loading available dates:', error);
    } finally {
      setStepLoading(false);
    }
  };

  const loadTimeSlots = async (staffId: string, serviceId: string, date: string, duration: number) => {
    try {
      setStepLoading(true);
      const result = await appointmentService.getAvailableTimeSlots({
        storeId,
        staffId,
        serviceId,
        date,
        duration
      });
      
      if (result.success && result.data) {
        const slots: TimeSlot[] = result.data.timeSlots.map((slot: { startTime: string; endTime: string; available?: boolean; status?: string }) => ({
          startTime: slot.startTime,
          endTime: slot.endTime,
          available: slot.available || false,
          status: slot.status || 'blocked'
        }));
        setTimeSlots(slots);
      }
    } catch (error) {
      console.error('Error loading time slots:', error);
    } finally {
      setStepLoading(false);
    }
  };

  const handleNext = async () => {
    switch (currentStep) {
      case CreateStep.CUSTOMER_PET:
        if (!selectedCustomer) {
          alert(t('pleaseSelectCustomer'));
          return;
        }
        setCurrentStep(CreateStep.SERVICE_CATEGORY);
        break;

      case CreateStep.SERVICE_CATEGORY:
        if (!selectedCategory) {
          alert(t('pleaseSelectServiceCategory'));
          return;
        }
        await loadServicesForCategory(selectedCategory);
        setCurrentStep(CreateStep.STORE_SERVICE);
        break;

      case CreateStep.STORE_SERVICE:
        if (!selectedStoreService) {
          alert(t('pleaseSelectStoreService'));
          return;
        }
        setAppointmentData(prev => ({ 
          ...prev, 
          storeServiceId: selectedStoreService.sid,
          duration: 60 // Default duration, can be adjusted
        }));
        await loadStaffForService(selectedStoreService);
        setCurrentStep(CreateStep.STAFF);
        break;

      case CreateStep.STAFF:
        if (!selectedStaff) {
          alert(t('pleaseSelectStaff'));
          return;
        }
        setAppointmentData(prev => ({ ...prev, staffId: selectedStaff.userData.uid }));
        await loadAvailableDates(selectedStaff.userData.uid);
        setCurrentStep(CreateStep.DATE);
        break;

      case CreateStep.DATE:
        if (!selectedDate) {
          alert(t('pleaseSelectDate'));
          return;
        }
        setAppointmentData(prev => ({ ...prev, date: selectedDate }));
        await loadTimeSlots(
          selectedStaff!.userData.uid,
          selectedStoreService!.sid,
          selectedDate,
          appointmentData.duration
        );
        setCurrentStep(CreateStep.TIME);
        break;

      case CreateStep.TIME:
        if (!selectedTime) {
          alert(t('pleaseSelectTime'));
          return;
        }
        setAppointmentData(prev => ({ ...prev, startTime: selectedTime }));
        setCurrentStep(CreateStep.CONFIRM);
        break;

      case CreateStep.CONFIRM:
        await createAppointment();
        break;
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const createAppointment = async () => {
    if (!userData?.uid || !selectedStoreService) {
      alert(t('missingRequiredData'));
      return;
    }

    try {
      setStepLoading(true);

      const createData = {
        customerId: appointmentData.customerId,
        staffId: appointmentData.staffId,
        serviceId: appointmentData.storeServiceId,
        date: appointmentData.date,
        startTime: appointmentData.startTime,
        duration: appointmentData.duration,
        notes: appointmentData.notes
      };

      const result = await appointmentService.createAppointment(
        storeId,
        createData,
        userData.uid
      );

      if (result.success) {
        alert(t('appointmentCreatedSuccessfully'));
        router.push(`/store/${storeId}/appointments`);
      } else {
        alert(result.error || t('appointmentCreationFailed'));
      }
    } catch (error) {
      console.error('Error creating appointment:', error);
      alert(t('appointmentCreationFailed'));
    } finally {
      setStepLoading(false);
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case CreateStep.CUSTOMER_PET: return t('steps.customerAndPet');
      case CreateStep.SERVICE_CATEGORY: return t('steps.serviceCategory');
      case CreateStep.STORE_SERVICE: return t('steps.storeService');
      case CreateStep.STAFF: return t('steps.staff');
      case CreateStep.DATE: return t('steps.date');
      case CreateStep.TIME: return t('steps.time');
      case CreateStep.CONFIRM: return t('steps.confirm');
      default: return '';
    }
  };

  const getCustomerDisplayName = (customer: Customer): string => {
    const firstName = customer.customerData.firstName || '';
    const lastName = customer.customerData.lastName || '';
    return `${firstName} ${lastName}`.trim() || customer.customerData.email || 'Unknown Customer';
  };

  const getStaffDisplayName = (staff: StaffMember): string => {
    return staff.userData.displayName || 
           `${staff.userData.firstName || ''} ${staff.userData.lastName || ''}`.trim() ||
           staff.email || 'Unknown Staff';
  };

  const formatTime = (time: string) => {
    const [hour] = time.split(':');
    const hourNum = parseInt(hour);
    const period = hourNum >= 12 ? t('timeSlots.afternoon') : t('timeSlots.morning');
    return `${time} (${period})`;
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case CreateStep.CUSTOMER_PET:
        return (
          <div className="space-y-6">
            {/* Customer Selection */}
            <div>
              <h3 className="text-lg font-semibold text-slate-800 mb-4">{t('selectCustomer')}</h3>
              {customers.length === 0 ? (
                <div className="text-center py-8 text-slate-500">
                  {t('noCustomersFound')}
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">
                  {customers.map((customer) => (
                    <div
                      key={customer.customerData.sid || customer.customerData.uid}
                      onClick={() => {
                        setSelectedCustomer(customer);
                        setAppointmentData(prev => ({ 
                          ...prev, 
                          customerId:customer.customerData.uid || ''
                        }));
                      }}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedCustomer?.customerData.sid === customer.customerData.sid ||
                        selectedCustomer?.customerData.uid === customer.customerData.uid
                          ? 'border-violet-500 bg-violet-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-slate-900">
                            {getCustomerDisplayName(customer)}
                          </p>
                          <p className="text-sm text-slate-500">{customer.customerData.email}</p>
                        </div>
                        <FiUser className="w-5 h-5 text-slate-400" />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Pet Selection */}
            {selectedCustomer && selectedCustomer.pets && selectedCustomer.pets.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-slate-800 mb-4">
                  {t('selectPet')} ({t('optional')})
                </h3>
                <div className="grid grid-cols-1 gap-3">
                  {selectedCustomer.pets.map((pet) => (
                    <div
                      key={pet.sid}
                      onClick={() => {
                        setSelectedPet(selectedPet?.sid === pet.sid ? null : pet);
                        setAppointmentData(prev => ({ 
                          ...prev, 
                          petId: selectedPet?.sid === pet.sid ? undefined : pet.sid
                        }));
                      }}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedPet?.sid === pet.sid
                          ? 'border-violet-500 bg-violet-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-slate-900">{pet.name}</p>
                          <p className="text-sm text-slate-500">
                            {t('petTypes.' + (pet.type || 'OTHER'))} • {pet.breed || t('unknownBreed')}
                          </p>
                        </div>
                        <FiHeart className="w-4 h-4 text-violet-500" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case CreateStep.SERVICE_CATEGORY:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">{t('selectServiceCategory')}</h3>
            
            {availableCategories.length === 0 ? (
              <div className="text-center py-12">
                <FiAlertCircle className="w-16 h-16 text-slate-400 mx-auto mb-4" />
                <div className="text-slate-600 text-lg mb-2">{t('noServicesAvailable')}</div>
                <p className="text-slate-500 mb-6">{t('noServicesDescription')}</p>
                <Button
                  onClick={() => router.push(`/store/${storeId}/services`)}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
                >
                  <FiSettings className="w-4 h-4 mr-2" />
                  {t('goToServiceManagement')}
                  <FiExternalLink className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {availableCategories.map((category) => {
                  const serviceCount = allStoreServices.filter(s => s.serviceCategory === category).length;
                  return (
                    <div
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedCategory === category
                          ? 'border-violet-500 bg-violet-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                    >
                      <div className="text-center">
                        <FiSettings className="w-8 h-8 mx-auto mb-2 text-slate-400" />
                        <p className="font-medium text-slate-900">{t('categories.' + category)}</p>
                        <p className="text-xs text-slate-500 mt-1">{serviceCount} {t('services')}</p>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );

      case CreateStep.STORE_SERVICE:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-800">{t('selectStoreService')}</h3>
            {stepLoading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-slate-600">{t('loadingServices')}</p>
              </div>
            ) : categoryServices.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                {t('noServicesFound')}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-3">
                {categoryServices.map((serviceWithStaff) => (
                  <div
                    key={serviceWithStaff.storeService.sid}
                    onClick={() => setSelectedStoreService(serviceWithStaff.storeService)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedStoreService?.sid === serviceWithStaff.storeService.sid
                        ? 'border-violet-500 bg-violet-50'
                        : 'border-slate-200 hover:border-slate-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-slate-900">{serviceWithStaff.storeService.serviceCategory}</p>
                        <p className="text-sm text-slate-500">
                          {t('breeds.' + serviceWithStaff.storeService.serviceBreed)} • 
                          {serviceWithStaff.availableStaff.length} {t('availableStaff')}
                        </p>
                        <p className="text-xs text-slate-400 mt-1">
                          {t('commission')}: {serviceWithStaff.storeService.commission}%
                        </p>
                      </div>
                      <FiSettings className="w-5 h-5 text-slate-400" />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case CreateStep.STAFF:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-800">{t('selectStaff')}</h3>
            {stepLoading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-slate-600">{t('loadingStaff')}</p>
              </div>
            ) : availableStaff.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                {t('noStaffFound')}
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-3">
                {availableStaff.map((staff) => (
                  <div
                    key={staff.userData.uid}
                    onClick={() => setSelectedStaff(staff)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                      selectedStaff?.userData.uid === staff.userData.uid
                        ? 'border-violet-500 bg-violet-50'
                        : 'border-slate-200 hover:border-slate-300'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-slate-900">
                          {getStaffDisplayName(staff)}
                        </p>
                        <p className="text-sm text-slate-500">{staff.email}</p>
                        <p className="text-xs text-slate-400 mt-1">
                          {staff.isActive ? t('active') : t('inactive')}
                        </p>
                      </div>
                      <FiUsers className="w-5 h-5 text-slate-400" />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case CreateStep.DATE:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-800">{t('calendar.selectDate')}</h3>
            {stepLoading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-slate-600">{t('loading')}</p>
              </div>
            ) : availableDates.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                {t('noAvailableDates')}
              </div>
            ) : (
              <CalendarDatePicker
                availableDates={availableDates}
                selectedDate={selectedDate}
                onDateSelect={(date) => setSelectedDate(date)}
                className="max-w-md mx-auto"
              />
            )}
          </div>
        );

      case CreateStep.TIME:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-slate-800">{t('timeSlots.selectTimeSlot')}</h3>
            {stepLoading ? (
              <div className="text-center py-8">
                <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-slate-600">{t('loadingTimeSlots')}</p>
              </div>
            ) : timeSlots.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                <FiAlertCircle className="w-12 h-12 text-slate-400 mx-auto mb-3" />
                <p className="text-lg font-medium text-slate-600 mb-2">{t('noAvailableTimeSlots')}</p>
                <p className="text-sm text-slate-500">{t('noTimeSlotsDescription')}</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* 工作时间显示 */}
                <div className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-600">{t('workingHours')}:</span>
                    <span className="font-medium text-slate-800">
                      {/* 假设 timeSlots 数据中包含工作时间信息 */}
                      {timeSlots.length > 0 && `${timeSlots[0].startTime} - ${timeSlots[timeSlots.length - 1].endTime}`}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm mt-2">
                    <span className="text-slate-600">{t('serviceDuration')}:</span>
                    <span className="font-medium text-slate-800">{appointmentData.duration} {t('minutes')}</span>
                  </div>
                </div>

                {/* 时间段选择 - 使用滚动容器 */}
                <div className="relative">
                  <div className="text-sm text-slate-600 mb-3 flex items-center justify-between">
                    <span>{t('availableTimeSlots')} ({timeSlots.filter(slot => slot.available).length})</span>
                    <div className="flex items-center space-x-4 text-xs">
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-green-100 border border-green-300 rounded"></div>
                        <span>{t('available')}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-red-100 border border-red-300 rounded"></div>
                        <span>{t('booked')}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <div className="w-3 h-3 bg-slate-100 border border-slate-300 rounded"></div>
                        <span>{t('blocked')}</span>
                      </div>
                    </div>
                  </div>
                  
                  {/* 滚动时间段容器 */}
                  <div className="max-h-80 overflow-y-auto border border-slate-200 rounded-lg p-3 bg-white">
                    <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 gap-2">
                      {timeSlots.map((slot) => {
                        const isAvailable = slot.available && slot.status === 'available';
                        const isBooked = slot.status === 'booked';
                        const isBlocked = slot.status === 'blocked' || slot.status === 'holiday';
                        const isSelected = selectedTime === slot.startTime;

                        return (
                          <div
                            key={slot.startTime}
                            onClick={() => {
                              if (isAvailable) {
                                setSelectedTime(slot.startTime);
                              }
                            }}
                            className={`p-2.5 text-center border rounded-lg transition-all duration-200 text-sm ${
                              isSelected
                                ? 'border-violet-500 bg-violet-500 text-white shadow-md transform scale-105'
                                : isAvailable
                                ? 'border-green-300 bg-green-50 text-green-800 hover:border-green-400 hover:bg-green-100 cursor-pointer'
                                : isBooked
                                ? 'border-red-300 bg-red-50 text-red-600 cursor-not-allowed'
                                : 'border-slate-300 bg-slate-100 text-slate-500 cursor-not-allowed'
                            }`}
                            title={
                              isAvailable 
                                ? t('clickToSelect') 
                                : isBooked 
                                ? t('timeSlotBooked') 
                                : t('timeSlotUnavailable')
                            }
                          >
                            <div className="font-medium">{slot.startTime}</div>
                            <div className="text-xs mt-1 opacity-75">
                              {isAvailable 
                                ? t('available') 
                                : isBooked 
                                ? t('booked') 
                                : t('blocked')
                              }
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 选择的时间显示 */}
                  {selectedTime && (
                    <div className="mt-4 p-4 bg-violet-50 border border-violet-200 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-violet-600 font-medium">{t('selectedTime')}</p>
                          <p className="text-lg font-bold text-violet-800">{formatTime(selectedTime)}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-violet-600">{t('duration')}</p>
                          <p className="text-lg font-bold text-violet-800">{appointmentData.duration} {t('minutes')}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      case CreateStep.CONFIRM:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-slate-800">{t('confirmAppointment')}</h3>
            
            <div className="bg-slate-50 rounded-lg p-6 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('customer')}:</span>
                  <span className="font-medium">{getCustomerDisplayName(selectedCustomer!)}</span>
                </div>
                
                {selectedPet && (
                  <div className="flex justify-between">
                    <span className="text-slate-600">{t('pet')}:</span>
                    <span className="font-medium">{selectedPet.name}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('service')}:</span>
                  <span className="font-medium">{selectedStoreService?.serviceCategory}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('staff')}:</span>
                  <span className="font-medium">{getStaffDisplayName(selectedStaff!)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('date')}:</span>
                  <span className="font-medium">
                    {new Date(selectedDate).toLocaleDateString('zh-CN')}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('time')}:</span>
                  <span className="font-medium">{formatTime(selectedTime)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('duration')}:</span>
                  <span className="font-medium">{appointmentData.duration} {t('minutes')}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-slate-600">{t('serviceBreed')}:</span>
                  <span className="font-medium">{t('breeds.' + selectedStoreService?.serviceBreed)}</span>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                {t('notes')} ({t('optional')})
              </label>
              <textarea
                value={appointmentData.notes}
                onChange={(e) => setAppointmentData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder={t('appointmentNotes')}
                rows={3}
                className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-violet-500 focus:border-violet-500"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-violet-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-gray-600">{t('loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StoreHeader storeName={t('title')} storeId={storeId} currentPage="appointments" />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => router.push(`/store/${storeId}/appointments`)}
                className="bg-slate-100 hover:bg-slate-200 text-slate-700"
              >
                <FiArrowLeft className="w-4 h-4 mr-2" />
                {t('back')}
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-slate-900 flex items-center">
                  <FiCalendar className="w-8 h-8 mr-3 text-violet-600" />
                  {t('title')}
                </h1>
                <p className="text-slate-600 font-medium mt-1">
                  {t('step')} {currentStep} {t('of')} 7 • {getStepTitle()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Progress */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            {[1, 2, 3, 4, 5, 6, 7].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step < currentStep
                    ? 'bg-green-500 text-white'
                    : step === currentStep
                    ? 'bg-violet-500 text-white'
                    : 'bg-slate-200 text-slate-500'
                }`}>
                  {step < currentStep ? <FiCheck className="w-4 h-4" /> : step}
                </div>
                {step < 7 && (
                  <div className={`w-8 h-1 mx-2 ${
                    step < currentStep ? 'bg-green-500' : 'bg-slate-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          {renderStepContent()}
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            onClick={currentStep === 1 ? () => router.push(`/store/${storeId}/appointments`) : handlePrevious}
            className="bg-slate-100 hover:bg-slate-200 text-slate-700"
          >
            <FiChevronLeft className="w-4 h-4 mr-2" />
            {currentStep === 1 ? t('cancel') : t('previous')}
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={stepLoading}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700 text-white"
          >
            {stepLoading ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            ) : currentStep === 7 ? (
              <FiCheck className="w-4 h-4 mr-2" />
            ) : (
              <FiChevronRight className="w-4 h-4 mr-2" />
            )}
            {currentStep === 7 ? t('confirmAppointment') : t('next')}
          </Button>
        </div>
      </div>
    </div>
  );
} 