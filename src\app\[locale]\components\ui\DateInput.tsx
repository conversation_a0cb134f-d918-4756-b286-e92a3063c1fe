'use client';

import React from 'react';

interface DateInputProps {
  id?: string;
  label?: string;
  value?: string; // ISO date string (YYYY-MM-DD)
  onChange?: (value: string) => void;
  error?: string;
  placeholder?: string;
  className?: string;
  required?: boolean;
  minDate?: string; // ISO date string
  maxDate?: string; // ISO date string
}

export const DateInput: React.FC<DateInputProps> = ({
  id,
  label,
  value = '',
  onChange,
  error,
  placeholder,
  className = '',
  required = false,
  minDate,
  maxDate,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <input
        id={id}
        type="date"
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        min={minDate}
        max={maxDate}
        required={required}
        className={`w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#A126FF] focus:border-[#A126FF] transition-all duration-200 ${
          error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''
        }`}
      />

      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}
    </div>
  );
};

export default DateInput; 