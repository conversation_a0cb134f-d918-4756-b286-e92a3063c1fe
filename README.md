# OneNata Admin Portal

一个基于 Next.js 和 Firebase 的多语言管理后台系统，支持用户认证、数据管理和国际化。

## 🚀 功能特性

### 认证系统
- ✅ Firebase Authentication 集成
- ✅ 邮箱/密码登录注册
- ✅ 邮箱验证
- ✅ 密码重置
- ✅ 用户资料管理
- ✅ JWT 令牌验证

### 国际化支持
- ✅ 8种语言支持：英语、德语、法语、韩语、简体中文、繁体中文、日语、西班牙语
- ✅ 动态语言切换
- ✅ 完整的翻译内容

### 用户界面
- ✅ 现代化响应式设计
- ✅ Tailwind CSS 样式
- ✅ 深色/浅色主题
- ✅ 移动端优化

### 开发体验
- ✅ TypeScript 类型安全
- ✅ Firebase 模拟器支持
- ✅ ESLint 代码规范
- ✅ 热重载开发

## 📦 技术栈

- **前端框架**: Next.js 15 (App Router)
- **认证**: Firebase Authentication
- **数据库**: Firebase Firestore
- **存储**: Firebase Storage
- **样式**: Tailwind CSS
- **国际化**: next-intl
- **类型**: TypeScript
- **开发工具**: ESLint, Firebase Emulator
- **cloud Function**: Firebase Cloud Function

## 🛠️ 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd onenata_admin
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

创建 `.env.local` 文件：

```bash
# Firebase 开发环境配置
NEXT_PUBLIC_FIREBASE_API_KEY=demo-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=demo-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=demo-project
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=demo-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# Firebase 模拟器配置
NEXT_PUBLIC_FIREBASE_AUTH_EMULATOR_HOST=localhost:9099
NEXT_PUBLIC_FIREBASE_FIRESTORE_EMULATOR_HOST=localhost:8080
NEXT_PUBLIC_FIREBASE_STORAGE_EMULATOR_HOST=localhost:9199

# 开发环境标识
NODE_ENV=development

# Google Map Api
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=GOOGLEMAPAPIKEY
```

### 4. 测试 Firebase 配置

```bash
npm run test:firebase
```

### 5. 启动 Firebase 模拟器

```bash
firebase emulators:start  --import=./emulators-data --export-on-exit=./emulators-data
```


### 6. 启动开发服务器

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
onenata_admin/
├── src/
│   ├── app/
│   │   ├── [locale]/              # 国际化路由
│   │   │   ├── auth/              # 认证页面
│   │   │   │   ├── login/         # 登录页
│   │   │   │   ├── signup/        # 注册页
│   │   │   │   ├── verify-email/  # 邮箱验证
│   │   │   │   └── profile-setup/ # 资料设置
│   │   │   ├── dashboard/         # 仪表板
│   │   │   └── components/        # 组件
│   │   └── lib/                   # 业务逻辑
│   │       ├── controllers/       # 控制器
│   │       ├── services/          # 服务层
│   │       ├── models/            # 数据模型
│   │       └── types/             # 类型定义
│   ├── lib/
│   │   └── firebase/              # Firebase 配置
│   ├── i18n/                      # 国际化配置
│   └── middleware.ts              # 中间件
├── messages/                      # 翻译文件
├── functions/                     # Firebase Functions
├── dataconnect/                   # Firebase Data Connect
└── scripts/                       # 脚本文件
```

## 🔧 可用脚本

```bash
# 开发
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器
npm run lint         # 代码检查

# Firebase
npm run test:firebase # 测试 Firebase 配置
npm run emulator     # 启动 Firebase 模拟器
```

## 🌐 页面路由

### 公共页面
- `/` - 首页（语言选择）
- `/[locale]/auth/login` - 登录页
- `/[locale]/auth/signup` - 注册页
- `/[locale]/auth/verify-email` - 邮箱验证
- `/[locale]/auth/profile-setup` - 资料设置

### 认证页面
- `/[locale]/dashboard` - 仪表板

### API 端点
- `/api/auth/login` - 登录
- `/api/auth/register` - 注册
- `/api/auth/forgot-password` - 忘记密码

## 🔐 认证流程

1. **注册**: 用户填写基本信息 → Firebase 创建账户 → 发送验证邮件
2. **验证**: 用户点击邮件链接 → 邮箱验证完成
3. **登录**: 用户输入邮箱密码 → Firebase 验证 → 返回 JWT 令牌
4. **授权**: 客户端携带令牌 → 服务端验证 → 访问受保护资源

## 📱 响应式设计

- **桌面端**: 1200px+ 全功能界面
- **平板端**: 768px-1199px 适配布局
- **移动端**: <768px 优化体验

## 🌍 国际化

支持的语言：
- 🇺🇸 English (en)
<!-- - 🇩🇪 Deutsch (de)
- 🇫🇷 Français (fr)
- 🇰🇷 한국어 (ko) -->
- 🇨🇳 简体中文 (zh-CN)
<!-- - 🇹🇼 繁體中文 (zh-TW)
- 🇯🇵 日本語 (ja)
- 🇪🇸 Español (es) -->

## 🚀 部署

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 部署完成

### 环境变量配置

生产环境需要配置真实的 Firebase 项目信息：

```bash
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
FIREBASE_SERVICE_ACCOUNT_KEY=your-service-account-key
```

## 📚 相关文档

- **开发者文档**: [DEVELOPER_DOCUMENTATION.md](./DEVELOPER_DOCUMENTATION.md) - 完整的项目文档
- **快速开始**: [QUICK_START.md](./QUICK_START.md) - 5分钟快速启动指南
- **API 参考**: [API_REFERENCE.md](./API_REFERENCE.md) - 详细的 API 文档
- **开发文档**: `.cursor/` - 开发过程中的逻辑文档
- **项目文档**: `.docs/` - 功能实现文档

## �� 许可证

MIT License
