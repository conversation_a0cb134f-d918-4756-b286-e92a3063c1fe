import { NextRequest, NextResponse } from 'next/server';

const GOOGLE_MAPS_API_BASE = 'https://maps.googleapis.com/maps/api';
const API_KEY = process.env.GOOGLE_MAPS_API_KEY || process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, params } = body;

    if (!API_KEY) {
      return NextResponse.json(
        { success: false, error: 'Google Maps API key not configured' },
        { status: 500 }
      );
    }

    let url: string;
    let searchParams: URLSearchParams;

    switch (action) {
      case 'geocode':
        // Geocoding API call
        searchParams = new URLSearchParams({
          key: API_KEY,
          language: 'en'
        });
        if (params.address) {
          searchParams.append('address', params.address);
        }
        if (params.latlng) {
          searchParams.append('latlng', params.latlng);
        }
        if (params.components) {
          searchParams.append('components', params.components);
        }
        
        url = `${GOOGLE_MAPS_API_BASE}/geocode/json?${searchParams}&language=en`;
        console.log('url for geocode', url);
        break;

      case 'place_autocomplete':
        // Places Autocomplete API call
        searchParams = new URLSearchParams({
          input: params.input,
          key: API_KEY,
          types: params.types || 'establishment',
          language: 'en'
        });
        if (params.sessiontoken) {
          searchParams.append('sessiontoken', params.sessiontoken);
        }
        if (params.location) {
          searchParams.append('location', `${params.location.lat},${params.location.lng}`);
        }
        if (params.radius) {
          searchParams.append('radius', params.radius.toString());
        }
        if (params.strictbounds) {
          searchParams.append('strictbounds', params.strictbounds.toString());
        }
        url = `${GOOGLE_MAPS_API_BASE}/place/autocomplete/json?${searchParams}&language=en`;
        console.log('url for place_autocomplete', url);
        break;

      case 'place_nearby':
        // Places Nearby Search API call
        searchParams = new URLSearchParams({
          location: `${params.location.lat},${params.location.lng}`,
          radius: params.radius.toString(),
          key: API_KEY,
          language: 'en'
        });
        if (params.keyword) {
          searchParams.append('keyword', params.keyword);
        }
        if (params.type) {
          searchParams.append('type', params.type);
        }
        url = `${GOOGLE_MAPS_API_BASE}/place/nearbysearch/json?${searchParams}&language=en`;
        console.log('url for place_nearby', url);
        break;

      case 'get_photo_url':
        // Google Places Photo API call
        if (!params.photo_reference) {
          return NextResponse.json(
            { success: false, error: 'Photo reference is required' },
            { status: 400 }
          );
        }
        
        try {
          searchParams = new URLSearchParams({
            photoreference: params.photo_reference,
            key: API_KEY,
            maxwidth: params.maxwidth || '800',
            maxheight: params.maxheight || '600'
          });
          
          const photoUrl = `${GOOGLE_MAPS_API_BASE}/place/photo?${searchParams}`;
          console.log('Fetching photo from:', photoUrl);
          
          // Fetch the actual image data with redirect handling
          const imageResponse = await fetch(photoUrl, {
            redirect: 'follow',
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; GoogleMapsBot/1.0)'
            }
          });
          
          console.log('Image response status:', imageResponse.status);
          console.log('Image response headers:', Object.fromEntries(imageResponse.headers.entries()));
          
          if (!imageResponse.ok) {
            throw new Error(`Failed to fetch image: ${imageResponse.statusText}`);
          }
          
          // Check if we got actual image data
          const contentType = imageResponse.headers.get('content-type');
          if (!contentType || !contentType.startsWith('image/')) {
            console.error('Invalid content type:', contentType);
            throw new Error(`Invalid content type: ${contentType}`);
          }
          
          // Get the image as a buffer
          const imageBuffer = await imageResponse.arrayBuffer();
          console.log('Image buffer size:', imageBuffer.byteLength);
          console.log('Content type:', contentType);
          
          // Validate buffer size
          if (imageBuffer.byteLength === 0) {
            throw new Error('Empty image buffer received');
          }
          
          // Convert to base64
          const base64Image = Buffer.from(imageBuffer).toString('base64');
          const dataUrl = `data:${contentType};base64,${base64Image}`;
          
          console.log('Data URL length:', dataUrl.length);
          console.log('Data URL preview:', dataUrl.substring(0, 100) + '...');
          
          return NextResponse.json({
            success: true,
            data: {
              photoUrl: dataUrl,
              contentType: contentType
            }
          });
        } catch (error) {
          console.error('Error fetching photo:', error);
          return NextResponse.json(
            { success: false, error: `Failed to fetch photo: ${error instanceof Error ? error.message : 'Unknown error'}` },
            { status: 500 }
          );
        }

      case 'place_details':
        // Places Details API call
        searchParams = new URLSearchParams({
          place_id: params.place_id,
          key: API_KEY,
          fields: params.fields || 'address_component,formatted_address,geometry,name,place_id,business_status,website,formatted_phone_number,opening_hours,rating,user_ratings_total,types,photos,email,international_phone_number,url,vicinity',
          language: 'en'
        });
        url = `${GOOGLE_MAPS_API_BASE}/place/details/json?${searchParams}&language=en`;
        console.log('url for place_details', url);
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    console.log('Making Google Maps API call:', url);

    const response = await fetch(url);
    const data = await response.json();

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Google Maps API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 