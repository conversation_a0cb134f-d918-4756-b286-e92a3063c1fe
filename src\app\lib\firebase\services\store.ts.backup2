import { PortalUserData } from '../../models/portal-user';
import { 
  StoreAccount, 
  StoreInfo
} from '../../models/store';
import { 
  UserType, 
  StoreStatus, 
  StoreVerifiedStatus,
  BusinessType
} from '../../models/types';
import { FirestoreDocument } from './firestore';
import FirestoreService from './firestore';
import { v4 as uuidv4 } from 'uuid';

export interface CreateStoreData {
  storeName: string;
  businessType: BusinessType;
  description?: string;
  phone: string;
  email: string;
  website?: string;
  currentAddress: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    province: string;
    country: string;
    postCode: string;
  };
  services: {
    grooming: boolean;
    boarding: boolean;
    veterinary: boolean;
    training: boolean;
    retail: boolean;
  };
  businessHours?: {
    [key: string]: {
      open: string;
      close: string;
      closed: boolean;
    };
  };
  googlePlaceId?: string;
  avatarUrl?: string;
  storePhotos?: string[];
}

// 使用联合类型来处理 Firestore 文档
export type StoreAccountDocument = StoreAccount & FirestoreDocument;
export type StoreInfoDocument = StoreInfo & FirestoreDocument;

export interface StoreListItem extends StoreInfoDocument {
  accountInfo: StoreAccountDocument;
}

class StoreService {
  private readonly STORE_ACCOUNT_COLLECTION = 'store-account';
  private readonly STORE_INFO_COLLECTION = 'store-info';
  private readonly PORTAL_USER_DATA_COLLECTION = 'portal-user-data';

  /**
   * 检查用户是否有权限创建店铺
   */
  async canCreateStore(userDataSid: string): Promise<boolean> {
    try {
      const userDoc = await FirestoreService.getById(this.PORTAL_USER_DATA_COLLECTION, userDataSid);
      if (!userDoc.success || !userDoc.data) {
        return false;
      }

      const userType = userDoc.data.userType as UserType;
      return userType === UserType.PETSTORE_OWNER || 
             userType === UserType.ONENATA_ADMIN ||
             userType === UserType.PETSTORE_BUSINESS;
    } catch (error) {
      console.error('Error checking store creation permission:', error);
      return false;
    }
  }

  /**
   * 创建新店铺
   */
  async createStore(userData: PortalUserData, storeData: CreateStoreData): Promise<{
    success: boolean;
    data?: string; // store ID
    error?: string;
  }> {
    try {
      // 检查权限
      if (!userData.sid) {
        return {
          success: false,
          error: 'User data not found'
        };
      }

      console.log('userData to  create store:', userData);

      const canCreate = await this.canCreateStore(userData.sid || '');
      if (!canCreate) {
        return {
          success: false,
          error: 'User does not have permission to create stores'
        };
      }

      // 生成店铺ID
      const storeId = uuidv4();

      // 创建StoreAccount
      const storeAccount = {
        sid: storeId,
        name: storeData.storeName,
        ownerId: userData.uid,
        storeName: storeData.storeName,
        storeVerifiedStatus: StoreVerifiedStatus.PENDING,
        storeStatus: StoreStatus.ACTIVE,
        googlePlaceId: storeData.googlePlaceId || '',
        isValid: true,
        isSynced: true,
        createdBy: userData.uid,
        updatedBy: userData.uid,
        tags: []
      };

      console.log('storeAccount to  create store:', storeAccount); 
      
      const storeInfoSid = uuidv4();

      // 创建StoreInfo
      const storeInfo = {
        sid: storeInfoSid,
        name: storeData.storeName,
        storeId: storeId,
        currentAddress: storeData.currentAddress,
        phone: storeData.phone,
        email: storeData.email,
        website: storeData.website,
        businessType: storeData.businessType,
        description: storeData.description,
        services: storeData.services,
        businessHours: storeData.businessHours,
        avatarUrl: storeData.avatarUrl,
        storePhotos: storeData.storePhotos,
        appointmentOpen: false,
        hasSpecialEvent: false,
        staffs: [userData.uid], // 店主自动成为员工
        customerList: [],
        isValid: true,
        isSynced: true,
        createdBy: userData.uid,
        updatedBy: userData.uid,
        tags: []
      };

      // 保存到数据库
      const accountResult = await FirestoreService.createWithId(
        this.STORE_ACCOUNT_COLLECTION, 
        storeId, 
        storeAccount
      );

      if (!accountResult.success) {
        return {
          success: false,
          error: 'Failed to create store account'
        };
      }

      const infoResult = await FirestoreService.createWithId(
        this.STORE_INFO_COLLECTION, 
        storeInfoSid, 
        storeInfo
      );

      if (!infoResult.success) {
        // 如果StoreInfo创建失败，删除已创建的StoreAccount
        await FirestoreService.delete(this.STORE_ACCOUNT_COLLECTION, storeId);
        return {
          success: false,
          error: 'Failed to create store info'
        };
      }

      return {
        success: true,
        data: storeId
      };

    } catch (error) {
      console.error('Error creating store:', error);
      return {
        success: false,
        error: 'Failed to create store'
      };
    }
  }

  /**
   * 获取用户的所有店铺
   */
  async getUserStores(userId: string): Promise<{
    success: boolean;
    data?: StoreListItem[];
    error?: string;
  }> {
    try {
      // 获取用户拥有的店铺账户
      const accountsResult = await FirestoreService.getMany<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION,
        {
          where: [{ field: 'ownerId', operator: '==', value: userId }],
          orderBy: [{ field: 'createdAt', direction: 'desc' }]
        }
      );

      if (!accountsResult.success || !accountsResult.data) {
        return {
          success: true,
          data: []
        };
      }

      // 获取对应的店铺信息
      const storeList: StoreListItem[] = [];
      
      for (const account of accountsResult.data) {
        // 确保 docId 是字符串类型
        const docId = (account as FirestoreDocument & { sid?: string }).sid || account.id;
        if (!docId || typeof docId !== 'string') {
          console.warn('Invalid store ID:', docId);
          continue;
        }

        const infoResult = await FirestoreService.getById<FirestoreDocument>(
          this.STORE_INFO_COLLECTION, 
          docId
        );

        if (infoResult.success && infoResult.data) {
          storeList.push({
            ...infoResult.data,
            accountInfo: account
          } as StoreListItem);
        }
      }

      return {
        success: true,
        data: storeList
      };

    } catch (error) {
      console.error('Error getting user stores:', error);
      return {
        success: false,
        error: 'Failed to get user stores'
      };
    }
  }

  /**
   * 获取单个店铺详情
   */
  async getStoreDetails(storeId: string): Promise<{
    success: boolean;
    data?: StoreListItem;
    error?: string;
  }> {
    try {
      const accountResult = await FirestoreService.getById<FirestoreDocument>(
        this.STORE_ACCOUNT_COLLECTION, 
        storeId
      );

      const infoResult = await FirestoreService.getById<FirestoreDocument>(
        this.STORE_INFO_COLLECTION, 
        storeId
      );

      if (!accountResult.success || !infoResult.success || 
          !accountResult.data || !infoResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      return {
        success: true,
        data: {
          ...infoResult.data,
          accountInfo: accountResult.data
        } as StoreListItem
      };

    } catch (error) {
      console.error('Error getting store details:', error);
      return {
        success: false,
        error: 'Failed to get store details'
      };
    }
  }

  /**
   * 更新店铺信息
   */
  async updateStore(storeId: string, updates: Partial<CreateStoreData>): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      const accountUpdates: Record<string, unknown> = {};
      const infoUpdates: Record<string, unknown> = {};

      // 分离需要更新的字段
      if (updates.storeName) {
        accountUpdates.storeName = updates.storeName;
        accountUpdates.name = updates.storeName;
      }

      if (updates.googlePlaceId) {
        accountUpdates.googlePlaceId = updates.googlePlaceId;
      }

      // 其他字段更新到StoreInfo
      Object.keys(updates).forEach(key => {
        if (key !== 'storeName' && key !== 'googlePlaceId') {
          infoUpdates[key] = updates[key as keyof CreateStoreData];
        }
      });

      // 添加更新者信息
      accountUpdates.updatedBy = storeId;
      infoUpdates.updatedBy = storeId;

      // 更新数据库
      if (Object.keys(accountUpdates).length > 1) { // 除了updatedBy还有其他字段
        const accountResult = await FirestoreService.update(
          this.STORE_ACCOUNT_COLLECTION, 
          storeId, 
          accountUpdates
        );
        
        if (!accountResult.success) {
          return {
            success: false,
            error: 'Failed to update store account'
          };
        }
      }

      if (Object.keys(infoUpdates).length > 1) { // 除了updatedBy还有其他字段
        const infoResult = await FirestoreService.update(
          this.STORE_INFO_COLLECTION, 
          storeId, 
          infoUpdates
        );
        
        if (!infoResult.success) {
          return {
            success: false,
            error: 'Failed to update store info'
          };
        }
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error updating store:', error);
      return {
        success: false,
        error: 'Failed to update store'
      };
    }
  }

  /**
   * 删除店铺
   */
  async deleteStore(storeId: string, ownerId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 验证所有权
      const storeResult = await this.getStoreDetails(storeId);
      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      if (storeResult.data.accountInfo.ownerId !== ownerId) {
        return {
          success: false,
          error: 'Unauthorized to delete this store'
        };
      }

      // 删除店铺信息和账户
      const infoDeleteResult = await FirestoreService.delete(
        this.STORE_INFO_COLLECTION, 
        storeId
      );

      const accountDeleteResult = await FirestoreService.delete(
        this.STORE_ACCOUNT_COLLECTION, 
        storeId
      );

      if (!infoDeleteResult.success || !accountDeleteResult.success) {
        return {
          success: false,
          error: 'Failed to delete store'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error deleting store:', error);
      return {
        success: false,
        error: 'Failed to delete store'
      };
    }
  }

  /**
   * 获取店铺统计信息
   */
  async getStoreStats(userId: string): Promise<{
    success: boolean;
    data?: {
      totalStores: number;
      activeStores: number;
      pendingStores: number;
      rejectedStores: number;
    };
    error?: string;
  }> {
    try {
      const storesResult = await this.getUserStores(userId);
      
      if (!storesResult.success || !storesResult.data) {
        return {
          success: true,
          data: {
            totalStores: 0,
            activeStores: 0,
            pendingStores: 0,
            rejectedStores: 0
          }
        };
      }

      const stores = storesResult.data;
      const stats = {
        totalStores: stores.length,
        activeStores: stores.filter(s => s.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.APPROVED).length,
        pendingStores: stores.filter(s => s.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.PENDING).length,
        rejectedStores: stores.filter(s => s.accountInfo.storeVerifiedStatus === StoreVerifiedStatus.REJECTED).length
      };

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting store stats:', error);
      return {
        success: false,
        error: 'Failed to get store statistics'
      };
    }
  }
}

   * 审核店铺 - 管理员功能
   */
  async approveStore(storeId: string, adminUserId: string, decision: StoreVerifiedStatus, reason?: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 验证管理员权限
      const adminDoc = await FirestoreService.getById(this.PORTAL_USER_DATA_COLLECTION, adminUserId);
      if (!adminDoc.success || !adminDoc.data) {
        return {
          success: false,
          error: 'Admin user not found'
        };
      }

      const userType = adminDoc.data.userType as UserType;
      if (userType !== UserType.ONENATA_ADMIN) {
        return {
          success: false,
          error: 'Insufficient permissions'
        };
      }

      // 更新店铺审核状态
      const updateResult = await FirestoreService.update(
        this.STORE_ACCOUNT_COLLECTION,
        storeId,
        {
          storeVerifiedStatus: decision,
          updatedBy: adminUserId,
          reviewedAt: new Date(),
          reviewReason: reason
        }
      );

      if (!updateResult.success) {
        return {
          success: false,
          error: 'Failed to update store status'
        };
      }

      // 创建审核记录
      await this.createApprovalRecord(storeId, adminUserId, decision, reason);

      // 发送通知给店主
      await this.sendApprovalNotification(storeId, decision, reason);

      return {
        success: true
      };

    } catch (error) {
      console.error('Error approving store:', error);
      return {
        success: false,
        error: 'Failed to approve store'
      };
    }
  }

  /**
   * 创建审核记录
   */
  private async createApprovalRecord(storeId: string, adminUserId: string, decision: StoreVerifiedStatus, reason?: string): Promise<void> {
    try {
      const recordId = uuidv4();
      const record = {
        sid: recordId,
        storeId: storeId,
        adminUserId: adminUserId,
        decision: decision,
        reason: reason,
        createdAt: new Date(),
        isValid: true,
        isSynced: true,
        createdBy: adminUserId,
        updatedBy: adminUserId,
        tags: []
      };

      await FirestoreService.createWithId('store-approval-records', recordId, record);
    } catch (error) {
      console.error('Error creating approval record:', error);
    }
  }

  /**
   * 发送审核通知
   */
  private async sendApprovalNotification(storeId: string, decision: StoreVerifiedStatus, reason?: string): Promise<void> {
    try {
      // 获取店铺信息
      const storeResult = await this.getStoreDetails(storeId);
      if (!storeResult.success || !storeResult.data) {
        return;
      }

      const store = storeResult.data;
      const ownerId = store.accountInfo.ownerId;

      // 创建通知
      const notificationId = uuidv4();
      const notification = {
        sid: notificationId,
        userId: ownerId,
        type: 'store_approval',
        title: decision === StoreVerifiedStatus.APPROVED ? '店铺审核通过' : '店铺审核未通过',
        message: this.getApprovalMessage(store.name, decision, reason),
        data: {
          storeId: storeId,
          storeName: store.name,
          decision: decision,
          reason: reason
        },
        read: false,
        createdAt: new Date(),
        isValid: true,
        isSynced: true,
        createdBy: 'system',
        updatedBy: 'system',
        tags: []
      };

      await FirestoreService.createWithId('notifications', notificationId, notification);

      // 如果用户启用了邮件通知，发送邮件
      await this.sendApprovalEmail(store, decision, reason);

    } catch (error) {
      console.error('Error sending approval notification:', error);
    }
  }

  /**
   * 生成审核消息
   */
  private getApprovalMessage(storeName: string, decision: StoreVerifiedStatus, reason?: string): string {
    switch (decision) {
      case StoreVerifiedStatus.APPROVED:
        return `恭喜！您的店铺"${storeName}"已通过审核，现在可以正常营业了。`;
      case StoreVerifiedStatus.REJECTED:
        return `很抱歉，您的店铺"${storeName}"未通过审核。${reason ? `原因：${reason}` : ''}请修改后重新提交。`;
      default:
        return `您的店铺"${storeName}"审核状态已更新。`;
    }
  }

  /**
   * 发送审核邮件
   */
  private async sendApprovalEmail(store: StoreListItem, decision: StoreVerifiedStatus, reason?: string): Promise<void> {
    try {
      // 这里可以集成邮件服务，如SendGrid、Nodemailer等
      console.log('Sending approval email:', {
        to: store.email,
        storeName: store.name,
        decision: decision,
        reason: reason
      });

      // TODO: 实现实际的邮件发送逻辑
    } catch (error) {
      console.error('Error sending approval email:', error);
    }
  }

  /**
   * 获取待审核店铺列表 - 管理员功能
   */
  async getPendingStores(adminUserId: string): Promise<{
    success: boolean;
    data?: StoreListItem[];
    error?: string;
  }> {
    try {
      // 验证管理员权限
      const adminDoc = await FirestoreService.getById(this.PORTAL_USER_DATA_COLLECTION, adminUserId);
      if (!adminDoc.success || !adminDoc.data) {
        return {
          success: false,
          error: 'Admin user not found'
        };
      }

      const userType = adminDoc.data.userType as UserType;
      if (userType !== UserType.ONENATA_ADMIN) {
        return {
          success: false,
          error: 'Insufficient permissions'
        };
      }

      // 获取待审核的店铺账户
      const accountsResult = await FirestoreService.query(
        this.STORE_ACCOUNT_COLLECTION,
        [
          { field: 'storeVerifiedStatus', operator: '==', value: StoreVerifiedStatus.PENDING },
          { field: 'isValid', operator: '==', value: true }
        ]
      );

      if (!accountsResult.success || !accountsResult.data) {
        return {
          success: true,
          data: []
        };
      }

      // 获取对应的店铺信息
      const stores: StoreListItem[] = [];
      for (const account of accountsResult.data) {
        const infoResult = await FirestoreService.query(
          this.STORE_INFO_COLLECTION,
          [
            { field: 'storeId', operator: '==', value: account.sid },
            { field: 'isValid', operator: '==', value: true }
          ]
        );

        if (infoResult.success && infoResult.data && infoResult.data.length > 0) {
          stores.push({
            ...infoResult.data[0],
            accountInfo: account
          } as StoreListItem);
        }
      }

      return {
        success: true,
        data: stores
      };

    } catch (error) {
      console.error('Error getting pending stores:', error);
      return {
        success: false,
        error: 'Failed to get pending stores'
      };
    }
  }

  /**
   * 获取审核记录
   */
  async getApprovalHistory(storeId: string): Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }> {
    try {
      const result = await FirestoreService.query(
        'store-approval-records',
        [
          { field: 'storeId', operator: '==', value: storeId },
          { field: 'isValid', operator: '==', value: true }
        ],
        { field: 'createdAt', direction: 'desc' }
      );

      return {
        success: true,
        data: result.data || []
      };

    } catch (error) {
      console.error('Error getting approval history:', error);
      return {
        success: false,
        error: 'Failed to get approval history'
      };
    }
  }

  /**
   * 重新提交审核
   */
  async resubmitForApproval(storeId: string, ownerId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 验证所有权
      const storeResult = await this.getStoreDetails(storeId);
      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      if (storeResult.data.accountInfo.ownerId !== ownerId) {
        return {
          success: false,
          error: 'Unauthorized'
        };
      }

      // 只有被拒绝的店铺才能重新提交
      if (storeResult.data.accountInfo.storeVerifiedStatus !== StoreVerifiedStatus.REJECTED) {
        return {
          success: false,
          error: 'Store is not in rejected status'
        };
      }

      // 更新状态为待审核
      const updateResult = await FirestoreService.update(
        this.STORE_ACCOUNT_COLLECTION,
        storeId,
        {
          storeVerifiedStatus: StoreVerifiedStatus.PENDING,
          updatedBy: ownerId,
          resubmittedAt: new Date()
        }
      );

      if (!updateResult.success) {
        return {
          success: false,
          error: 'Failed to resubmit store'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error resubmitting store:', error);
      return {
        success: false,
        error: 'Failed to resubmit store'
      };
    }
  }
}

export default new StoreService();

  /**
   * 审核店铺 - 管理员功能
   */
  async approveStore(storeId: string, adminUserId: string, decision: StoreVerifiedStatus, reason?: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 验证管理员权限
      const adminDoc = await FirestoreService.getById(this.PORTAL_USER_DATA_COLLECTION, adminUserId);
      if (!adminDoc.success || !adminDoc.data) {
        return {
          success: false,
          error: 'Admin user not found'
        };
      }

      const userType = adminDoc.data.userType as UserType;
      if (userType !== UserType.ONENATA_ADMIN) {
        return {
          success: false,
          error: 'Insufficient permissions'
        };
      }

      // 更新店铺审核状态
      const updateResult = await FirestoreService.update(
        this.STORE_ACCOUNT_COLLECTION,
        storeId,
        {
          storeVerifiedStatus: decision,
          updatedBy: adminUserId,
          reviewedAt: new Date(),
          reviewReason: reason
        }
      );

      if (!updateResult.success) {
        return {
          success: false,
          error: 'Failed to update store status'
        };
      }

      // 创建审核记录
      await this.createApprovalRecord(storeId, adminUserId, decision, reason);

      // 发送通知给店主
      await this.sendApprovalNotification(storeId, decision, reason);

      return {
        success: true
      };

    } catch (error) {
      console.error('Error approving store:', error);
      return {
        success: false,
        error: 'Failed to approve store'
      };
    }
  }

  /**
   * 创建审核记录
   */
  private async createApprovalRecord(storeId: string, adminUserId: string, decision: StoreVerifiedStatus, reason?: string): Promise<void> {
    try {
      const recordId = uuidv4();
      const record = {
        sid: recordId,
        storeId: storeId,
        adminUserId: adminUserId,
        decision: decision,
        reason: reason,
        createdAt: new Date(),
        isValid: true,
        isSynced: true,
        createdBy: adminUserId,
        updatedBy: adminUserId,
        tags: []
      };

      await FirestoreService.createWithId('store-approval-records', recordId, record);
    } catch (error) {
      console.error('Error creating approval record:', error);
    }
  }

  /**
   * 发送审核通知
   */
  private async sendApprovalNotification(storeId: string, decision: StoreVerifiedStatus, reason?: string): Promise<void> {
    try {
      // 获取店铺信息
      const storeResult = await this.getStoreDetails(storeId);
      if (!storeResult.success || !storeResult.data) {
        return;
      }

      const store = storeResult.data;
      const ownerId = store.accountInfo.ownerId;

      // 创建通知
      const notificationId = uuidv4();
      const notification = {
        sid: notificationId,
        userId: ownerId,
        type: 'store_approval',
        title: decision === StoreVerifiedStatus.APPROVED ? '店铺审核通过' : '店铺审核未通过',
        message: this.getApprovalMessage(store.name, decision, reason),
        data: {
          storeId: storeId,
          storeName: store.name,
          decision: decision,
          reason: reason
        },
        read: false,
        createdAt: new Date(),
        isValid: true,
        isSynced: true,
        createdBy: 'system',
        updatedBy: 'system',
        tags: []
      };

      await FirestoreService.createWithId('notifications', notificationId, notification);

      // 如果用户启用了邮件通知，发送邮件
      await this.sendApprovalEmail(store, decision, reason);

    } catch (error) {
      console.error('Error sending approval notification:', error);
    }
  }

  /**
   * 生成审核消息
   */
  private getApprovalMessage(storeName: string, decision: StoreVerifiedStatus, reason?: string): string {
    switch (decision) {
      case StoreVerifiedStatus.APPROVED:
        return `恭喜！您的店铺"${storeName}"已通过审核，现在可以正常营业了。`;
      case StoreVerifiedStatus.REJECTED:
        return `很抱歉，您的店铺"${storeName}"未通过审核。${reason ? `原因：${reason}` : ''}请修改后重新提交。`;
      default:
        return `您的店铺"${storeName}"审核状态已更新。`;
    }
  }

  /**
   * 发送审核邮件
   */
  private async sendApprovalEmail(store: StoreListItem, decision: StoreVerifiedStatus, reason?: string): Promise<void> {
    try {
      // 这里可以集成邮件服务，如SendGrid、Nodemailer等
      console.log('Sending approval email:', {
        to: store.email,
        storeName: store.name,
        decision: decision,
        reason: reason
      });

      // TODO: 实现实际的邮件发送逻辑
    } catch (error) {
      console.error('Error sending approval email:', error);
    }
  }

  /**
   * 获取待审核店铺列表 - 管理员功能
   */
  async getPendingStores(adminUserId: string): Promise<{
    success: boolean;
    data?: StoreListItem[];
    error?: string;
  }> {
    try {
      // 验证管理员权限
      const adminDoc = await FirestoreService.getById(this.PORTAL_USER_DATA_COLLECTION, adminUserId);
      if (!adminDoc.success || !adminDoc.data) {
        return {
          success: false,
          error: 'Admin user not found'
        };
      }

      const userType = adminDoc.data.userType as UserType;
      if (userType !== UserType.ONENATA_ADMIN) {
        return {
          success: false,
          error: 'Insufficient permissions'
        };
      }

      // 获取待审核的店铺账户
      const accountsResult = await FirestoreService.query(
        this.STORE_ACCOUNT_COLLECTION,
        [
          { field: 'storeVerifiedStatus', operator: '==', value: StoreVerifiedStatus.PENDING },
          { field: 'isValid', operator: '==', value: true }
        ]
      );

      if (!accountsResult.success || !accountsResult.data) {
        return {
          success: true,
          data: []
        };
      }

      // 获取对应的店铺信息
      const stores: StoreListItem[] = [];
      for (const account of accountsResult.data) {
        const infoResult = await FirestoreService.query(
          this.STORE_INFO_COLLECTION,
          [
            { field: 'storeId', operator: '==', value: account.sid },
            { field: 'isValid', operator: '==', value: true }
          ]
        );

        if (infoResult.success && infoResult.data && infoResult.data.length > 0) {
          stores.push({
            ...infoResult.data[0],
            accountInfo: account
          } as StoreListItem);
        }
      }

      return {
        success: true,
        data: stores
      };

    } catch (error) {
      console.error('Error getting pending stores:', error);
      return {
        success: false,
        error: 'Failed to get pending stores'
      };
    }
  }

  /**
   * 获取审核记录
   */
  async getApprovalHistory(storeId: string): Promise<{
    success: boolean;
    data?: any[];
    error?: string;
  }> {
    try {
      const result = await FirestoreService.query(
        'store-approval-records',
        [
          { field: 'storeId', operator: '==', value: storeId },
          { field: 'isValid', operator: '==', value: true }
        ],
        { field: 'createdAt', direction: 'desc' }
      );

      return {
        success: true,
        data: result.data || []
      };

    } catch (error) {
      console.error('Error getting approval history:', error);
      return {
        success: false,
        error: 'Failed to get approval history'
      };
    }
  }

  /**
   * 重新提交审核
   */
  async resubmitForApproval(storeId: string, ownerId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      // 验证所有权
      const storeResult = await this.getStoreDetails(storeId);
      if (!storeResult.success || !storeResult.data) {
        return {
          success: false,
          error: 'Store not found'
        };
      }

      if (storeResult.data.accountInfo.ownerId !== ownerId) {
        return {
          success: false,
          error: 'Unauthorized'
        };
      }

      // 只有被拒绝的店铺才能重新提交
      if (storeResult.data.accountInfo.storeVerifiedStatus !== StoreVerifiedStatus.REJECTED) {
        return {
          success: false,
          error: 'Store is not in rejected status'
        };
      }

      // 更新状态为待审核
      const updateResult = await FirestoreService.update(
        this.STORE_ACCOUNT_COLLECTION,
        storeId,
        {
          storeVerifiedStatus: StoreVerifiedStatus.PENDING,
          updatedBy: ownerId,
          resubmittedAt: new Date()
        }
      );

      if (!updateResult.success) {
        return {
          success: false,
          error: 'Failed to resubmit store'
        };
      }

      return {
        success: true
      };

    } catch (error) {
      console.error('Error resubmitting store:', error);
      return {
        success: false,
        error: 'Failed to resubmit store'
      };
    }
  }
}


export default new StoreService();
