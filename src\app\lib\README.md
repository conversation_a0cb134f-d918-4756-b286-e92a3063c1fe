# OneNata Admin 认证系统

这是一个完整的 Firebase Auth 认证系统，支持邮箱和手机号码注册、登录、验证码验证等功能。

## 🏗️ 架构概览

```
app/lib/
├── types/
│   └── auth.ts          # 认证相关类型定义
├── services/
│   ├── auth.service.ts  # 认证服务
│   ├── verification.service.ts  # 验证码服务
│   ├── user.service.ts  # 用户管理服务
│   └── index.ts         # 服务导出
├── controllers/
│   ├── auth.controller.ts  # 认证控制器
│   ├── user.controller.ts  # 用户控制器
│   └── index.ts            # 控制器导出
└── models/
    ├── portal-user.ts   # 用户模型
    └── types.ts         # 通用类型
```

## 🔐 核心功能

### 1. 用户注册
- 支持邮箱和手机号码注册
- 密码强度验证
- 邀请码验证
- 自动发送验证码
- 密码加盐加密存储

### 2. 用户登录
- 邮箱/手机号码登录
- 密码验证
- 会话管理
- 登录记录

### 3. 验证码系统
- 邮箱验证码
- 短信验证码
- 验证码过期管理
- 防重复发送
- 尝试次数限制

### 4. 密码管理
- 忘记密码
- 重置密码
- 更改密码
- 密码强度要求

## 📡 API 端点

### 认证相关

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "phoneNumber": "+1234567890",
  "password": "StrongPassword123!",
  "firstName": "张",
  "lastName": "三",
  "displayName": "张三",
  "authChannel": "EMAIL",
  "invitationCode": "INVITE123"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "StrongPassword123!",
  "authChannel": "EMAIL"
}
```

#### 验证码验证
```http
POST /api/auth/verify-code
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "type": "email"
}
```

#### 发送验证码
```http
POST /api/auth/send-verification-code
Content-Type: application/json

{
  "email": "<EMAIL>",
  "type": "email"
}
```

#### 忘记密码
```http
POST /api/auth/forgot-password
Content-Type: application/json

{
  "email": "<EMAIL>",
  "authChannel": "EMAIL"
}
```

#### 重置密码
```http
POST /api/auth/reset-password
Content-Type: application/json

{
  "token": "reset-token",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

#### 更改密码
```http
POST /api/auth/change-password
Content-Type: application/json
Authorization: Bearer <token>

{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword123!",
  "confirmPassword": "NewPassword123!"
}
```

### 用户管理

#### 获取用户列表
```http
GET /api/users?page=1&limit=20&userType=PETSTORE_STAFF&verified=true
```

#### 获取用户详情
```http
GET /api/users/{uid}
```

#### 更新用户信息
```http
PUT /api/users/{uid}
Content-Type: application/json

{
  "displayName": "新名称",
  "firstName": "新",
  "lastName": "名称",
  "bio": "个人简介",
  "phoneNumber": "+1234567890"
}
```

#### 删除用户
```http
DELETE /api/users/{uid}
```

## 🔧 使用示例

### 1. 在 Next.js API 路由中使用

```typescript
// app/api/auth/register/route.ts
import { NextRequest } from 'next/server';
import { AuthController } from '@/app/lib/controllers';

const authController = new AuthController();

export async function POST(request: NextRequest) {
  return await authController.register(request);
}
```

### 2. 在服务端组件中使用

```typescript
import { AuthService, UserService } from '@/app/lib/services';

export default async function UserProfile({ uid }: { uid: string }) {
  const userService = new UserService();
  const userData = await userService.getUserData(uid);
  
  return (
    <div>
      <h1>{userData?.displayName}</h1>
      <p>{userData?.bio}</p>
    </div>
  );
}
```

### 3. 在客户端中使用

```typescript
// 注册用户
const registerUser = async (userData: RegisterRequest) => {
  const response = await fetch('/api/auth/register', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(userData),
  });
  
  return await response.json();
};

// 验证验证码
const verifyCode = async (email: string, code: string) => {
  const response = await fetch('/api/auth/verify-code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email,
      code,
      type: 'email'
    }),
  });
  
  return await response.json();
};
```

## 🗄️ 数据库结构

### Firestore Collections

#### user-account
```typescript
{
  sid: string;           // 用户唯一标识符 (UUID)
  fid: string;           // Firebase User ID
  email?: string;        // 邮箱
  phoneNumber?: string;  // 手机号码
  salt: string;          // 密码盐值
  hashedCredential: string; // 加密后的密码
  isEmailVerified: boolean; // 邮箱验证状态
  isValid: boolean;      // 账户有效状态
  isSynced: boolean;     // 同步状态
}
```

#### portal-user-data
```typescript
{
  sid: string;           // 数据唯一标识符
  fid: string;           // Firebase User ID
  uid: string;           // 关联的 user-account sid
  displayName: string;   // 显示名称
  firstName: string;     // 名
  lastName: string;      // 姓
  userType: UserType;    // 用户类型
  phoneNumber?: string;  // 手机号码
  bio?: string;          // 个人简介
  photoURL?: string;     // 头像URL
  invitationCode?: string; // 邀请码
}
```

#### verification-codes
```typescript
{
  id: string;            // 验证码记录ID
  email?: string;        // 邮箱
  phoneNumber?: string;  // 手机号码
  code: string;          // 验证码
  type: VerificationType; // 验证类型
  status: VerificationStatus; // 验证状态
  expiresAt: Date;       // 过期时间
  createdAt: Date;       // 创建时间
  attempts: number;      // 尝试次数
  maxAttempts: number;   // 最大尝试次数
}
```

## ⚙️ 配置

### 环境变量

```env
# Firebase 配置
NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-auth-domain
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-storage-bucket
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id

# 邮件服务配置 (可选)
SENDGRID_API_KEY=your-sendgrid-key
AWS_SES_ACCESS_KEY=your-aws-access-key
AWS_SES_SECRET_KEY=your-aws-secret-key

# 短信服务配置 (可选)
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
AWS_SNS_ACCESS_KEY=your-aws-access-key
AWS_SNS_SECRET_KEY=your-aws-secret-key
```

### 认证配置

```typescript
const authConfig = {
  passwordRequirements: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true
  },
  verificationCodeLength: 6,
  verificationCodeExpiry: 15, // 分钟
  maxVerificationAttempts: 5,
  lockoutDuration: 30, // 分钟
  sessionTimeout: 1440 // 分钟 (24小时)
};
```

## 🔒 安全特性

1. **密码安全**
   - 密码强度验证
   - 加盐哈希存储
   - 防彩虹表攻击

2. **验证码安全**
   - 时间限制 (15分钟过期)
   - 尝试次数限制 (最多5次)
   - 防重复发送

3. **会话管理**
   - JWT Token 认证
   - 会话过期检查
   - 自动登出

4. **输入验证**
   - 邮箱格式验证
   - 手机号码格式验证
   - SQL 注入防护

## 🚀 部署说明

1. **Firebase 设置**
   ```bash
   npm install firebase
   ```

2. **初始化 Firebase**
   ```typescript
   // firebase.config.ts
   import { initializeApp } from 'firebase/app';
   import { getAuth } from 'firebase/auth';
   import { getFirestore } from 'firebase/firestore';

   const firebaseConfig = {
     // 你的 Firebase 配置
   };

   const app = initializeApp(firebaseConfig);
   export const auth = getAuth(app);
   export const db = getFirestore(app);
   ```

3. **Firestore 安全规则**
   ```javascript
   rules_version = '2';
   service cloud.firestore {
     match /databases/{database}/documents {
       match /user-account/{userId} {
         allow read, write: if request.auth != null && request.auth.uid == userId;
       }
       
       match /portal-user-data/{docId} {
         allow read, write: if request.auth != null;
       }
       
       match /verification-codes/{docId} {
         allow read, write: if request.auth != null;
       }
     }
   }
   ```

## 🧪 测试

### 单元测试示例

```typescript
import { AuthService } from '@/app/lib/services';
import { AuthChannel } from '@/app/lib/models/types';

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    authService = new AuthService();
  });

  test('should register user successfully', async () => {
    const registerRequest = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: '测试',
      lastName: '用户',
      authChannel: AuthChannel.EMAIL
    };

    const result = await authService.register(registerRequest);
    expect(result.success).toBe(true);
    expect(result.data?.uid).toBeDefined();
  });
});
```

## 📝 注意事项

1. **生产环境**
   - 请配置真实的邮件和短信服务
   - 设置正确的 Firebase 安全规则
   - 启用 HTTPS

2. **性能优化**
   - 使用 Firebase 索引
   - 实现缓存机制
   - 优化查询

3. **监控和日志**
   - 集成错误监控
   - 记录关键操作日志
   - 设置告警机制

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个认证系统！ 