@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

:root.dark {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* 保持原有的媒体查询作为后备 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #ffffff;
    --foreground: #171717;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* 深色主题保持与浅色主题相同的样式 */
.dark {
  /* 不做任何颜色覆盖，保持原有的浅色主题样式 */
}

/* 媒体查询作为后备方案 - 也保持浅色主题样式 */
@media (prefers-color-scheme: dark) {
  /* 不做任何颜色覆盖，保持原有的浅色主题样式 */
}
